
version: '3.8'

services:
  # API Server
  boatbook-api:
    build:
      context: ./server
      dockerfile: Dockerfile
    image: boatbook-api:latest
    container_name: boatbook-api
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - FRONTEND_URL=http://localhost:8080
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Web Server
  boatbook-web:
    build:
      context: .
      dockerfile: Dockerfile
    image: boatbook:latest
    container_name: boatbook-app
    ports:
      - "8080:80"
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=http://localhost:3001/api
    depends_on:
      - boatbook-api
    restart: unless-stopped
