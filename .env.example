# BoatBook Environment Configuration
# Copy this file to .env and update the values as needed

# Application Configuration
VITE_APP_TITLE=BoatBook
NODE_ENV=development

# Development Configuration
VITE_DEV_PORT=8080
VITE_DEV_HOST=localhost

# API Configuration (if using external APIs)
# VITE_API_BASE_URL=https://api.example.com
# VITE_API_KEY=your-api-key-here

# Feature Flags
VITE_ENABLE_DEBUG=false
VITE_ENABLE_ANALYTICS=false

# Build Configuration
VITE_BUILD_SOURCEMAP=false
