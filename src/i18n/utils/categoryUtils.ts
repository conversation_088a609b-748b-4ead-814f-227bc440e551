
import { <PERSON><PERSON>ey } from '../types';
import { AuthTranslationKey } from '../types/auth';
import { CommonTranslationKey } from '../types/common';
import { JauntsTranslationKey } from '../types/jaunts';
import { MaintenanceTranslationKey } from '../types/maintenance';
import { NavigationTranslationKey } from '../types/navigation';
import { SettingsTranslationKey } from '../types/settings';
import { TeamsTranslationKey } from '../types/teams';
import { WatercraftTranslationKey } from '../types/watercraft';
import { UserRoleTranslationKey } from '../types/userRole';

/**
 * A utility function that categorizes a translation key based on its type.
 * This helps to identify which category a key belongs to.
 * 
 * @param key The translation key to categorize
 * @returns The category the key belongs to, or 'unknown' if it can't be determined
 */
export function getCategoryForKey(key: TranslationKey): string {
  // Use type checking instead of empty objects to improve performance
  if (key.startsWith('login_') || key.includes('sign_in') || key.includes('password')) return 'auth';
  if (key.startsWith('my_jaunts') || key.includes('jaunts') || key.includes('distance')) return 'jaunts';
  if (key.startsWith('my_maintenance') || key.includes('maintenance') || key.includes('repair')) return 'maintenance';
  if (key.startsWith('nav_') || key.includes('navigation') || key.includes('dashboard')) return 'navigation';
  if (key.startsWith('settings_') || key.includes('profile') || key.includes('account')) return 'settings';
  if (key.startsWith('my_teams') || key.includes('team_') || key.includes('invite_member')) return 'teams';
  if (key.startsWith('filter_watercraft') || key.includes('boat_') || key.includes('checkout')) return 'watercraft';
  if (key.startsWith('role') || key.includes('admin_') || key.includes('user_created')) return 'userRole';
  
  // Default to common for general terms
  return 'common';
}

/**
 * Groups all translation keys by their categories for better organization
 * 
 * @returns An object mapping category names to arrays of translation keys
 */
export function getAllKeysByCategory(): Record<string, TranslationKey[]> {
  const categories: Record<string, TranslationKey[]> = {
    auth: [],
    common: [],
    jaunts: [],
    maintenance: [],
    navigation: [],
    settings: [],
    teams: [],
    watercraft: [],
    userRole: [],
    unknown: []
  };

  // Get all keys from all languages and categorize them
  const { getAllTranslationKeys } = require('../translations');
  const allKeys = getAllTranslationKeys();
  
  for (const key of allKeys) {
    const category = getCategoryForKey(key);
    categories[category].push(key);
  }

  return categories;
}

/**
 * Checks if a translation key exists in the given category
 * 
 * @param key The translation key to check
 * @param category The category to check against
 * @returns True if the key belongs to the specified category, false otherwise
 */
export function isKeyInCategory(key: TranslationKey, category: string): boolean {
  return getCategoryForKey(key) === category;
}
