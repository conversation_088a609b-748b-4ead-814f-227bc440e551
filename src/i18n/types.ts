
// This file is maintained for backward compatibility
// Import and re-export everything from the refactored files
import { TranslationKey, TranslationsMap } from './types/index';
import { getCategoryForKey, getAllKeysByCategory, isKeyInCategory } from './utils/categoryUtils';

export type { TranslationKey, TranslationsMap };
export { getCategoryForKey, getAllKeysByCategory, isKeyInCategory };

// Re-export all the individual translation key types for backward compatibility
export * from './types/auth';
export * from './types/common';
export * from './types/jaunts';
export * from './types/maintenance';
export * from './types/navigation';
export * from './types/settings';
export * from './types/teams';
export * from './types/watercraft';
export * from './types/userRole';
