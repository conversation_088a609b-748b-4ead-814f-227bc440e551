
export const maintenanceTranslations = {
  my_maintenance_requests: "Mis Solicitudes de Mantenimiento",
  view_maintenance_requests: "Ver y gestionar solicitudes de mantenimiento para tus embarcaciones",
  no_maintenance_requests: "No se encontraron solicitudes de mantenimiento",
  request_maintenance: "Solicitar Mantenimiento",
  maintenance_type: "Tipo de Mantenimiento",
  priority: "Prioridad",
  request_date: "Fecha de Solicitud",
  resolution_status: "Estado de Resolución",
  assigned_to: "Asignado a",
  new_maintenance_request: "Nueva Solicitud de Mantenimiento",
  select_watercraft: "Seleccionar Embarcación",
  select_maintenance_type: "Seleccionar Tipo de Mantenimiento",
  low: "Baja",
  medium: "Media",
  high: "Alta",
  repair: "Reparación",
  inspection: "Inspección",
  cleaning: "Limpieza",
  replacement: "Reemplazo",
  unassigned: "Sin Asignar",
  open: "Abierta",
  in_progress: "En Progreso",
  completed: "Completada",
  rejected: "<PERSON>chazad<PERSON>",
  closed: "Cerra<PERSON>",
  submit_request: "Enviar Solicitud",
  error_loading_maintenance: "Error al cargar las solicitudes de mantenimiento",
  description: "Descripción",
  resolution: "Resolución",
  change: "Cambiar",
  refresh: "Actualizar",
  expand: "Expandir",
  fill_all_fields: "Por favor, complete todos los campos",
  maintenance_request_created: "Solicitud de mantenimiento creada con éxito",
  manage_maintenance_requests: "Gestionar solicitudes de mantenimiento",
  issue_type: "Tipo de problema",
  select_issue_type: "Seleccionar tipo de problema",
  broken: "Roto",
  damaged: "Dañado",
  missing_part: "Pieza faltante",
  watercraft_state: "Estado de la embarcación",
  select_watercraft_state: "Seleccionar estado de la embarcación",
  useable: "Utilizable",
  unuseable: "No utilizable",
  describe_issue: "Describir el problema",
  all_requests: "Todas las solicitudes",
  resolved_date: "Fecha de resolución",
  "in-progress": "En progreso",
  maintenance_title: "Título de mantenimiento",
  maintenance_description: "Descripción de mantenimiento"
};
