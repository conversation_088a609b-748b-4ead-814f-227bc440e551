
export const weatherTranslations = {
  weather_title: "Wetter",
  weather_station: "Wetterstation",
  weather_temperature: "Temperatur",
  weather_water_temp: "Wassertemp.",
  weather_wind_speed: "Wind",
  weather_wind_gust: "Böen",
  weather_wind_direction: "Richtung",
  weather_tide_level: "Gezeiten",
  weather_tide: "Gezeiten", // Add missing translation
  weather_lightning: "Blitz",
  weather_lightning_warning: "Blitzwarnung", // Add missing translation
  weather_search_placeholder: "Nach einer Wetterstation suchen...",
  weather_no_station_selected: "Keine Wetterstation ausgewählt",
  weather_loading: "Wetterdaten werden geladen...",
  weather_error: "Fehler beim Laden der Wetterdaten",
  weather_hourly_forecast: "Stündliche Vorhersage",
  weather_station_saved: "Wetterstation gespeichert",
  weather_unit_saved: "Wettereinheit gespeichert", // Add missing translation
  weather_station_setting: "Wetterstation",
  weather_station_setting_description: "Wählen Sie Ihre bevorzugt<PERSON> Wetterstation",
  weather_unit_setting: "Wettereinheiten",
  weather_unit_metric: "Metrisch (°C, km/h)",
  weather_unit_imperial: "Imperial (°F, mph)",
  weather_not_available: "N/V",
  weather_no_results: "Keine Stationen gefunden",
  weather_no_forecast_available: "Keine zukünftige Vorhersage verfügbar",
  weather_forecast_not_available: "Vorhersagedaten nicht verfügbar", // Add missing translation
  weather_now: "Jetzt", // Add missing translation
  
  // Settings related translations
  settings_weather: "Wettereinstellungen",
  settings_weather_description: "Konfigurieren Sie Ihre Wettereinstellungen",
  settings_preferred_station: "Bevorzugte Wetterstation",
  settings_unit_system: "Maßsystem",
  settings_metric: "Metrisch (°C, km/h)",
  settings_imperial: "Imperial (°F, mph)",
};
