
export const weatherTranslations = {
  weather_title: "Météo",
  weather_station_setting: "Station météo",
  weather_station_setting_description: "Sélectionnez votre station météo préférée",
  weather_no_station_selected: "Aucune station sélectionnée",
  weather_station_saved: "Station météo enregistrée",
  weather_unit_saved: "Unité météo enregistrée",
  weather_loading: "Chargement des données météo...",
  weather_error: "Erreur lors du chargement des données météo",
  weather_refresh: "Rafraîchir les données",
  weather_temperature: "Température",
  weather_wind: "Vent",
  weather_wind_speed: "Vitesse du vent",
  weather_wind_direction: "Direction du vent",
  weather_wind_gust: "Rafale",
  weather_air_temperature: "Température de l'air",
  weather_water_temperature: "Température de l'eau",
  weather_tide_level: "Niveau de marée",
  weather_lightning: "Foudre détectée",
  weather_not_available: "N/A",
  weather_hourly_forecast: "Prévisions horaires",
  weather_no_forecast_available: "Aucune prévision disponible",
  weather_expand_forecast: "Afficher les prévisions",
  weather_collapse_forecast: "Masquer les prévisions",
  weather_unit_setting: "Unités de mesure",
  weather_unit_metric: "Métrique (°C, m/s)",
  weather_unit_imperial: "Impérial (°F, mph)",
  weather_now: "Maintenant",
  search_stations: "Rechercher des stations",
  search_by_name_or_id: "Rechercher par nom ou ID",
  no_stations_found: "Aucune station trouvée",
  nearby_stations: "Stations à proximité",
  distance: "Distance",
  current_location: "Utiliser ma position",
  location_access_denied: "Accès à la localisation refusé",
  location_error: "Erreur de localisation",
  fetching_location: "Récupération de votre position...",
  weather_north: "N",
  weather_northeast: "NE",
  weather_east: "E",
  weather_southeast: "SE",
  weather_south: "S",
  weather_southwest: "SO", 
  weather_west: "O",
  weather_northwest: "NO"
};
