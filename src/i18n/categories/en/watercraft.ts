export const watercraftTranslations = {
  filter_watercraft: "Filter Watercrafts",
  skill_level: "Skill Level",
  weight_range: "Weight Range",
  showing_favorites_only: "Showing favorites only",
  show_favorites_only: "Show favorites only",
  book: "Book",
  maintain: "Maintain",
  search_watercraft: "Search watercraft...",
  watercraft_type: "Watercraft Type",
  boat_type: "Boat Type",
  status_available: "Available",
  status_booked: "Booked",
  status_maintenance: "In Maintenance",
  status_out: "Out",
  checkout_watercraft: "Checkout Watercraft",
  watercraft: "Watercraft",
  boat: "Boat",
  kayak: "Kayak",
  surfski: "Surfski",
  PB: "Paddle Board",
  launch: "Launch",
  coastal: "Coastal",
  start_time: "Start Time",
  planned_end_time: "Planned End Time",
  comment_placeholder: "Add any comments or notes here...",
  confirm_checkout: "Confirm Checkout",
  checkout: "Checkout",
  unavailable: "Unavailable",
  ownership: "Ownership",
  specifications: "Specifications",
  current_location: "Current Location",
  location_map_placeholder: "Map view coming soon",
  usage_history: "Usage History",
  skill_level_required: "Skill Level Required",
  capacity: "Capacity",
  length: "Length",
  available: "Available",
  "in-use": "In Use",
  maintenance: "Maintenance",
  regatta: "Regatta",
  skills_and_certifications: "Skills & Certifications",
  skills: "Skills",
  watercraft_permissions: "Watercraft Permissions",
  member: "Member",
  coach: "Coach",
  admin: "Admin",
  private: "Private",
  club: "Club",
  rental: "Rental",
  no_watercrafts_found: "No watercrafts found",
  try_adjusting_filters: "Try adjusting your filters or search criteria",
  loading_watercrafts: "Loading watercrafts...",
  status_in_use: "In Use",
  status_regatta: "At Regatta",
  
  add_watercraft: "Add Watercraft",
  edit_watercraft: "Edit Watercraft",
  add_watercraft_description: "Fill out the form to add a new watercraft to the system",
  edit_watercraft_description: "Update the information for this watercraft",
  watercraft_updated: "Watercraft updated successfully",
  watercraft_created: "Watercraft created successfully",
  watercraft_error: "An error occurred while saving the watercraft",
  select_type: "Select type",
  select_boat_type: "Select boat type",
  select_ownership: "Select ownership type",
  select_skill_level: "Select skill level",
  select_status: "Select status",
  min_weight: "Minimum Weight",
  max_weight: "Maximum Weight",
  location: "Location",
  name: "Name",
  update: "Update",
  create: "Create",
  beginner: "Beginner",
  intermediate: "Intermediate",
  advanced: "Advanced",
  
  club_owned: "Club owned",
  member_owned: "Member owned",
  watercraft_status_updated: "Watercraft status updated successfully",
  watercraft_deleted: "Watercraft deleted successfully",
  manage_watercraft: "Manage Watercraft",
  actions: "Actions",
  change_status: "Change Status",
  mark_available: "Mark as Available",
  mark_in_use: "Mark as In Use",
  mark_maintenance: "Mark as In Maintenance",
  delete_watercraft: "Delete Watercraft",
  no_watercraft_found: "No watercraft found",
  type: "Type",
  watercraft_image_url: "Image URL",
  details: "Details",
  status: "Status",
  must_login_to_favorite: "You must be logged in to favorite watercrafts",
  removed_from_favorites: "Removed from favorites",
  added_to_favorites: "Added to favorites",
  toggle_favorite: "Toggle favorite",
};
