
export const teamsTranslations = {
  my_teams: "My Teams",
  view_teams: "View your rowing teams",
  team_members: "Team Members",
  no_teams: "You aren't part of any teams yet",
  team_role: "Team Role",
  join_date: "Join Date",
  team_admin: "Team Admin",
  team_coach: "Team Coach",
  team_member: "Team Member",
  team_details: "Team Details",
  invite_member: "Invite Member",
  leave_team: "Leave Team",
  create_team: "Create Team",
  error_loading_teams: "Error loading teams",
  teams: "Teams",
  manage_teams: "Manage Teams",
  no_teams_yet: "You don't have any teams yet",
  create_your_first_team: "Create your first team",
  no_other_teams: "No other teams available",
  team_name_email_required: "Team name and email are required",
  team_created_successfully: "Team created successfully",
  manage_your_rowing_teams: "Manage your rowing teams",
  other_teams: "Other Teams",
  all_teams: "All Teams",
  table_view: "Table View",
  create_new_team: "Create New Team",
  team_name: "Team Name",
  enter_team_name: "Enter team name",
  team_email: "Team Email",
  enter_team_email: "Enter team email",
  practice_signup_sheet_url: "Practice Signup Sheet URL",
  failed_to_copy: "Failed to copy to clipboard",
  email_copied: "Email copied to clipboard",
  whatsapp_group: "WhatsApp Group",
  practice_signup_sheet: "Practice Signup Sheet",
  url_copied: "URL copied to clipboard",
  open_sheet: "Open Sheet",
  manage: "Manage",
  members: "members",
  change: "Change",
  refresh: "Refresh",
  expand: "Expand",
  coach: "coach",
  coaches: "coaches",
  practices: "practices",
  no_upcoming_practices: "No upcoming practices scheduled",
  upcoming_practices: "Upcoming Practices",
  attending: "Attending",
  not_attending: "Not Attending",
  tentative: "Tentative",
  add_member: "Add Member",
  search_members: "Search Members",
  search_team_members: "Search Team Members",
  no_members_found: "No members found",
  member_added: "Member added",
  member_removed: "Member removed",
  remove_member: "Remove Member",
  cannot_remove_last_member: "Cannot remove the last member",
  user_already_in_team: "User is already in this team",
  team_coaches: "Team Coaches",
  team_coaches_description: "Manage the coaches assigned to this team",
  no_coaches_assigned: "No coaches assigned",
  assign_coach: "Assign Coach",
  search_coaches: "Search Coaches",
  no_coaches_found: "No coaches found",
  coach_assigned: "Coach assigned",
  coach_removed: "Coach removed",
  remove_coach: "Remove Coach",
  practice_schedule: "Practice Schedule",
  team_practice_schedule_description: "Manage the practice schedule for this team",
  no_practice_schedule: "No practice schedule set",
  add_practice: "Add Practice",
  edit_practice: "Edit Practice",
  day: "Day",
  select_day: "Select Day",
  start_time: "Start Time",
  end_time: "End Time",
  location: "Location",
  optional: "Optional",
  enter_practice_location: "Enter practice location",
  notes: "Notes",
  enter_practice_notes: "Enter practice notes",
  cancel: "Cancel",
  update: "Update",
  add: "Add",
  time: "Time",
  actions: "Actions",
  practice_schedule_required_fields: "Day, start time, and end time are required",
  practice_updated: "Practice updated",
  practice_added: "Practice added",
  practice_deleted: "Practice deleted",
  monday: "Monday",
  tuesday: "Tuesday",
  wednesday: "Wednesday",
  thursday: "Thursday",
  friday: "Friday",
  saturday: "Saturday",
  sunday: "Sunday",
  loading: "Loading",
  error_loading_coaches: "Error loading coaches",
  team_updated: "Team updated",
  team_updated_successfully: "Team updated successfully",
  error_updating_team: "Error updating team",
  feature_disabled: "Feature disabled",
  member_sign_up_disabled_description: "The member sign-up feature is currently disabled",
  practice_sign_up: "Practice Sign-up",
  lineup_management: "Lineup Management",
  lineups: "Lineups",
  update_status: "Update Status",
  no_practices_on_this_date: "No practices on this date",
  practice_sign_up_success: "Signed up for practice successfully",
  practice_sign_out_success: "Signed out from practice successfully",
  practice_tentative_success: "Status updated to tentative",
  select_status: "Select Status",
  attendees: "Attendees",
  members_attending_this_practice: "Members attending this practice",
  no_members_attending: "No members attending",
  assign_boats_and_positions: "Assign Boats and Positions",
  add_boat: "Add Boat",
  select_boat: "Select Boat",
  seats: "Seats",
  assign_crew: "Assign Crew",
  position: "Position",
  crew_member: "Crew Member",
  coxswain: "Coxswain",
  seat: "Seat",
  save_lineup: "Save Lineup",
  unknown_boat: "Unknown Boat",
  unknown_member: "Unknown Member",
  no_lineups_created: "No lineups created",
  create_first_lineup: "Create First Lineup",
  lineup_saved: "Lineup saved",
  lineup_deleted: "Lineup deleted",
  select_practice_date: "Select Practice Date",
  select_date_to_manage_lineups: "Select a date to manage lineups",
  practice_selection: "Practice Selection",
  please_select_date: "Please select a date",
  select_member: "Select Member",
  unassigned: "Unassigned",
  edit: "Edit",
  delete: "Delete",
  my_lineups: "My Lineups",
  my_assigned_lineups: "My Assigned Lineups",
  practice_locked: "Practice locked",
  practice_signup_closed: "Practice signup is closed",
  lock_signup: "Lock signup",
  select_practice_first: "Please select a practice first",
  missing_boat_selection: "Please select a boat",
  assign_at_least_one_crew_member: "Please assign at least one crew member",
  practice_unlocked: "Practice signup is now open",
  login_required: "Login required",
  practice_not_found: "Practice not found",
  error_updating_signup: "Error updating signup",
  error_locking_practice: "Error locking practice",
  error_unlocking_practice: "Error unlocking practice",
  // New team selector related keys
  select_team: "Select Team",
  search_teams: "Search Teams",
  no_teams_found: "No teams found",
  // Adding the new translation keys used in UpcomingPracticeList
  practice_signup_description: "Sign up for upcoming team practices",
  locked: "Locked",
  you_are_attending: "You are attending",
  you_are_not_attending: "You are not attending",
  you_did_not_respond: "You did not respond",
  no_upcoming_practices_for_month: "No upcoming practices for this month",
  // New keys for batch selection
  select_all: "Select All",
  unselect_all: "Unselect All",
  // New keys for batch selection status messages
  signed_up_for_all_practices: "Signed up for {{count}} practices",
  signed_out_from_all_practices: "Signed out from {{count}} practices",
  already_signed_up_for_all: "Already signed up for all practices",
  already_signed_out_from_all: "Already signed out from all practices"
};
