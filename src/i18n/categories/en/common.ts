
export const commonTranslations = {
  // App
  app_name: "Aqua Journey Keeper",
  welcome: "Welcome",
  loading: "Loading...",
  coming_soon: "Coming Soon",
  unknown: "Unknown",
  yes: "Yes",
  no: "No",
  save: "Save",
  cancel: "Cancel",
  delete: "Delete",
  edit: "Edit",
  search: "Search",
  create: "Create",
  actions: "Actions",
  view: "View",
  manage: "Manage",
  details: "Details",
  back: "Back",
  next: "Next",
  submit: "Submit",
  continue: "Continue",
  optional: "optional",
  required: "required",
  preview: "Preview",
  notification: "Notification",
  notifications: "Notifications",
  settings: "Settings",
  
  // User roles
  admin: "Admin",
  coach: "Coach",
  member: "Member",
  
  // Success messages
  changes_saved: "Changes saved successfully",
  success_message: "Operation completed successfully",
  
  // Error messages
  error_message: "An error occurred",
  error_try_again: "Please try again later",
  no_results_found: "No results found",
  
  // Admin panel translations
  admin_dashboard: "Admin Dashboard",
  admin_overview: "Overview",
  admin_overview_description: "Key metrics and statistics for your organization",
  admin_users: "Users",
  admin_watercraft: "Watercraft",
  admin_teams: "Teams",
  manage_users: "Manage Users",
  manage_watercraft: "Manage Watercraft",
  manage_teams: "Manage Teams",
  search_users: "Search users...",
  search_watercraft: "Search watercraft...",
  search_teams: "Search teams...",
  no_users_found: "No users found",
  no_watercraft_found: "No watercraft found",
  no_teams_found: "No teams found",
  
  // Admin stats
  total_users: "Total Users",
  total_watercraft: "Total Watercraft",
  maintenance_requests: "Maintenance Requests",
  active_jaunts: "Active Jaunts",
  
  // Admin actions
  user_role_updated: "User role updated successfully",
  user_deleted: "User deleted successfully",
  watercraft_status_updated: "Watercraft status updated successfully",
  watercraft_deleted: "Watercraft deleted successfully",
  team_deleted: "Team deleted successfully",
  
  // Admin user management
  user: "User",
  role: "Role",
  skills: "Skills",
  change_role: "Change Role",
  make_admin: "Make Admin",
  make_coach: "Make Coach",
  make_member: "Make Member",
  delete_user: "Delete User",
  
  // Admin watercraft management
  watercraft: "Watercraft",
  type: "Type",
  ownership: "Ownership",
  status: "Status",
  club_owned: "Club Owned",
  member_owned: "Member Owned",
  mark_available: "Mark as Available",
  mark_in_use: "Mark as In Use",
  mark_maintenance: "Mark as In Maintenance",
  delete_watercraft: "Delete Watercraft",
  
  // Admin team management
  team_name: "Team Name",
  members: "Members",
  contact: "Contact",
  resources: "Resources",
  edit_team: "Edit Team",
  manage_members: "Manage Members",
  no_resources: "No resources",
  practice_sheet: "Practice Sheet",
  
  // Misc
  open_menu: "Open Menu"
};
