
export const weatherTranslations = {
  weather_title: "Weather",
  weather_station: "Weather Station",
  weather_temperature: "Temperature",
  weather_water_temp: "Water Temp",
  weather_wind_speed: "Wind",
  weather_wind_gust: "Gust",
  weather_wind_direction: "Direction",
  weather_tide_level: "Tide",
  weather_tide: "Tide", // Add missing translation
  weather_lightning: "Lightning",
  weather_lightning_warning: "Lightning Warning", // Add missing translation
  weather_search_placeholder: "Search for a weather station...",
  weather_no_station_selected: "No weather station selected",
  weather_loading: "Loading weather data...",
  weather_error: "Error loading weather data",
  weather_hourly_forecast: "Hourly Forecast",
  weather_station_saved: "Weather station saved",
  weather_unit_saved: "Weather unit saved",
  weather_station_setting: "Weather Station",
  weather_station_setting_description: "Choose your preferred weather station",
  weather_unit_setting: "Weather Units",
  weather_unit_metric: "Metric (°C, km/h)",
  weather_unit_imperial: "Imperial (°F, mph)",
  weather_not_available: "N/A",
  weather_no_results: "No stations found",
  weather_no_forecast_available: "No future forecast available",
  weather_forecast_not_available: "Forecast data not available", // Add missing translation
  weather_now: "Now",
  
  // Settings related translations
  settings_weather: "Weather Settings",
  settings_weather_description: "Configure your weather preferences",
  settings_preferred_station: "Preferred Weather Station",
  settings_unit_system: "Measurement System",
  settings_metric: "Metric (°C, km/h)",
  settings_imperial: "Imperial (°F, mph)",
};
