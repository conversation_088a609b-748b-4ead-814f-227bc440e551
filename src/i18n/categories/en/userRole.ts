
export const userRoleTranslations = {
  role: "Role",
  skills: "Skills",
  contact: "Contact",
  resources: "Resources",
  open_menu: "Open Menu",
  change_role: "Change Role",
  make_admin: "Make Admin",
  make_coach: "Make Coach",
  make_member: "Make Member",
  delete_user: "Delete User",
  no_users_found: "No Users Found",
  user_deleted: "User Deleted",
  user_role_updated: "User Role Updated",
  manage_users: "Manage Users",
  search_users: "Search Users",
  team_deleted: "Team Deleted",
  manage_teams: "Manage Teams",
  search_teams: "Search Teams",
  edit_team: "Edit Team",
  manage_members: "Manage Members",
  delete_team: "Delete Team",
  no_teams_found: "No Teams Found",
  practice_sheet: "Practice Sheet",
  no_resources: "No Resources",
  club_owned: "Club Owned",
  member_owned: "Member Owned",
  watercraft_status_updated: "Watercraft Status Updated",
  watercraft_deleted: "Watercraft Deleted",
  manage_watercraft: "Manage Watercraft",
  change_status: "Change Status",
  mark_available: "Mark Available",
  mark_in_use: "Mark In Use",
  mark_maintenance: "Mark Maintenance",
  delete_watercraft: "Delete Watercraft",
  no_watercraft_found: "No Watercraft Found",
  admin_dashboard: "Admin Dashboard",
  admin_overview: "Admin Overview",
  admin_overview_description: "Overview of your club's activity and resources",
  admin_users: "Users",
  admin_watercraft: "Watercraft",
  admin_teams: "Teams",
  total_users: "Total Users",
  total_watercraft: "Total Watercraft",
  maintenance_requests: "Maintenance Requests",
  active_jaunts: "Active Jaunts",
  nav_admin: "Admin",
  add_watercraft: "Add Watercraft",
  edit_watercraft: "Edit Watercraft",
  add_watercraft_description: "Add a new watercraft to your fleet",
  edit_watercraft_description: "Edit an existing watercraft",
  watercraft_updated: "Watercraft Updated",
  watercraft_created: "Watercraft Created",
  select_type: "Select Type",
  select_boat_type: "Select Boat Type",
  select_ownership: "Select Ownership",
  select_skill_level: "Select Skill Level",
  select_status: "Select Status",
  select_member: "Select Member",
  min_weight: "Min Weight",
  max_weight: "Max Weight",
  beginner: "Beginner",
  intermediate: "Intermediate",
  advanced: "Advanced",
  missing_part: "Missing Part",
  damaged: "Damaged",
  issue_type: "Issue Type",
  watercraft_state: "Watercraft State",
  resolved_date: "Resolved Date",
  fill_all_fields: "Fill All Fields",
  select_watercraft: "Select Watercraft",
  select_issue_type: "Select Issue Type",
  select_watercraft_state: "Select Watercraft State",
  useable: "Useable",
  unuseable: "Unuseable",
  describe_issue: "Describe Issue",
  submit_request: "Submit Request",
  no_maintenance_requests: "No Maintenance Requests",
  error_loading_maintenance: "Error Loading Maintenance",
  maintenance_request_created: "Maintenance Request Created",
  manage_maintenance_requests: "Manage Maintenance Requests",
  new_maintenance_request: "New Maintenance Request",
  all_requests: "All Requests",
  admin: "Admin",
  coach: "Coach",
  member: "Member",
  create_user: "Create User",
  edit_user: "Edit User",
  create_user_description: "Add a new user to the system",
  edit_user_description: "Modify existing user information",
  select_role: "Select a role",
  select_skills_description: "Select the skills this user has",
  saving: "Saving...",
  user_created: "User created successfully",
  user_updated: "User updated successfully",
  confirm_delete: "Confirm Delete",
  delete_user_confirmation: "Are you sure you want to delete this user? This action cannot be undone."
};
