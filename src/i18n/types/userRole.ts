
export type UserRoleTranslationKey =
  | 'role'
  | 'skills'
  | 'contact'
  | 'resources'
  | 'open_menu'
  | 'change_role'
  | 'make_admin'
  | 'make_coach'
  | 'make_member'
  | 'delete_user'
  | 'no_users_found'
  | 'user_deleted'
  | 'user_role_updated'
  | 'manage_users'
  | 'search_users'
  | 'team_deleted'
  | 'manage_teams'
  | 'search_teams'
  | 'edit_team'
  | 'manage_members'
  | 'delete_team'
  | 'no_teams_found'
  | 'practice_sheet'
  | 'no_resources'
  | 'club_owned'
  | 'member_owned'
  | 'watercraft_status_updated'
  | 'watercraft_deleted'
  | 'manage_watercraft'
  | 'change_status'
  | 'mark_available'
  | 'mark_in_use'
  | 'mark_maintenance'
  | 'delete_watercraft'
  | 'no_watercraft_found'
  | 'admin_dashboard'
  | 'admin_overview'
  | 'admin_overview_description'
  | 'admin_users'
  | 'admin_watercraft'
  | 'admin_teams'
  | 'total_users'
  | 'total_watercraft'
  | 'maintenance_requests'
  | 'active_jaunts'
  | 'nav_admin'
  | 'add_watercraft'
  | 'edit_watercraft'
  | 'add_watercraft_description'
  | 'edit_watercraft_description'
  | 'watercraft_updated'
  | 'watercraft_created'
  | 'select_type'
  | 'select_boat_type'
  | 'select_ownership'
  | 'select_skill_level'
  | 'select_status'
  | 'select_member'
  | 'min_weight'
  | 'max_weight'
  | 'beginner'
  | 'intermediate'
  | 'advanced'
  | 'missing_part'
  | 'damaged'
  | 'issue_type'
  | 'watercraft_state'
  | 'resolved_date'
  | 'fill_all_fields'
  | 'select_watercraft'
  | 'select_issue_type'
  | 'select_watercraft_state'
  | 'useable'
  | 'unuseable'
  | 'describe_issue'
  | 'submit_request'
  | 'no_maintenance_requests'
  | 'error_loading_maintenance'
  | 'maintenance_request_created'
  | 'manage_maintenance_requests'
  | 'new_maintenance_request'
  | 'all_requests'
  | 'admin'
  | 'coach' 
  | 'member'
  // User management
  | 'create_user'
  | 'edit_user'
  | 'create_user_description'
  | 'edit_user_description'
  | 'select_role'
  | 'select_skills_description'
  | 'saving'
  | 'user_created'
  | 'user_updated'
  | 'confirm_delete'
  | 'delete_user_confirmation';
