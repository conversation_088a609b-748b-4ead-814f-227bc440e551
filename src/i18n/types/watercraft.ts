
// This file defines all the valid translation keys for watercraft-related content
export type WatercraftTranslationKey =
  | "filter_watercraft"
  | "book"
  | "maintain"
  | "search_watercraft"
  | "watercraft_type"
  | "boat_type"
  | "status_available"
  | "status_booked"
  | "status_maintenance"
  | "status_out"
  | "checkout_watercraft"
  | "watercraft"
  | "boat"
  | "kayak"
  | "surfski" 
  | "PB"
  | "launch"
  | "coastal"
  | "start_time"
  | "planned_end_time"
  | "comment_placeholder"
  | "confirm_checkout"
  | "current_jaunt"
  | "check_in"
  | "report_issue"
  | "overdue"
  | "remaining"
  | "checked_in_success"
  | "loading_watercrafts"
  | "no_watercrafts_found" 
  | "try_adjusting_filters"
  | "checkout"
  | "unavailable"
  | "ownership"
  | "specifications"
  | "current_location"
  | "location_map_placeholder"
  | "usage_history"
  | "skill_level_required"
  | "capacity"
  | "length"
  | "available"
  | "in-use"
  | "maintenance"
  | "regatta"
  | "skills_and_certifications"
  | "skills"
  | "watercraft_permissions"
  | "member"
  | "coach"
  | "admin"
  | "private"
  | "club"
  | "rental"
  | "no_watercraft_found"
  | "try_adjusting_filters"
  | "loading_watercrafts"
  | "status_in_use"
  | "status_regatta"
  | "add_watercraft"
  | "edit_watercraft"
  | "add_watercraft_description"
  | "edit_watercraft_description"
  | "watercraft_updated"
  | "watercraft_created"
  | "watercraft_error"
  | "select_type"
  | "select_boat_type"
  | "select_ownership"
  | "select_skill_level"
  | "select_status"
  | "min_weight"
  | "max_weight"
  | "location"
  | "name"
  | "update"
  | "create"
  | "beginner"
  | "intermediate"
  | "advanced"
  | "club_owned"
  | "member_owned"
  | "watercraft_status_updated"
  | "watercraft_deleted"
  | "manage_watercraft"
  | "actions"
  | "change_status"
  | "mark_available"
  | "mark_in_use"
  | "mark_maintenance"
  | "delete_watercraft"
  | "type"
  | "no_watercrafts_found"
  | "watercraft_image_url"
  | "skill_level"
  | "weight_range"
  | "showing_favorites_only"
  | "show_favorites_only"
  | "checkin_success"
  | "checkout_success"
  | "issue_report_feature_coming_soon"
  | "Länge"  // This key exists in the German translations, should be normalized to "length"
  | "details"
  | "status"
  | "must_login_to_favorite"
  | "removed_from_favorites"
  | "added_to_favorites"
  | "toggle_favorite";  // Added the missing translation key
