
import { AuthTranslationKey } from './auth';
import { CommonTranslationKey } from './common';
import { JauntsTranslationKey } from './jaunts';
import { MaintenanceTranslationKey } from './maintenance';
import { NavigationTranslationKey } from './navigation';
import { SettingsTranslationKey } from './settings';
import { TeamsTranslationKey } from './teams';
import { WatercraftTranslationKey } from './watercraft';
import { UserRoleTranslationKey } from './userRole';
import { WeatherTranslationKey } from './weather';

export * from './auth';
export * from './common';
export * from './jaunts';
export * from './maintenance';
export * from './navigation';
export * from './settings';
export * from './teams';
export * from './watercraft';
export * from './userRole';
export * from './weather';

export type TranslationsMap = {
  [key: string]: string;
};

export type TranslationKey =
  | AuthTranslationKey
  | CommonTranslationKey
  | JauntsTranslationKey
  | MaintenanceTranslationKey
  | NavigationTranslationKey
  | SettingsTranslationKey
  | TeamsTranslationKey
  | WatercraftTranslationKey
  | UserRoleTranslationKey
  | WeatherTranslationKey;
