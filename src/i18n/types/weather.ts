
export type WeatherTranslationKey =
  | 'weather_title'
  | 'weather_station'
  | 'weather_temperature'
  | 'weather_water_temp'
  | 'weather_wind_speed'
  | 'weather_wind_gust'
  | 'weather_wind_direction'
  | 'weather_tide_level'
  | 'weather_tide'  // Add missing key
  | 'weather_lightning'
  | 'weather_lightning_warning'  // Add missing key
  | 'weather_search_placeholder'
  | 'weather_no_station_selected'
  | 'weather_loading'
  | 'weather_error'
  | 'weather_hourly_forecast'
  | 'weather_station_saved'
  | 'weather_unit_saved'
  | 'weather_station_setting'
  | 'weather_station_setting_description'
  | 'weather_unit_setting'
  | 'weather_unit_metric'
  | 'weather_unit_imperial'
  | 'weather_not_available'
  | 'weather_no_results'
  | 'weather_no_forecast_available'
  | 'weather_forecast_not_available'  // Add missing key
  | 'weather_now'
  | 'settings_weather'
  | 'settings_weather_description'
  | 'settings_preferred_station'
  | 'settings_unit_system'
  | 'settings_metric'
  | 'settings_imperial';
