
import { WeekDay } from '@/types';

export type TeamsTranslationKey =
  | 'my_teams'
  | 'view_teams'
  | 'team_members'
  | 'no_teams'
  | 'team_role'
  | 'join_date'
  | 'team_admin'
  | 'team_coach'
  | 'team_member'
  | 'team_details'
  | 'invite_member'
  | 'leave_team'
  | 'create_team'
  | 'error_loading_teams'
  | 'teams'
  | 'manage_teams'
  | 'no_teams_yet'
  | 'create_your_first_team'
  | 'no_other_teams'
  | 'team_name_email_required'
  | 'team_created_successfully'
  | 'manage_your_rowing_teams'
  | 'other_teams'
  | 'all_teams'
  | 'table_view'
  | 'create_new_team'
  | 'team_name'
  | 'enter_team_name'
  | 'team_email'
  | 'enter_team_email'
  | 'practice_signup_sheet_url'
  | 'failed_to_copy'
  | 'email_copied'
  | 'whatsapp_group'
  | 'practice_signup_sheet'
  | 'url_copied'
  | 'open_sheet'
  | 'manage'
  | 'members'
  | 'change'
  | 'refresh'
  | 'expand'
  | 'coach'
  | 'coaches'
  | 'team_coaches'
  | 'team_coaches_description'
  | 'no_coaches_assigned'
  | 'assign_coach'
  | 'search_coaches'
  | 'no_coaches_found'
  | 'coach_assigned'
  | 'coach_removed'
  | 'remove_coach'
  | 'practices'
  | 'no_upcoming_practices'
  | 'upcoming_practices'
  | 'attending'
  | 'not_attending'
  | 'tentative'
  | 'practice_schedule'
  | 'team_practice_schedule_description'
  | 'no_practice_schedule'
  | 'add_practice'
  | 'edit_practice'
  | 'day'
  | 'select_day'
  | 'start_time'
  | 'end_time'
  | 'location'
  | 'optional'
  | 'enter_practice_location'
  | 'notes'
  | 'enter_practice_notes'
  | 'cancel'
  | 'update'
  | 'add'
  | 'time'
  | 'actions'
  | 'practice_schedule_required_fields'
  | 'practice_updated'
  | 'practice_added'
  | 'practice_deleted'
  | 'monday'
  | 'tuesday'
  | 'wednesday'
  | 'thursday'
  | 'friday'
  | 'saturday'
  | 'sunday'
  | 'loading'
  | 'error_loading_coaches'
  | 'team_updated'
  | 'team_updated_successfully'
  | 'error_updating_team'
  | 'feature_disabled'
  | 'member_sign_up_disabled_description'
  | 'practice_sign_up'
  | 'lineup_management'
  | 'lineups'
  | 'update_status'
  | 'no_practices_on_this_date'
  | 'practice_sign_up_success'
  | 'practice_sign_out_success'
  | 'practice_tentative_success'
  | 'select_status'
  | 'attendees'
  | 'members_attending_this_practice'
  | 'no_members_attending'
  | 'assign_boats_and_positions'
  | 'add_boat'
  | 'select_boat'
  | 'seats'
  | 'assign_crew'
  | 'position'
  | 'crew_member'
  | 'coxswain'
  | 'seat'
  | 'save_lineup'
  | 'unknown_boat'
  | 'unknown_member'
  | 'no_lineups_created'
  | 'create_first_lineup'
  | 'lineup_saved'
  | 'lineup_deleted'
  | 'select_practice_date'
  | 'select_date_to_manage_lineups'
  | 'practice_selection'
  | 'please_select_date'
  | 'select_member'
  | 'unassigned'
  | 'edit'
  | 'delete'
  | 'my_lineups'
  | 'my_assigned_lineups'
  | 'practice_locked'
  | 'practice_signup_closed'
  | 'lock_signup'
  | 'select_practice_first'
  | 'missing_boat_selection'
  | 'assign_at_least_one_crew_member'
  | 'practice_unlocked'
  | 'login_required'
  | 'practice_not_found'
  | 'error_updating_signup'
  | 'error_locking_practice'
  | 'error_unlocking_practice'
  | 'add_member'
  | 'search_members'
  | 'search_team_members'
  | 'no_members_found'
  | 'member_added'
  | 'member_removed'
  | 'remove_member'
  | 'cannot_remove_last_member'
  | 'user_already_in_team'
  | 'select_team'
  | 'search_teams'
  | 'no_teams_found'
  | 'practice_signup_description'
  | 'locked'
  | 'you_are_attending'
  | 'you_are_not_attending'
  | 'you_did_not_respond'
  | 'no_upcoming_practices_for_month'
  | 'select_all'
  | 'unselect_all'
  | 'signed_up_for_all_practices'
  | 'signed_out_from_all_practices'
  | 'already_signed_up_for_all'
  | 'already_signed_out_from_all';
