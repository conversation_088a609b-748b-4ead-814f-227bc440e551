import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import App from '../App';

// Mock the context providers
vi.mock('../context/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div data-testid="auth-provider">{children}</div>,
  useAuth: () => ({
    user: null,
    login: vi.fn(),
    logout: vi.fn(),
    isLoading: false,
  }),
}));

vi.mock('../context/LanguageContext', () => ({
  LanguageProvider: ({ children }: { children: React.ReactNode }) => <div data-testid="language-provider">{children}</div>,
  useLanguage: () => ({
    language: 'en',
    setLanguage: vi.fn(),
    t: (key: string) => key,
  }),
}));

vi.mock('../context/ConfigContext', () => ({
  ConfigProvider: ({ children }: { children: React.ReactNode }) => <div data-testid="config-provider">{children}</div>,
  useConfig: () => ({
    config: {},
    updateConfig: vi.fn(),
  }),
}));

vi.mock('../context/WeatherContext', () => ({
  WeatherProvider: ({ children }: { children: React.ReactNode }) => <div data-testid="weather-provider">{children}</div>,
  useWeather: () => ({
    selectedStation: null,
    setSelectedStation: vi.fn(),
    weatherData: null,
    isLoading: false,
  }),
}));

// Mock the pages
vi.mock('../pages/Index', () => ({
  default: () => <div data-testid="index-page">Index Page</div>,
}));

vi.mock('../pages/Login', () => ({
  default: () => <div data-testid="login-page">Login Page</div>,
}));

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        refetchOnWindowFocus: false,
      },
    },
  });

  return render(
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    </BrowserRouter>
  );
};

describe('App', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders without crashing', () => {
    expect(() => renderWithProviders(<App />)).not.toThrow();
  });

  it('renders the main application structure', async () => {
    renderWithProviders(<App />);
    
    // Check that the app renders with context providers
    expect(screen.getByTestId('auth-provider')).toBeInTheDocument();
    expect(screen.getByTestId('language-provider')).toBeInTheDocument();
    expect(screen.getByTestId('config-provider')).toBeInTheDocument();
    expect(screen.getByTestId('weather-provider')).toBeInTheDocument();
  });

  it('renders the index page by default', async () => {
    renderWithProviders(<App />);
    
    await waitFor(() => {
      expect(screen.getByTestId('index-page')).toBeInTheDocument();
    });
  });

  it('handles routing correctly', async () => {
    // Test that the router is working
    renderWithProviders(<App />);
    
    // The app should render without throwing errors
    await waitFor(() => {
      expect(document.body).toContainHTML('div');
    });
  });
});
