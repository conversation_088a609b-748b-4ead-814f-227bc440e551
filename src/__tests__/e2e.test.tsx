import { describe, it, expect, vi, beforeAll, afterAll, beforeEach } from 'vitest';

// Mock server endpoints for E2E testing
const mockServer = {
  health: {
    status: 'ok',
    timestamp: new Date().toISOString(),
    cache: { keys: 0, stats: { hits: 0, misses: 0, keys: 0, ksize: 0, vsize: 0 } }
  },
  stations: [
    {
      id: '9414290',
      name: 'San Francisco, CA',
      lat: 37.8063,
      lon: -122.4659,
      state: 'CA'
    },
    {
      id: '9414523',
      name: 'Redwood City, CA',
      lat: 37.5063,
      lon: -122.2094,
      state: 'CA'
    }
  ],
  weatherData: {
    timestamp: new Date().toISOString(),
    temperature: 20,
    waterTemperature: 18,
    windSpeed: 10,
    windGust: 15,
    windDirection: 180,
    tideLevel: 1.5,
    lightning: false,
  }
};

// Mock fetch for E2E testing
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('End-to-End Application Tests', () => {
  beforeAll(() => {
    // Setup global mocks
    Object.defineProperty(window, 'location', {
      value: {
        href: 'http://localhost:8080',
        origin: 'http://localhost:8080',
        pathname: '/',
        search: '',
        hash: '',
      },
      writable: true,
    });
  });

  beforeEach(() => {
    vi.clearAllMocks();
    mockFetch.mockClear();
  });

  afterAll(() => {
    vi.restoreAllMocks();
  });

  describe('Application Health Checks', () => {
    it('should verify server health endpoint', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockServer.health,
      });

      const response = await fetch('http://localhost:3001/health');
      const data = await response.json();

      expect(response.ok).toBe(true);
      expect(data.status).toBe('ok');
      expect(data).toHaveProperty('timestamp');
      expect(data).toHaveProperty('cache');
    });

    it('should verify weather API endpoints are accessible', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockServer.stations,
      });

      const response = await fetch('http://localhost:3001/api/weather/stations/search?q=san');
      const data = await response.json();

      expect(response.ok).toBe(true);
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBeGreaterThan(0);
    });
  });

  describe('Weather Station Search Flow', () => {
    it('should complete full weather station search workflow', async () => {
      // Step 1: Search for stations
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockServer.stations,
      });

      const searchResponse = await fetch('/api/weather/stations/search?q=san%20francisco');
      const stations = await searchResponse.json();

      expect(stations).toHaveLength(2);
      expect(stations[0]).toHaveProperty('id');
      expect(stations[0]).toHaveProperty('name');
      expect(stations[0]).toHaveProperty('lat');
      expect(stations[0]).toHaveProperty('lon');

      // Step 2: Get weather data for selected station
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockServer.weatherData,
      });

      const weatherResponse = await fetch(`/api/weather/data/${stations[0].id}`);
      const weatherData = await weatherResponse.json();

      expect(weatherData).toHaveProperty('timestamp');
      expect(weatherData).toHaveProperty('temperature');
      expect(weatherData).toHaveProperty('windSpeed');
      expect(typeof weatherData.temperature).toBe('number');
      expect(typeof weatherData.windSpeed).toBe('number');
    });

    it('should handle station search with no results', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [],
      });

      const response = await fetch('/api/weather/stations/search?q=nonexistent');
      const data = await response.json();

      expect(response.ok).toBe(true);
      expect(data).toEqual([]);
    });

    it('should handle weather data not available', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ error: 'No weather data available for this station' }),
      });

      const response = await fetch('/api/weather/data/invalid-station');
      
      expect(response.ok).toBe(false);
      expect(response.status).toBe(404);
    });
  });

  describe('API Error Handling', () => {
    it('should handle server errors gracefully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({ error: 'Internal server error' }),
      });

      const response = await fetch('/api/weather/stations/search?q=test');
      
      expect(response.ok).toBe(false);
      expect(response.status).toBe(500);
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(fetch('/api/weather/stations/search?q=test')).rejects.toThrow('Network error');
    });

    it('should verify no mock data fallbacks exist', async () => {
      // Test that when API fails, no mock data is returned
      mockFetch.mockRejectedValueOnce(new Error('API unavailable'));

      await expect(fetch('/api/weather/data/test-station')).rejects.toThrow('API unavailable');
      
      // Verify the request was actually made (no mock fallback)
      expect(mockFetch).toHaveBeenCalledTimes(1);
    });
  });

  describe('Application Performance', () => {
    it('should complete API requests within reasonable time', async () => {
      const startTime = Date.now();
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockServer.stations,
      });

      await fetch('/api/weather/stations/search?q=test');
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // API should respond within 5 seconds (generous for testing)
      expect(duration).toBeLessThan(5000);
    });
  });
});
