import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { searchWeatherStations, getWeatherData } from '../services/weather.service';

// Mock fetch for controlled testing
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('Weather Service Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockFetch.mockClear();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('searchWeatherStations', () => {
    it('should search for weather stations successfully', async () => {
      const mockResponse = [
        {
          id: '9414290',
          name: 'San Francisco, CA',
          lat: 37.8063,
          lon: -122.4659,
          state: 'CA'
        }
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await searchWeatherStations('san francisco');
      
      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith('/api/weather/stations/search?q=san%20francisco');
    });

    it('should handle empty search queries', async () => {
      const result = await searchWeatherStations('');
      expect(result).toEqual([]);
      expect(mockFetch).not.toHaveBeenCalled();
    });

    it('should handle short search queries', async () => {
      const result = await searchWeatherStations('a');
      expect(result).toEqual([]);
      expect(mockFetch).not.toHaveBeenCalled();
    });

    it('should throw error when API fails', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(searchWeatherStations('san francisco')).rejects.toThrow('Network error');
    });

    it('should handle API returning non-ok response', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
      });

      await expect(searchWeatherStations('san francisco')).rejects.toThrow('HTTP error! status: 500');
    });
  });

  describe('getWeatherData', () => {
    it('should fetch weather data successfully', async () => {
      const mockWeatherData = {
        timestamp: '2023-01-01T00:00:00Z',
        temperature: 20,
        waterTemperature: 18,
        windSpeed: 10,
        windGust: 15,
        windDirection: 180,
        tideLevel: 1.5,
        lightning: false,
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockWeatherData,
      });

      const result = await getWeatherData('9414290');
      
      expect(result).toEqual(mockWeatherData);
      expect(mockFetch).toHaveBeenCalledWith('/api/weather/data/9414290');
    });

    it('should throw error when weather data API fails', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(getWeatherData('9414290')).rejects.toThrow('Network error');
    });

    it('should throw error when API returns 404', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
      });

      await expect(getWeatherData('9414290')).rejects.toThrow('HTTP error! status: 404');
    });

    it('should throw error when API returns 500', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
      });

      await expect(getWeatherData('9414290')).rejects.toThrow('HTTP error! status: 500');
    });
  });

  describe('Error Handling', () => {
    it('should not fall back to mock data when API fails', async () => {
      mockFetch.mockRejectedValueOnce(new Error('API unavailable'));

      await expect(searchWeatherStations('test')).rejects.toThrow('API unavailable');
      
      // Verify no mock data is returned
      expect(mockFetch).toHaveBeenCalledTimes(1);
    });

    it('should properly handle network timeouts', async () => {
      mockFetch.mockImplementationOnce(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), 100)
        )
      );

      await expect(searchWeatherStations('test')).rejects.toThrow('Request timeout');
    });
  });
});
