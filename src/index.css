
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 50% 98%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 210 92% 45%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 210 40% 96%;
    --accent-foreground: 222 47% 11%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 222 84% 48%;

    --radius: 0.5rem;

    --sidebar-background: 210 50% 98%;
    --sidebar-foreground: 222 47% 11%;
    --sidebar-primary: 210 92% 45%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 96%;
    --sidebar-accent-foreground: 222 47% 11%;
    --sidebar-border: 214 32% 91%;
    --sidebar-ring: 222 84% 48%;
  }

  html {
    font-family: 'Inter', sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
    overflow-x: hidden; /* Prevent horizontal scrolling */
  }

  /* Improve default focus styles */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2;
  }
  
  /* Glass effect for cards */
  .glass {
    @apply bg-white/80 backdrop-blur-sm border border-white/20 shadow-sm;
  }
  
  /* Transition for all interactive elements */
  a, button, input, select, textarea {
    @apply transition-all duration-200;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground;
  }
  
  /* Animation classes */
  .animate-in {
    animation: fade-in 0.3s ease-out forwards;
  }
  
  .animate-out {
    animation: fade-out 0.3s ease-out forwards;
  }
}

@layer components {
  .page-container {
    @apply container mx-auto px-4 py-6 max-w-7xl;
  }
  
  .page-title {
    @apply text-2xl sm:text-3xl font-bold mb-4 sm:mb-6 animate-in;
  }
  
  .card-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }
  
  .watercraft-card {
    @apply glass rounded-lg overflow-hidden transition-all duration-300 hover:shadow-md hover:-translate-y-1;
  }
  
  .button-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md font-medium;
  }
  
  .button-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 px-4 py-2 rounded-md font-medium;
  }
  
  .input-field {
    @apply px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring;
  }

  /* Hide scrollbar but keep functionality */
  .hide-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  .hide-scrollbar::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }
}

/* Responsive utilities */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
