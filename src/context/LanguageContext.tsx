
import { createContext, useState, useContext, useEffect, ReactNode } from "react";
import { SupportedLanguage, LanguageContextType } from "@/types";
import { getTranslation } from "@/i18n/translations";
import { TranslationKey } from "@/i18n/types";

const defaultLanguage: SupportedLanguage = "en";
const supportedLanguages: SupportedLanguage[] = ["en", "es", "fr", "de", "ko", "he"];

// Updated context type with typed translation function
interface TypedLanguageContextType extends Omit<LanguageContextType, 't'> {
  t: (key: TranslationKey) => string;
}

const LanguageContext = createContext<TypedLanguageContextType>({
  language: defaultLanguage,
  setLanguage: () => {},
  t: (key: TranslationKey) => key,
});

export const LanguageProvider = ({ children }: { children: ReactNode }) => {
  const [language, setLanguage] = useState<SupportedLanguage>(() => {
    // Try to get the language from localStorage
    if (typeof window === 'undefined') return defaultLanguage;
    
    const savedLanguage = localStorage.getItem("language") as SupportedLanguage;
    // Check if it's a valid language
    if (savedLanguage && supportedLanguages.includes(savedLanguage)) {
      return savedLanguage;
    }
    
    // Otherwise use the browser language if possible
    const browserLang = navigator.language.split("-")[0] as SupportedLanguage;
    if (supportedLanguages.includes(browserLang)) {
      return browserLang;
    }
    
    // Default to English
    return defaultLanguage;
  });

  // Update the document attributes when language changes
  useEffect(() => {
    // Save to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem("language", language);
    }
    
    // Set the document direction for RTL languages
    document.documentElement.dir = language === "he" ? "rtl" : "ltr";
    
    // Set lang attribute on html element for accessibility
    document.documentElement.lang = language;
    
    // Force update of all translated components
    document.dispatchEvent(new Event('languageChanged'));
  }, [language]);

  // Type-safe translation function
  const t = (key: TranslationKey): string => {
    if (!key) {
      console.warn('Empty translation key provided');
      return '';
    }
    
    const translation = getTranslation(language, key);
    
    // If the translation is not found, log a warning and return the key as fallback
    if (translation === key && process.env.NODE_ENV !== 'production') {
      console.warn(`Missing translation for key "${key}" in language "${language}"`);
    }
    
    return translation;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => useContext(LanguageContext);
