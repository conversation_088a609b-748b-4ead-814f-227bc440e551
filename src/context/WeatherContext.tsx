
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { toast } from 'sonner';
import { WeatherStation, WeatherData, HourlyForecast, WeatherUnit, WeatherAlerts } from '@/types/weather';
import { getWeatherData, getHourlyForecast, getWeatherAlerts, DEFAULT_STATION } from '@/services/weather.service';
import { useAuth } from '@/context/AuthContext';
import { useLanguage } from '@/context/LanguageContext';

interface WeatherContextType {
  currentStation: WeatherStation | null;
  setCurrentStation: (station: WeatherStation | null) => void;
  weatherData: WeatherData | null;
  hourlyForecast: HourlyForecast | null;
  weatherAlerts: WeatherAlerts | null;
  isLoading: boolean;
  isExpanded: boolean;
  setIsExpanded: (expanded: boolean) => void;
  weatherUnit: WeatherUnit;
  setWeatherUnit: (unit: WeatherUnit) => void;
  error: Error | null;
  refreshWeather: () => void;
}

const WeatherContext = createContext<WeatherContextType | undefined>(undefined);

/**
 * Get the default weather unit based on user's location/preferences
 * In the future, this could be enhanced to detect user's location and use appropriate units
 */
const getDefaultWeatherUnit = (user: any): WeatherUnit => {
  // If user has a preferred unit, use it
  if (user?.preferredWeatherUnit) {
    return user.preferredWeatherUnit as WeatherUnit;
  }

  // Try to detect from browser locale (basic implementation)
  const locale = navigator.language || 'en-US';

  // Countries that primarily use imperial units
  const imperialCountries = ['US', 'LR', 'MM']; // USA, Liberia, Myanmar
  const countryCode = locale.split('-')[1];

  if (countryCode && imperialCountries.includes(countryCode)) {
    return 'imperial';
  }

  // Default to metric for most of the world
  return 'metric';
};

export const WeatherProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const { t } = useLanguage();
  const [currentStation, setCurrentStation] = useState<WeatherStation | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [weatherUnit, setWeatherUnit] = useState<WeatherUnit>(() => getDefaultWeatherUnit(user));

  // Load user's preferred station and unit from user data
  useEffect(() => {
    if (user?.preferredWeatherStation) {
      setCurrentStation(user.preferredWeatherStation);
    } else {
      setCurrentStation(DEFAULT_STATION);
    }

    // Update weather unit based on user preference or default
    const defaultUnit = getDefaultWeatherUnit(user);
    setWeatherUnit(defaultUnit);
  }, [user]);
  
  // Query for weather data
  const { 
    data: weatherData, 
    isLoading: isLoadingWeather, 
    error: weatherError,
    refetch: refetchWeather
  } = useQuery({
    queryKey: ['weatherData', currentStation?.id],
    queryFn: () => currentStation ? getWeatherData(currentStation.id) : null,
    enabled: !!currentStation,
    refetchInterval: 1000 * 60 * 10, // Refetch every 10 minutes
    staleTime: 1000 * 60 * 5, // Data is stale after 5 minutes
    retry: 2, // Retry twice before giving up
  });
  
  // Query for hourly forecast
  const {
    data: hourlyForecast,
    isLoading: isLoadingForecast,
    error: forecastError,
    refetch: refetchForecast
  } = useQuery({
    queryKey: ['hourlyForecast', currentStation?.id],
    queryFn: () => currentStation ? getHourlyForecast(currentStation.id) : null,
    enabled: !!currentStation, // Always fetch when station is available
    refetchInterval: 1000 * 60 * 30, // Refetch every 30 minutes
    staleTime: 1000 * 60 * 15, // Data is stale after 15 minutes
    retry: 2, // Retry twice before giving up
  });

  // Query for weather alerts
  const {
    data: weatherAlerts,
    isLoading: isLoadingAlerts,
    error: alertsError,
    refetch: refetchAlerts
  } = useQuery({
    queryKey: ['weatherAlerts', currentStation?.lat, currentStation?.lon],
    queryFn: () => currentStation ? getWeatherAlerts(currentStation.lat, currentStation.lon) : null,
    enabled: !!currentStation,
    refetchInterval: 1000 * 60 * 15, // Refetch every 15 minutes
    staleTime: 1000 * 60 * 10, // Data is stale after 10 minutes
    retry: 2, // Retry twice before giving up
  });
  
  // Refresh all weather data
  const refreshWeather = () => {
    refetchWeather();
    refetchAlerts();
    refetchForecast(); // Always refresh forecast data
  };
  
  // Show error toast if there's an error
  useEffect(() => {
    if (weatherError) {
      toast.error(t('weather_error'));
      console.error('Weather data error:', weatherError);
    }
    
    if (forecastError) {
      toast.error(t('weather_error'));
      console.error('Forecast data error:', forecastError);
    }

    if (alertsError) {
      console.error('Weather alerts error:', alertsError);
      // Don't show toast for alerts errors as they're not critical
    }
  }, [weatherError, forecastError, alertsError, t]);
  
  const value = {
    currentStation,
    setCurrentStation,
    weatherData: weatherData || null,
    hourlyForecast: hourlyForecast || null,
    weatherAlerts: weatherAlerts || null,
    isLoading: isLoadingWeather || isLoadingForecast || isLoadingAlerts,
    isExpanded,
    setIsExpanded,
    weatherUnit,
    setWeatherUnit,
    error: weatherError || forecastError || null,
    refreshWeather,
  };
  
  return (
    <WeatherContext.Provider value={value}>
      {children}
    </WeatherContext.Provider>
  );
};

export const useWeather = (): WeatherContextType => {
  const context = useContext(WeatherContext);
  if (context === undefined) {
    throw new Error('useWeather must be used within a WeatherProvider');
  }
  return context;
};
