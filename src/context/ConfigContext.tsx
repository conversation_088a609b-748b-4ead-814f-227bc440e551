
import { createContext, useContext, ReactNode, useState, useEffect } from 'react';

// Define the configuration type
export interface AppConfig {
  enableTeamCreation: boolean;
  enableAdvancedScheduling: boolean;
  enableWeatherAlerts: boolean;
  enableMaintenanceRequests: boolean;
  enableMemberSignUp: boolean; // Feature flag for member sign-up
  debugMode: boolean;
}

// Default configuration
const defaultConfig: AppConfig = {
  enableTeamCreation: false,
  enableAdvancedScheduling: false,
  enableWeatherAlerts: false,
  enableMaintenanceRequests: true,
  enableMemberSignUp: true, // Set to true
  debugMode: false
};

interface ConfigContextType {
  config: AppConfig;
  isFeatureEnabled: (featureName: keyof AppConfig) => boolean;
}

const ConfigContext = createContext<ConfigContextType>({
  config: defaultConfig,
  isFeatureEnabled: () => false,
});

export const ConfigProvider = ({ children }: { children: ReactNode }) => {
  const [config, setConfig] = useState<AppConfig>(defaultConfig);

  // Load configuration from localStorage on mount
  useEffect(() => {
    const loadConfig = () => {
      try {
        const savedConfig = localStorage.getItem('app-config');
        if (savedConfig) {
          const parsedConfig = { ...defaultConfig, ...JSON.parse(savedConfig) };
          console.log('Config loaded:', parsedConfig);
          setConfig(parsedConfig);
        } else {
          console.log('Using default config:', defaultConfig);
        }
      } catch (error) {
        console.error('Failed to load config from localStorage', error);
      }
    };

    loadConfig();

    // Listen for storage events to update config when it changes in other tabs
    window.addEventListener('storage', loadConfig);
    return () => window.removeEventListener('storage', loadConfig);
  }, []);

  // Check if a specific feature is enabled
  const isFeatureEnabled = (featureName: keyof AppConfig): boolean => {
    const isEnabled = config[featureName] || false;
    console.log(`Feature check: ${featureName} = ${isEnabled}`);
    return isEnabled;
  };

  return (
    <ConfigContext.Provider value={{ config, isFeatureEnabled }}>
      {children}
    </ConfigContext.Provider>
  );
};

export const useConfig = () => useContext(ConfigContext);
