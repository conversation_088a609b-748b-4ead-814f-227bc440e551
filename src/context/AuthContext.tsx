
import { createContext, useState, useContext, useEffect, ReactNode } from "react";
import { User } from "@/types";
import { toast } from "sonner";
import { mockUsers } from "@/services/mockData";

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  socialLogin: (provider: 'github' | 'facebook' | 'twitter' | 'google' | 'apple') => Promise<void>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
  isAdmin: boolean;
  isCoach: boolean;
  loading: boolean;
  updateUser: (user: User) => void; // Add the updateUser function to the interface
}

// This ensures that our user ID format is consistent throughout the application
export const validateUserId = (id: string): boolean => {
  return /^user-\d{3}$/.test(id);
};

const AuthContext = createContext<AuthContextType>({
  user: null,
  login: () => Promise.resolve(),
  socialLogin: () => Promise.resolve(),
  logout: () => Promise.resolve(),
  isAuthenticated: false,
  isAdmin: false,
  isCoach: false,
  loading: false,
  updateUser: () => {}, // Add default implementation
});

/**
 * Get default weather unit based on browser locale
 */
const getDefaultWeatherUnitForNewUser = (): 'metric' | 'imperial' => {
  const locale = navigator.language || 'en-US';
  const countryCode = locale.split('-')[1];

  // Countries that primarily use imperial units
  const imperialCountries = ['US', 'LR', 'MM']; // USA, Liberia, Myanmar

  if (countryCode && imperialCountries.includes(countryCode)) {
    return 'imperial';
  }

  return 'metric';
};

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>({
    id: 'user-001', // Using user-001 to match team data
    name: 'Alex Johnson',
    email: '<EMAIL>',
    phone: '************',
    role: 'member',
    skills: ['Rowing'],
    permissions: [
      { watercraftType: 'boat', skillLevel: 1 },
      { watercraftType: 'kayak', skillLevel: 0 },
    ],
    language: 'en',
    favorites: [],
    preferredWeatherUnit: getDefaultWeatherUnitForNewUser(),
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-06-15'),
  });

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const savedUser = localStorage.getItem("user");
    if (savedUser) {
      try {
        const parsedUser = JSON.parse(savedUser);
        // Validate the user ID before setting it
        if (parsedUser.id && validateUserId(parsedUser.id)) {
          setUser(parsedUser);
        } else {
          console.error("Invalid user ID format in saved user data");
          localStorage.removeItem("user");
        }
      } catch (error) {
        console.error("Failed to parse saved user", error);
        localStorage.removeItem("user");
      }
    }
    setLoading(false);
  }, []);

  const updateUser = (updatedUser: User) => {
    setUser(updatedUser);
  };

  const login = async (email: string, password: string): Promise<void> => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const foundUser = mockUsers.find(u => u.email === email) || mockUsers[0];
      
      if (!foundUser) {
        throw new Error("Invalid email or password");
      }
      
      // Validate user ID before setting
      if (!validateUserId(foundUser.id)) {
        throw new Error("Invalid user ID format");
      }
      
      setUser(foundUser);
      localStorage.setItem("user", JSON.stringify(foundUser));
      toast.success(`Welcome back, ${foundUser.name}!`);
    } catch (error) {
      console.error("Login error:", error);
      toast.error((error as Error).message || "Failed to login");
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const socialLogin = async (provider: 'github' | 'facebook' | 'twitter' | 'google' | 'apple'): Promise<void> => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const randomIndex = Math.floor(Math.random() * mockUsers.length);
      const foundUser = mockUsers[randomIndex];
      
      // Validate user ID before setting
      if (!validateUserId(foundUser.id)) {
        throw new Error(`Invalid user ID format for ${provider} login`);
      }
      
      setUser(foundUser);
      localStorage.setItem("user", JSON.stringify(foundUser));
      toast.success(`Welcome via ${provider}, ${foundUser.name}!`);
    } catch (error) {
      console.error(`${provider} login error:`, error);
      toast.error(`Failed to login with ${provider}`);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setUser(null);
      localStorage.removeItem("user");
      toast.success("Successfully logged out");
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("Failed to logout");
    } finally {
      setLoading(false);
    }
  };

  const isAuthenticated = !!user;
  const isAdmin = user?.role === 'admin';
  const isCoach = user?.role === 'coach';

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        socialLogin,
        logout,
        isAuthenticated,
        isAdmin,
        isCoach,
        loading,
        updateUser, // Add updateUser to the provider
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
