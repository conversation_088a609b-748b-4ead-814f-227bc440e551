
import { Watercraft, Boat } from "@/types";
import { generateWatercraftId } from "@/utils/idGenerator";

// Mock data for watercrafts - create a deep copy to prevent mutation issues
export const mockWatercrafts: Watercraft[] = [
  {
    id: "craft-001",
    name: "Swift Arrow",
    type: "boat",
    ownershipType: "club",
    location: "Bay A, Rack 3",
    imageUrl: "https://images.unsplash.com/photo-1535535112387-56ffe8db21ff?q=80&w=600&auto=format&fit=crop",
    skillLevel: 1,
    status: "available",
    createdAt: new Date(),
    updatedAt: new Date()
  } as Boat,
  {
    id: "craft-002",
    name: "Water Strider",
    type: "kayak",
    ownershipType: "club",
    location: "Bay B, Rack 1",
    imageUrl: "https://images.unsplash.com/photo-1520454083703-778d6da1af02?q=80&w=600&auto=format&fit=crop",
    skillLevel: 0,
    status: "available",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "craft-003",
    name: "Blue Dolphin",
    type: "boat",
    ownershipType: "club",
    location: "Bay A, Rack 2",
    imageUrl: "https://images.unsplash.com/photo-1564842223950-a6ea8052e79e?q=80&w=600&auto=format&fit=crop",
    skillLevel: 2,
    status: "maintenance",
    createdAt: new Date(),
    updatedAt: new Date()
  } as Boat,
  {
    id: "craft-004",
    name: "Wind Chaser",
    type: "surfski",
    ownershipType: "member",
    memberId: "user-003",
    location: "Bay C, Rack 1",
    imageUrl: "https://images.unsplash.com/photo-1580332449238-a72634d3876b?q=80&w=600&auto=format&fit=crop",
    skillLevel: 3,
    status: "available",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "craft-005",
    name: "Sea Runner",
    type: "PB",
    ownershipType: "club",
    location: "Bay B, Rack 4",
    imageUrl: "https://images.unsplash.com/photo-1602568584853-71ce86840276?q=80&w=600&auto=format&fit=crop",
    skillLevel: 2,
    status: "in-use",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "craft-006",
    name: "Wave Rider",
    type: "launch",
    ownershipType: "club",
    location: "Marina Dock B",
    imageUrl: "https://images.unsplash.com/photo-1618640399586-4ea62f9688e8?q=80&w=600&auto=format&fit=crop",
    skillLevel: 3,
    status: "available",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "craft-007",
    name: "Eagle Eye",
    type: "boat",
    ownershipType: "club",
    location: "Bay A, Rack 1",
    imageUrl: "https://images.unsplash.com/photo-1595836956462-1de23a3d80e6?q=80&w=600&auto=format&fit=crop",
    skillLevel: 0,
    status: "available",
    createdAt: new Date(),
    updatedAt: new Date()
  } as Boat,
  {
    id: "craft-008",
    name: "Silver Streak",
    type: "kayak",
    ownershipType: "club",
    location: "Bay B, Rack 2",
    imageUrl: "https://images.unsplash.com/photo-1562610744-7c427b7e7d2f?q=80&w=600&auto=format&fit=crop",
    skillLevel: 0,
    status: "available",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "craft-009",
    name: "Coastal Cruiser",
    type: "coastal",
    ownershipType: "club",
    location: "Bay D, Rack 3",
    imageUrl: "https://images.unsplash.com/photo-1589307197668-6c28382b5b68?q=80&w=600&auto=format&fit=crop",
    skillLevel: 2,
    status: "available",
    createdAt: new Date(),
    updatedAt: new Date()
  }
];
