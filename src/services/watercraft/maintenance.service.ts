
import { MaintenanceRequest, UUID } from "@/types";
import { generateMaintenanceId } from "@/utils/idGenerator";
import { mockMaintenanceRequests, mockWatercrafts } from "./mock-data";

// Get all maintenance requests
export const getAllMaintenanceRequests = async (): Promise<MaintenanceRequest[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  return [...mockMaintenanceRequests];
};

// Get maintenance requests by watercraft ID
export const getMaintenanceRequestsByWatercraft = async (watercraftId: UUID): Promise<MaintenanceRequest[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  return mockMaintenanceRequests.filter(req => req.watercraftId === watercraftId);
};

// Add a new maintenance request
export const addMaintenanceRequest = async (
  request: Omit<MaintenanceRequest, 'id' | 'createdAt' | 'updatedAt'>
): Promise<MaintenanceRequest> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const newRequest: MaintenanceRequest = {
    ...request,
    id: generateMaintenanceId(),
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  mockMaintenanceRequests.push(newRequest);
  
  // If request is in progress, update watercraft status
  if (request.status === 'in-progress') {
    const watercraftIndex = mockWatercrafts.findIndex(wc => wc.id === request.watercraftId);
    if (watercraftIndex !== -1) {
      mockWatercrafts[watercraftIndex] = {
        ...mockWatercrafts[watercraftIndex],
        status: 'maintenance',
        updatedAt: new Date()
      };
    }
  }
  
  return newRequest;
};

// Update a maintenance request
export const updateMaintenanceRequest = async (
  id: UUID,
  updates: Partial<MaintenanceRequest>
): Promise<MaintenanceRequest | null> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const index = mockMaintenanceRequests.findIndex(req => req.id === id);
  if (index === -1) return null;
  
  const oldStatus = mockMaintenanceRequests[index].status;
  const newStatus = updates.status || oldStatus;
  
  const updatedRequest = {
    ...mockMaintenanceRequests[index],
    ...updates,
    updatedAt: new Date()
  };
  
  mockMaintenanceRequests[index] = updatedRequest;
  
  // If status changed to or from 'in progress', update watercraft status
  if (oldStatus !== newStatus) {
    const watercraftId = updatedRequest.watercraftId;
    const watercraftIndex = mockWatercrafts.findIndex(wc => wc.id === watercraftId);
    
    if (watercraftIndex !== -1) {
      if (newStatus === 'in-progress') {
        mockWatercrafts[watercraftIndex] = {
          ...mockWatercrafts[watercraftIndex],
          status: 'maintenance',
          updatedAt: new Date()
        };
      } else if (oldStatus === 'in-progress') {
        // Check if there are any other in-progress requests for this watercraft
        const hasOtherInProgressRequests = mockMaintenanceRequests.some(
          req => req.id !== id && req.watercraftId === watercraftId && req.status === 'in-progress'
        );
        
        if (!hasOtherInProgressRequests) {
          mockWatercrafts[watercraftIndex] = {
            ...mockWatercrafts[watercraftIndex],
            status: 'available',
            updatedAt: new Date()
          };
        }
      }
    }
  }
  
  return updatedRequest;
};

// Get count of open or in-progress maintenance requests by watercraft
export const getActiveMaintenanceCount = async (watercraftId: UUID): Promise<number> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200));
  
  return mockMaintenanceRequests.filter(
    req => req.watercraftId === watercraftId && 
    (req.status === 'open' || req.status === 'in-progress')
  ).length;
};
