
import { Watercraft, Boat, WatercraftType, UUID } from "@/types";
import { generateWatercraftId } from "@/utils/idGenerator";
import { mockWatercrafts } from "./mock-data";

// Get all watercrafts
export const getAllWatercrafts = async (): Promise<Watercraft[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  return [...mockWatercrafts];
};

// Get watercraft by ID
export const getWatercraftById = async (id: UUID): Promise<Watercraft | null> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  return mockWatercrafts.find(wc => wc.id === id) || null;
};

// Get watercrafts by type
export const getWatercraftsByType = async (type: WatercraftType): Promise<Watercraft[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  return mockWatercrafts.filter(wc => wc.type === type);
};

// Get watercrafts by owner
export const getWatercraftsByOwner = async (memberId: UUID): Promise<Watercraft[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  return mockWatercrafts.filter(wc => wc.ownershipType === 'member' && wc.memberId === memberId);
};

// Add a new watercraft
export const addWatercraft = async (watercraft: Omit<Watercraft, 'id' | 'createdAt' | 'updatedAt'>): Promise<Watercraft> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const newWatercraft: Watercraft = {
    ...watercraft,
    id: generateWatercraftId(),
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  mockWatercrafts.push(newWatercraft);
  return newWatercraft;
};

// Update a watercraft
export const updateWatercraft = async (id: UUID, updates: Partial<Watercraft>): Promise<Watercraft | null> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const index = mockWatercrafts.findIndex(wc => wc.id === id);
  if (index === -1) return null;
  
  const updatedWatercraft = {
    ...mockWatercrafts[index],
    ...updates,
    updatedAt: new Date()
  };
  
  mockWatercrafts[index] = updatedWatercraft;
  return updatedWatercraft;
};

// Delete a watercraft (soft delete)
export const deleteWatercraft = async (id: UUID): Promise<boolean> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const index = mockWatercrafts.findIndex(wc => wc.id === id);
  if (index === -1) return false;
  
  mockWatercrafts[index] = {
    ...mockWatercrafts[index],
    deleted: true,
    updatedAt: new Date()
  };
  
  return true;
};
