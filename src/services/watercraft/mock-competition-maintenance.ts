
import { MaintenanceRequest } from "@/types";
import { generateMaintenanceId } from "@/utils/idGenerator";

// Competition equipment maintenance requests
export const mockCompetitionMaintenanceRequests: MaintenanceRequest[] = [
  {
    id: generateMaintenanceId(),
    watercraftId: "craft-013", // Surfski Pro
    requestDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
    watercraftState: "unuseable",
    issueType: "damaged",
    note: "Severe impact damage to the bow section.",
    status: "in-progress",
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
  },
  {
    id: generateMaintenanceId(),
    watercraftId: "craft-007", // Eagle Eye
    requestDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
    watercraftState: "useable",
    issueType: "missing part",
    note: "Stretcher adjustment mechanism is loose.",
    status: "closed",
    resolution: "Replaced with new parts and secured properly.",
    resolutionDate: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000), // 8 days ago
    createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000)
  },
  {
    id: generateMaintenanceId(),
    watercraftId: "craft-011", // Trainer Double
    requestDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    watercraftState: "useable",
    issueType: "broken",
    note: "Oar holder clamps need adjustment, not closing properly.",
    status: "open",
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
  },
  {
    id: generateMaintenanceId(),
    watercraftId: "craft-014", // Safety Launch
    requestDate: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000), // 20 days ago
    watercraftState: "unuseable",
    issueType: "broken",
    note: "Engine not starting, possible fuel system issue.",
    status: "closed",
    resolution: "Fuel system cleaned and carburetor rebuilt. Working properly now.",
    resolutionDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
    createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)
  }
];
