
import { Watercraft, MaintenanceRequest } from "@/types";
import { mockWatercrafts as watercrafts } from "./mock-watercrafts";
import { mockCompetitionWatercrafts as competitionWatercrafts } from "./mock-competition-watercrafts";
import { mockMaintenanceRequests as maintenanceRequests } from "./mock-maintenance";
import { mockCompetitionMaintenanceRequests as competitionMaintenanceRequests } from "./mock-competition-maintenance";
import { getCleanMockWatercrafts } from "./data-utils";

// Export all the combined mock data
export { getCleanMockWatercrafts } from "./data-utils";

// Combine all watercrafts for a complete list
export const mockWatercrafts: Watercraft[] = [
  ...watercrafts,
  ...competitionWatercrafts
];

// Combine all maintenance requests
export const mockMaintenanceRequests: MaintenanceRequest[] = [
  ...maintenanceRequests,
  ...competitionMaintenanceRequests
];
