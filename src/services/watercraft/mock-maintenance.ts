
import { MaintenanceRequest } from "@/types";
import { generateMaintenanceId } from "@/utils/idGenerator";

// Mock maintenance requests
export const mockMaintenanceRequests: MaintenanceRequest[] = [
  {
    id: generateMaintenanceId(),
    watercraftId: "craft-003",
    requestDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
    watercraftState: "unuseable",
    issueType: "broken",
    note: "Rudder mechanism is stuck in one position.",
    status: "in-progress",
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
  },
  {
    id: generateMaintenanceId(),
    watercraftId: "craft-005",
    requestDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    watercraftState: "useable",
    issueType: "missing part",
    note: "Left oarlock is missing its pin.",
    status: "open",
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
  },
  {
    id: generateMaintenanceId(),
    watercraftId: "craft-008",
    requestDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 14 days ago
    watercraftState: "unuseable",
    issueType: "damaged",
    note: "Hull has a crack on the port side.",
    status: "closed",
    resolution: "Hull repaired with epoxy and reinforced with fiberglass.",
    resolutionDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
  },
];
