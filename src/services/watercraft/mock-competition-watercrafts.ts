
import { Boat, Watercraft } from "@/types";

// Competition and training watercrafts
export const mockCompetitionWatercrafts: Watercraft[] = [
  {
    id: "craft-010",
    name: "Competition 8+",
    type: "boat",
    ownershipType: "club",
    location: "Race Preparation Area",
    imageUrl: "https://images.unsplash.com/photo-1541443458929-9d6cbd3d2513?q=80&w=600&auto=format&fit=crop",
    skillLevel: 3,
    status: "regatta",
    createdAt: new Date(),
    updatedAt: new Date()
  } as Boat,
  {
    id: "craft-011",
    name: "Trainer Double",
    type: "boat",
    ownershipType: "club",
    location: "Training Area B",
    imageUrl: "https://images.unsplash.com/photo-1532643076501-5ad2201be430?q=80&w=600&auto=format&fit=crop",
    skillLevel: 1,
    status: "in-use",
    createdAt: new Date(),
    updatedAt: new Date()
  } as Boat,
  {
    id: "craft-012",
    name: "Coastal Racer",
    type: "coastal",
    ownershipType: "member",
    memberId: "user-005",
    location: "Member Storage A2",
    imageUrl: "https://images.unsplash.com/photo-1590179406383-a8e58ddb7652?q=80&w=600&auto=format&fit=crop",
    skillLevel: 2,
    status: "available",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "craft-013",
    name: "Surfski Pro",
    type: "surfski",
    ownershipType: "club",
    location: "Competition Storage",
    imageUrl: "https://images.unsplash.com/photo-1566008033935-e9f529c17b77?q=80&w=600&auto=format&fit=crop",
    skillLevel: 3,
    status: "maintenance",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "craft-014",
    name: "Safety Launch",
    type: "launch",
    ownershipType: "club",
    location: "Safety Dock",
    imageUrl: "https://images.unsplash.com/photo-1554253449-cfcc0c4b6b43?q=80&w=600&auto=format&fit=crop",
    skillLevel: 2,
    status: "available",
    createdAt: new Date(),
    updatedAt: new Date()
  },
];
