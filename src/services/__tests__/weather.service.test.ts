import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { searchWeatherStations, findNearbyStations, POPULAR_STATIONS } from '../weather.service';

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('Weather Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset console methods
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('searchWeatherStations', () => {
    it('should return empty array for queries less than 2 characters', async () => {
      const result = await searchWeatherStations('a');
      expect(result).toEqual([]);
    });

    it('should return empty array for empty query', async () => {
      const result = await searchWeatherStations('');
      expect(result).toEqual([]);
    });

    it('should find Redwood City station by city name', async () => {
      // Mock successful API response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [
          { id: '9414523', name: 'Redwood City, CA', lat: 37.5067, lon: -122.21, state: 'CA' }
        ]
      });

      const result = await searchWeatherStations('redwood city');
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Redwood City, CA');
      expect(result[0].id).toBe('9414523');
    });

    it('should find Redwood City station by city name with state', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [
          { id: '9414523', name: 'Redwood City, CA', lat: 37.5067, lon: -122.21, state: 'CA' }
        ]
      });

      const result = await searchWeatherStations('redwood city, ca');
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Redwood City, CA');
    });

    it('should find stations by state abbreviation', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [
          { id: '9414290', name: 'San Francisco, CA', lat: 37.8063, lon: -122.4659, state: 'CA' },
          { id: '9414523', name: 'Redwood City, CA', lat: 37.5067, lon: -122.21, state: 'CA' }
        ]
      });

      const result = await searchWeatherStations('ca');
      expect(result.length).toBeGreaterThan(0);
      expect(result.every(station => station.state === 'CA')).toBe(true);
    });

    it('should find station by ID', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [
          { id: '9414523', name: 'Redwood City, CA', lat: 37.5067, lon: -122.21, state: 'CA' }
        ]
      });

      const result = await searchWeatherStations('9414523');
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('9414523');
    });

    it('should handle case-insensitive searches', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [
          { id: '9414523', name: 'Redwood City, CA', lat: 37.5067, lon: -122.21, state: 'CA' }
        ]
      });

      const result = await searchWeatherStations('REDWOOD CITY');
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Redwood City, CA');
    });

    it('should trim whitespace from queries', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [
          { id: '9414523', name: 'Redwood City, CA', lat: 37.5067, lon: -122.21, state: 'CA' }
        ]
      });

      const result = await searchWeatherStations('  redwood city  ');
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Redwood City, CA');
    });

    it('should fallback to popular stations when API fails', async () => {
      // Mock API failure
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const result = await searchWeatherStations('redwood city');
      
      // Should still find Redwood City in popular stations
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Redwood City, CA');
      expect(result[0].id).toBe('9414523');
    });

    it('should handle API returning null or undefined', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => null
      });

      const result = await searchWeatherStations('nonexistent');
      expect(result).toEqual([]);
    });

    it('should handle HTTP errors gracefully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      });

      const result = await searchWeatherStations('redwood city');
      
      // Should fallback to popular stations
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Redwood City, CA');
    });

    it('should find multiple stations for broad searches', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [
          { id: '9414290', name: 'San Francisco, CA', lat: 37.8063, lon: -122.4659, state: 'CA' },
          { id: '9414523', name: 'Redwood City, CA', lat: 37.5067, lon: -122.21, state: 'CA' },
          { id: '9414750', name: 'Alameda, CA', lat: 37.7717, lon: -122.3000, state: 'CA' }
        ]
      });

      const result = await searchWeatherStations('san');
      expect(result.length).toBeGreaterThan(1);
      expect(result.some(station => station.name.includes('San Francisco'))).toBe(true);
    });
  });

  describe('findNearbyStations', () => {
    it('should find stations near San Francisco coordinates', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [
          { 
            id: '9414290', 
            name: 'San Francisco, CA', 
            lat: 37.8063, 
            lon: -122.4659, 
            state: 'CA',
            distance: 0.5
          }
        ]
      });

      const result = await findNearbyStations(37.8063, -122.4659, 50);
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('San Francisco, CA');
      expect(result[0].distance).toBeDefined();
    });

    it('should handle invalid coordinates', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Invalid coordinates'));

      const result = await findNearbyStations(999, 999, 50);
      expect(Array.isArray(result)).toBe(true);
    });
  });

  describe('POPULAR_STATIONS', () => {
    it('should include Redwood City station', () => {
      const redwoodCity = POPULAR_STATIONS.find(station => station.id === '9414523');
      expect(redwoodCity).toBeDefined();
      expect(redwoodCity?.name).toBe('Redwood City, CA');
      expect(redwoodCity?.lat).toBe(37.5067);
      expect(redwoodCity?.lon).toBe(-122.21);
      expect(redwoodCity?.state).toBe('CA');
    });

    it('should include major coastal cities', () => {
      const expectedCities = [
        'San Francisco, CA',
        'San Diego, CA', 
        'Boston, MA',
        'Miami Beach, FL',
        'Seattle, WA'
      ];

      expectedCities.forEach(cityName => {
        const station = POPULAR_STATIONS.find(s => s.name === cityName);
        expect(station).toBeDefined();
      });
    });

    it('should have valid coordinates for all stations', () => {
      POPULAR_STATIONS.forEach(station => {
        expect(station.lat).toBeGreaterThan(-90);
        expect(station.lat).toBeLessThan(90);
        expect(station.lon).toBeGreaterThan(-180);
        expect(station.lon).toBeLessThan(180);
        expect(station.id).toBeTruthy();
        expect(station.name).toBeTruthy();
      });
    });
  });
});
