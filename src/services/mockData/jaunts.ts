
import { Jaunt } from '@/types';
import { generateId } from '@/utils/idGenerator';
import { mockUsers } from './users';
import { mockWatercrafts } from './watercrafts';
import { mockBoats } from './boats';

// Mock Jaunts
export const mockJaunts: Jaunt[] = [
  {
    id: generateId('jaunt'),
    userId: mockUsers[2].id,
    watercraftId: mockWatercrafts[1].id,
    startTime: new Date('2023-05-15T09:00:00'),
    plannedEndTime: new Date('2023-05-15T11:00:00'),
    comments: 'Great morning row, water was calm',
  },
  // New test data - more diverse jaunt scenarios
  {
    id: generateId('jaunt'),
    userId: mockUsers[0].id,
    watercraftId: mockBoats[1].id, // Precision Single
    startTime: new Date('2023-06-10T07:30:00'),
    plannedEndTime: new Date('2023-06-10T09:30:00'),
    actualEndTime: new Date('2023-06-10T09:15:00'),
    comments: 'Early morning training session, excellent conditions',
    createdAt: new Date('2023-06-09T18:00:00'),
    updatedAt: new Date('2023-06-10T09:15:00'),
  },
  {
    id: generateId('jaunt'),
    userId: mockUsers[1].id,
    watercraftId: mockBoats[0].id, // Victory Sweep
    startTime: new Date('2023-06-12T15:00:00'),
    plannedEndTime: new Date('2023-06-12T17:00:00'),
    comments: 'Team practice for upcoming regatta',
    createdAt: new Date('2023-06-11T10:00:00'),
    updatedAt: new Date('2023-06-11T10:00:00'),
  },
  {
    id: generateId('jaunt'),
    userId: mockUsers[2].id,
    watercraftId: mockWatercrafts[2].id, // Wave Rider
    startTime: new Date('2023-06-15T12:00:00'),
    plannedEndTime: new Date('2023-06-15T13:30:00'),
    actualEndTime: new Date('2023-06-15T14:00:00'),
    comments: 'Lunchtime paddle, stayed out longer due to perfect weather',
    createdAt: new Date('2023-06-14T19:00:00'),
    updatedAt: new Date('2023-06-15T14:00:00'),
  },
  {
    id: generateId('jaunt'),
    userId: mockUsers[0].id,
    watercraftId: mockWatercrafts[11].id, // Rescue Patrol
    startTime: new Date('2023-06-18T08:00:00'),
    plannedEndTime: new Date('2023-06-18T12:00:00'),
    comments: 'Safety boat duty for junior training session',
    createdAt: new Date('2023-06-17T15:00:00'),
    updatedAt: new Date('2023-06-17T15:00:00'),
  },
  {
    id: generateId('jaunt'),
    userId: mockUsers[1].id,
    watercraftId: mockBoats[3].id, // Team Quad
    startTime: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
    plannedEndTime: new Date(Date.now() + 60 * 60 * 1000), // 1 hour from now
    comments: 'Current training session with team',
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 60 * 60 * 1000),
  },
  {
    id: generateId('jaunt'),
    userId: mockUsers[2].id,
    watercraftId: mockBoats[7].id, // Coastal Single
    startTime: new Date(Date.now() - 30 * 60 * 1000), // 30 mins ago
    plannedEndTime: new Date(Date.now() + 30 * 60 * 1000), // 30 mins from now
    comments: 'Quick practice session during lunch break',
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 30 * 60 * 1000),
  },
];
