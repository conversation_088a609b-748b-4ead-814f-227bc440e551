
import { User } from '@/types';
import { generateId } from '@/utils/idGenerator';
import { mockWatercrafts } from './watercrafts';
import { mockBoats } from './boats';

// Mock Users
export const mockUsers: User[] = [
  {
    id: generateId('user'),
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '************',
    role: 'admin',
    skills: ['Rowing', 'Coaching', 'Maintenance'],
    permissions: [
      { watercraftType: 'boat', skillLevel: 3 },
      { watercraftType: 'kayak', skillLevel: 3 },
      { watercraftType: 'PB', skillLevel: 3 },
      { watercraftType: 'launch', skillLevel: 3 },
      { watercraftType: 'surfski', skillLevel: 3 },
    ],
    language: 'en',
    favorites: [mockWatercrafts[0].id, mockWatercrafts[2].id],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId('user'),
    name: '<PERSON>',
    email: 'saman<PERSON>.<EMAIL>',
    phone: '************',
    role: 'coach',
    skills: ['Rowing', 'Coaching'],
    permissions: [
      { watercraftType: 'boat', skillLevel: 2 },
      { watercraftType: 'kayak', skillLevel: 1 },
      { watercraftType: 'PB', skillLevel: 2 },
      { watercraftType: 'launch', skillLevel: 2 },
    ],
    language: 'en',
    favorites: [mockBoats[0].id],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId('user'),
    name: 'Michael Chen',
    email: '<EMAIL>',
    phone: '************',
    role: 'member',
    skills: ['Rowing'],
    permissions: [
      { watercraftType: 'boat', skillLevel: 1 },
      { watercraftType: 'kayak', skillLevel: 0 },
    ],
    language: 'en',
    favorites: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  // New users below
  {
    id: generateId('user'),
    name: 'Sarah Rodriguez',
    email: '<EMAIL>',
    phone: '************',
    role: 'coach',
    skills: ['Rowing', 'Coaching', 'Coxswain'],
    permissions: [
      { watercraftType: 'boat', skillLevel: 3 },
      { watercraftType: 'kayak', skillLevel: 2 },
      { watercraftType: 'launch', skillLevel: 3 },
    ],
    language: 'es',
    favorites: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId('user'),
    name: 'David Kim',
    email: '<EMAIL>',
    phone: '************',
    role: 'member',
    skills: ['Rowing', 'Maintenance'],
    permissions: [
      { watercraftType: 'boat', skillLevel: 2 },
      { watercraftType: 'surfski', skillLevel: 1 },
    ],
    language: 'ko',
    favorites: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId('user'),
    name: 'Emma Wilson',
    email: '<EMAIL>',
    phone: '************',
    role: 'member',
    skills: ['Rowing'],
    permissions: [
      { watercraftType: 'boat', skillLevel: 1 },
    ],
    language: 'en',
    favorites: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId('user'),
    name: 'Jean Dupont',
    email: '<EMAIL>',
    phone: '************',
    role: 'coach',
    skills: ['Rowing', 'Coaching', 'Coxswain'],
    permissions: [
      { watercraftType: 'boat', skillLevel: 3 },
      { watercraftType: 'PB', skillLevel: 2 },
      { watercraftType: 'launch', skillLevel: 2 },
    ],
    language: 'fr',
    favorites: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: generateId('user'),
    name: 'Anna Schmidt',
    email: '<EMAIL>',
    phone: '************',
    role: 'member',
    skills: ['Rowing', 'Coxswain'],
    permissions: [
      { watercraftType: 'boat', skillLevel: 2 },
      { watercraftType: 'kayak', skillLevel: 1 },
    ],
    language: 'de',
    favorites: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  }
];
