
import { MaintenanceRequest } from '@/types';
import { generateId } from '@/utils/idGenerator';
import { mockWatercrafts } from './watercrafts';
import { mockBoats } from './boats';

// Mock Maintenance Requests
export const mockMaintenanceRequests: MaintenanceRequest[] = [
  {
    id: generateId('maintenance'),
    watercraftId: mockWatercrafts[3].id,
    requestDate: new Date('2023-05-10'),
    watercraftState: 'unuseable',
    issueType: 'broken',
    note: 'Rudder not responding correctly, needs immediate attention',
    status: 'in-progress',
  },
  {
    id: generateId('maintenance'),
    watercraftId: mockWatercrafts[1].id,
    requestDate: new Date('2023-05-12'),
    watercraftState: 'useable',
    issueType: 'damaged',
    note: 'Minor scratch on hull, should be checked',
    status: 'open',
  },
  {
    id: generateId('maintenance'),
    watercraftId: mockBoats[0].id,
    requestDate: new Date('2023-05-09'),
    watercraftState: 'useable',
    issueType: 'missing part',
    note: 'Missing a rigger pin',
    status: 'open',
  },
  // New test data - more diverse maintenance scenarios
  {
    id: generateId('maintenance'),
    watercraftId: mockBoats[4].id, // Lightweight Pair
    requestDate: new Date('2023-06-15'),
    watercraftState: 'unuseable',
    issueType: 'broken',
    note: 'The seat slide mechanism is broken',
    status: 'in-progress',
    createdAt: new Date('2023-06-15'),
    updatedAt: new Date('2023-06-16'),
  },
  {
    id: generateId('maintenance'),
    watercraftId: mockWatercrafts[8].id, // Ocean Cruiser
    requestDate: new Date('2023-06-20'),
    watercraftState: 'unuseable',
    issueType: 'damaged',
    note: 'Large crack in hull after collision with dock',
    status: 'open',
    createdAt: new Date('2023-06-20'),
    updatedAt: new Date('2023-06-20'),
  },
  {
    id: generateId('maintenance'),
    watercraftId: mockBoats[6].id, // Triple Scull
    requestDate: new Date('2023-05-25'),
    watercraftState: 'useable',
    issueType: 'missing part',
    note: 'Missing foot stretcher adjustment parts',
    status: 'closed',
    resolution: 'Replaced with new parts from supplier',
    resolutionDate: new Date('2023-06-10'),
    createdAt: new Date('2023-05-25'),
    updatedAt: new Date('2023-06-10'),
  },
  {
    id: generateId('maintenance'),
    watercraftId: mockWatercrafts[11].id, // Rescue Patrol
    requestDate: new Date('2023-06-05'),
    watercraftState: 'useable',
    issueType: 'damaged',
    note: 'Engine cover damaged, still operational but needs fixing',
    status: 'open',
    createdAt: new Date('2023-06-05'),
    updatedAt: new Date('2023-06-05'),
  },
  {
    id: generateId('maintenance'),
    watercraftId: mockWatercrafts[7].id, // Regatta Star
    requestDate: new Date('2023-04-10'),
    watercraftState: 'unuseable',
    issueType: 'broken',
    note: 'Rigger completely detached from shell',
    status: 'closed',
    resolution: 'Rigger reattached and reinforced with additional hardware',
    resolutionDate: new Date('2023-04-20'),
    createdAt: new Date('2023-04-10'),
    updatedAt: new Date('2023-04-20'),
  },
];

// Helper function to get maintenance count for a watercraft
export const getMaintenanceCountForWatercraft = (watercraftId: string): number => {
  return mockMaintenanceRequests.filter(
    req => req.watercraftId === watercraftId && (req.status === 'open' || req.status === 'in-progress')
  ).length;
};
