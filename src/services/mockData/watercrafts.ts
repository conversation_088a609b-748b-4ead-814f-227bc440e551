
import { Watercraft } from '@/types';
import { generateId } from '@/utils/idGenerator';

// Mock Watercraft Data
export const mockWatercrafts: Watercraft[] = [
  {
    id: generateId('watercraft'),
    name: 'Fluid Motion',
    type: 'boat',
    ownershipType: 'club',
    location: 'Bay A, Rack 2',
    imageUrl: '/boats/fluid-motion.jpg',
    skillLevel: 1,
    status: 'available',
  },
  {
    id: generateId('watercraft'),
    name: 'Swift Current',
    type: 'boat',
    ownershipType: 'club',
    location: 'Bay B, Rack 1',
    imageUrl: '/boats/swift-current.jpg',
    skillLevel: 2,
    status: 'in-use',
  },
  {
    id: generateId('watercraft'),
    name: 'Wave Rider',
    type: 'kayak',
    ownershipType: 'club',
    location: 'Bay C, Rack 3',
    imageUrl: '/boats/wave-rider.jpg',
    skillLevel: 0,
    status: 'available',
  },
  {
    id: generateId('watercraft'),
    name: 'Sea Breeze',
    type: 'surfski',
    ownershipType: 'club',
    location: 'Bay D, Rack 1',
    imageUrl: '/boats/sea-breeze.jpg',
    skillLevel: 1,
    status: 'maintenance',
  },
  {
    id: generateId('watercraft'),
    name: 'Rapid Runner',
    type: 'PB',
    ownershipType: 'club',
    location: 'Bay A, Rack 3',
    imageUrl: '/boats/rapid-runner.jpg',
    skillLevel: 0,
    status: 'available',
  },
  {
    id: generateId('watercraft'),
    name: 'Coastal Explorer',
    type: 'coastal',
    ownershipType: 'club',
    location: 'Bay E, Rack 4',
    imageUrl: '/boats/coastal-explorer.jpg',
    skillLevel: 2,
    status: 'available',
  },
  // New test data - more diverse scenarios
  {
    id: generateId('watercraft'),
    name: 'Member\'s Pride',
    type: 'boat',
    ownershipType: 'member',
    memberId: 'user-001',
    location: 'Private Storage A',
    imageUrl: '/boats/members-pride.jpg',
    skillLevel: 3,
    status: 'available',
  },
  {
    id: generateId('watercraft'),
    name: 'Regatta Star',
    type: 'boat',
    ownershipType: 'club',
    location: 'Competition Area',
    imageUrl: '/boats/regatta-star.jpg',
    skillLevel: 3,
    status: 'regatta',
  },
  {
    id: generateId('watercraft'),
    name: 'Ocean Cruiser',
    type: 'surfski',
    ownershipType: 'member',
    memberId: 'user-002',
    location: 'Private Storage B',
    imageUrl: '/boats/ocean-cruiser.jpg',
    skillLevel: 2,
    status: 'maintenance',
  },
  {
    id: generateId('watercraft'),
    name: 'Beginner\'s Friend',
    type: 'kayak',
    ownershipType: 'club',
    location: 'Training Area',
    imageUrl: '/boats/beginners-friend.jpg',
    skillLevel: 0,
    status: 'in-use',
  },
  {
    id: generateId('watercraft'),
    name: 'Coastal Rover',
    type: 'coastal',
    ownershipType: 'member',
    memberId: 'user-004',
    location: 'Member Section',
    imageUrl: '/boats/coastal-rover.jpg',
    skillLevel: 1,
    status: 'regatta',
  },
  {
    id: generateId('watercraft'),
    name: 'Rescue Patrol',
    type: 'launch',
    ownershipType: 'club',
    location: 'Emergency Dock',
    imageUrl: '/boats/rescue-patrol.jpg',
    skillLevel: 3,
    status: 'available',
  },
];
