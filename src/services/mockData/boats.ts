
import { Boat } from '@/types';
import { generateId } from '@/utils/idGenerator';

// Mock Boat Data (extending Watercraft)
export const mockBoats: Boat[] = [
  {
    id: generateId('watercraft'),
    name: 'Victory Sweep',
    type: 'boat',
    boatType: '8+',
    ownershipType: 'club',
    location: 'Bay E, Rack 1',
    imageUrl: '/boats/victory-sweep.jpg',
    skillLevel: 2,
    status: 'available',
    weightRange: {
      min: 75,
      max: 85,
    },
  },
  {
    id: generateId('watercraft'),
    name: 'Precision Single',
    type: 'boat',
    boatType: '1x',
    ownershipType: 'club',
    location: 'Bay E, Rack 2',
    imageUrl: '/boats/precision-single.jpg',
    skillLevel: 1,
    status: 'available',
    weightRange: {
      min: 65,
      max: 75,
    },
  },
  {
    id: generateId('watercraft'),
    name: 'Elite Double',
    type: 'boat',
    boatType: '2x',
    ownershipType: 'club',
    location: 'Bay F, Rack 1',
    imageUrl: '/boats/elite-double.jpg',
    skillLevel: 2,
    status: 'regatta',
    weightRange: {
      min: 70,
      max: 80,
    },
  },
  {
    id: generateId('watercraft'),
    name: 'Team Quad',
    type: 'boat',
    boatType: '4x',
    ownershipType: 'club',
    location: 'Bay F, Rack 3',
    imageUrl: '/boats/team-quad.jpg',
    skillLevel: 2,
    status: 'available',
    weightRange: {
      min: 70,
      max: 85,
    },
  },
  // New test data - more boat types
  {
    id: generateId('watercraft'),
    name: 'Lightweight Pair',
    type: 'boat',
    boatType: '2-',
    ownershipType: 'club',
    location: 'Bay G, Rack 1',
    imageUrl: '/boats/lightweight-pair.jpg',
    skillLevel: 2,
    status: 'available',
    weightRange: {
      min: 60,
      max: 70,
    },
  },
  {
    id: generateId('watercraft'),
    name: 'Competition Four',
    type: 'boat',
    boatType: '4+',
    ownershipType: 'club',
    location: 'Competition Storage',
    imageUrl: '/boats/competition-four.jpg',
    skillLevel: 3,
    status: 'regatta',
    weightRange: {
      min: 75,
      max: 90,
    },
  },
  {
    id: generateId('watercraft'),
    name: 'Triple Scull',
    type: 'boat',
    boatType: '3x',
    ownershipType: 'member',
    memberId: 'user-006',
    location: 'Private Bay 3',
    imageUrl: '/boats/triple-scull.jpg',
    skillLevel: 2,
    status: 'maintenance',
    weightRange: {
      min: 65,
      max: 80,
    },
  },
  // Changed from coastal to boat type to fix the type error
  {
    id: generateId('watercraft'),
    name: 'Coastal Single',
    type: 'boat', // Changed from 'coastal' to 'boat' to match the Boat interface
    boatType: '1x',
    ownershipType: 'club',
    location: 'Bay H, Rack 2',
    imageUrl: '/boats/coastal-single.jpg',
    skillLevel: 1,
    status: 'in-use',
    weightRange: {
      min: 70,
      max: 90,
    },
  },
];
