
import { Jaunt, UUID } from "@/types";
import { generateId } from "@/utils/idGenerator";
import { updateWatercraft } from "./watercraft.service";

// Helper for generating jaunt IDs
const generateJauntId = () => generateId('jaunt');

// Mock data for jaunts
const mockJaunts: Jaunt[] = [
  {
    id: "jaunt-001",
    userId: "user-003",
    watercraftId: "craft-005",
    startTime: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
    plannedEndTime: new Date(Date.now() + 60 * 60 * 1000), // 1 hour from now
    createdAt: new Date(Date.now() - 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 60 * 60 * 1000),
  },
  {
    id: "jaunt-002",
    userId: "user-002",
    watercraftId: "craft-002",
    startTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    plannedEndTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000), // 2 hours after start
    actualEndTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 1.5 * 60 * 60 * 1000), // 1.5 hours after start
    distanceTraveled: 5000, // 5km
    comments: "Great weather, boat performed well.",
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 1.5 * 60 * 60 * 1000),
  },
  {
    id: "jaunt-003",
    userId: "user-003",
    watercraftId: "craft-001",
    startTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    plannedEndTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 3 * 60 * 60 * 1000), // 3 hours after start
    actualEndTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 2.5 * 60 * 60 * 1000), // 2.5 hours after start
    distanceTraveled: 7500, // 7.5km
    comments: "Noticed some issues with the rudder mechanism.",
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 2.5 * 60 * 60 * 1000),
  },
];

// Get all jaunts
export const getAllJaunts = async (): Promise<Jaunt[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  return [...mockJaunts];
};

// Get jaunt by ID
export const getJauntById = async (id: UUID): Promise<Jaunt | null> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  return mockJaunts.find(jaunt => jaunt.id === id) || null;
};

// Get jaunts by user
export const getJauntsByUser = async (userId: UUID): Promise<Jaunt[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  return mockJaunts.filter(jaunt => jaunt.userId === userId);
};

// Get active jaunts by user (no actual end time)
export const getActiveJauntsByUser = async (userId: UUID): Promise<Jaunt[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  return mockJaunts.filter(jaunt => jaunt.userId === userId && !jaunt.actualEndTime);
};

// Get jaunts by watercraft
export const getJauntsByWatercraft = async (watercraftId: UUID): Promise<Jaunt[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  return mockJaunts.filter(jaunt => jaunt.watercraftId === watercraftId);
};

// Get recent jaunts by watercraft
export const getRecentJauntsByWatercraft = async (watercraftId: UUID, limit: number = 2): Promise<Jaunt[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  return mockJaunts
    .filter(jaunt => jaunt.watercraftId === watercraftId && jaunt.actualEndTime && jaunt.comments)
    .sort((a, b) => b.actualEndTime!.getTime() - a.actualEndTime!.getTime())
    .slice(0, limit);
};

// Create a new jaunt
export const createJaunt = async (jaunt: Omit<Jaunt, 'id' | 'createdAt' | 'updatedAt'>): Promise<Jaunt> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const newJaunt: Jaunt = {
    ...jaunt,
    id: generateJauntId(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  mockJaunts.push(newJaunt);
  
  // Update watercraft status to in-use
  await updateWatercraft(jaunt.watercraftId, { status: 'in-use' });
  
  return newJaunt;
};

// Update a jaunt
export const updateJaunt = async (id: UUID, updates: Partial<Jaunt>): Promise<Jaunt | null> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const index = mockJaunts.findIndex(jaunt => jaunt.id === id);
  if (index === -1) return null;
  
  // Check if we're adding an actual end time (checking in)
  const checkingIn = !mockJaunts[index].actualEndTime && updates.actualEndTime;
  
  const updatedJaunt = {
    ...mockJaunts[index],
    ...updates,
    updatedAt: new Date(),
  };
  
  mockJaunts[index] = updatedJaunt;
  
  // If checking in, update watercraft status back to available
  if (checkingIn) {
    await updateWatercraft(updatedJaunt.watercraftId, { status: 'available' });
  }
  
  return updatedJaunt;
};

// Delete a jaunt (soft delete)
export const deleteJaunt = async (id: UUID): Promise<boolean> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const index = mockJaunts.findIndex(jaunt => jaunt.id === id);
  if (index === -1) return false;
  
  mockJaunts[index] = {
    ...mockJaunts[index],
    deleted: true,
    updatedAt: new Date(),
  };
  
  return true;
};
