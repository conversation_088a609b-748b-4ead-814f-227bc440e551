
import { UUID } from './index';

export type WatercraftType = 'boat' | 'kayak' | 'PB' | 'launch' | 'surfski' | 'coastal';
export type WatercraftStatus = 'available' | 'in-use' | 'maintenance' | 'regatta';
export type OwnershipType = 'club' | 'member';
export type BoatType = '1x' | '2x' | '2-' | '4x' | '4+' | '3x' | '8+';
export type MaintenanceRequestStatus = 'open' | 'in-progress' | 'closed';
export type MaintenanceIssueType = 'missing part' | 'broken' | 'damaged';

export interface Watercraft {
  id: UUID;
  name: string;
  type: WatercraftType;
  ownershipType: OwnershipType;
  memberId?: UUID;
  location: string;
  imageUrl?: string;
  skillLevel: number;
  status: WatercraftStatus;
  isFavorite?: boolean;
  deleted?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}

export interface Boat extends Watercraft {
  type: 'boat';
  boatType: BoatType;
  weightRange?: {
    min: number;
    max: number;
  };
}

export interface MaintenanceRequest {
  id: UUID;
  watercraftId: UUID;
  requestDate: Date;
  watercraftState: 'useable' | 'unuseable';
  issueType: MaintenanceIssueType;
  note: string;
  status: MaintenanceRequestStatus;
  resolution?: string;
  resolutionDate?: Date;
  deleted?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}
