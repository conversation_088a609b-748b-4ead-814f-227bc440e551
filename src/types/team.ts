
import { UUID } from './index';

export type WeekDay = 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';

export interface PracticeSchedule {
  id: UUID;
  day: WeekDay;
  startTime: string; // Format: "HH:MM" in 24-hour format
  endTime: string; // Format: "HH:MM" in 24-hour format
  location?: string;
  notes?: string;
}

// New interface for practice attendance
export interface PracticeSignUp {
  id: UUID;
  practiceId: UUID;
  userId: UUID;
  teamId: UUID;
  status: 'attending' | 'not-attending' | 'tentative'; // Keep 'tentative' for backward compatibility
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// New interface for practice instances
export interface PracticeInstance {
  id: UUID;
  teamId: UUID;
  scheduleId: UUID; // Reference to the regular practice schedule
  date: Date;
  startTime: string;
  endTime: string;
  location?: string;
  notes?: string;
  attendees: PracticeSignUp[];
  boatAssignments?: BoatAssignment[];
  canceled?: boolean;
  cancelReason?: string;
  locked?: boolean; // New field to indicate if sign-up is locked after lineup creation
}

// New interface for boat assignments
export interface BoatAssignment {
  id: UUID;
  practiceInstanceId: UUID;
  watercraftId: UUID;
  positions: CrewPosition[];
}

// New interface for crew positions
export interface CrewPosition {
  positionNumber: number; // 0 for coxswain, 1-8 for rowers
  userId: UUID;
  role: 'coxswain' | 'rower';
}

export interface Team {
  id: UUID;
  name: string;
  memberIds: UUID[];
  coachIds?: UUID[]; // IDs of coaches assigned to this team
  email: string;
  whatsappGroupId?: string;
  practiceSignupSheetUrl?: string;
  practiceSchedules?: PracticeSchedule[]; // Regular practice schedule
  practiceInstances?: PracticeInstance[]; // Specific practice instances
  createdAt: Date;
  updatedAt: Date;
  deleted?: boolean;
}
