
export interface WeatherStationCapabilities {
  airTemperature: boolean;
  waterTemperature: boolean;
  wind: boolean;
  waterLevel: boolean;
  predictions: boolean;
  barometricPressure: boolean;
  visibility: boolean;
}

export interface WeatherStation {
  id: string;
  name: string;
  lat: number;
  lon: number;
  state?: string;
  distance?: number;
  capabilities?: WeatherStationCapabilities;
}

export interface WeatherData {
  timestamp: string;
  temperature: number;
  waterTemperature?: number;
  windSpeed: number;
  windGust: number;
  windDirection: number;
  tideLevel?: number;
  lightning: boolean;
}

export interface HourlyForecast {
  hours: WeatherData[];
}

export interface WeatherAlert {
  id: string;
  title: string;
  description: string;
  severity: 'minor' | 'moderate' | 'severe' | 'extreme';
  urgency: 'immediate' | 'expected' | 'future' | 'past';
  certainty: 'observed' | 'likely' | 'possible' | 'unlikely';
  event: string;
  effective: string;
  expires: string;
  areas: string[];
}

export interface WeatherAlerts {
  alerts: WeatherAlert[];
  lastUpdated: string;
}

export type WeatherUnit = 'metric' | 'imperial';
