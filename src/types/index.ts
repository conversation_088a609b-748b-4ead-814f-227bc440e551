
export type UUID = string;

export * from './watercraft';
export * from './user';
export * from './jaunt';
export * from './team';
export * from './language';

// Updated WatercraftFilters type with all required properties
export interface WatercraftFilters {
  search: string;
  type: string[];
  boatTypes?: string[];
  status: string[];
  showFavoritesOnly: boolean;
  skillLevel?: [number, number];
  weightRange?: [number, number];
}

// Skill types used in user profile
export type SkillType = "Rowing" | "Coaching" | "Maintenance" | "Coxswain";
