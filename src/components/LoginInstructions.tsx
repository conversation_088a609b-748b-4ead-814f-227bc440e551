
import { mockUsers } from "@/services/mockData/users";
import { User } from "@/types";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Copy } from "lucide-react";
import { toast } from "sonner";

const LoginInstructions = () => {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("Email copied to clipboard");
  };

  const getRoleBadge = (role: User["role"]) => {
    switch (role) {
      case "admin":
        return <Badge className="bg-red-500 hover:bg-red-600">Admin</Badge>;
      case "coach":
        return <Badge className="bg-blue-500 hover:bg-blue-600">Coach</Badge>;
      case "member":
        return <Badge className="bg-green-500 hover:bg-green-600">Member</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Demo User Accounts</CardTitle>
        <CardDescription>
          Use any of these accounts to log in to the application. No password is required in demo mode.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Language</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell className="font-medium">{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{getRoleBadge(user.role)}</TableCell>
                  <TableCell className="uppercase">{user.language}</TableCell>
                  <TableCell>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => copyToClipboard(user.email)}
                      className="flex items-center gap-1"
                    >
                      <Copy className="h-3 w-3" />
                      Copy
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <div className="mt-4 p-4 bg-muted rounded-md">
          <h4 className="font-semibold mb-2">How to log in:</h4>
          <ol className="list-decimal list-inside space-y-2">
            <li>Go to the login page</li>
            <li>Enter any email from the list above</li>
            <li>Use any text as password (passwords are not validated in demo mode)</li>
            <li>Click Login</li>
          </ol>
          <p className="mt-4 text-sm text-muted-foreground">
            Different roles have different permissions. Try logging in with different accounts to see the differences.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default LoginInstructions;
