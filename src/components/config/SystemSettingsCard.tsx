
import { AppConfig } from '@/context/ConfigContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

interface SystemSettingsCardProps {
  config: AppConfig;
  onConfigChange: (key: keyof AppConfig) => void;
}

export const SystemSettingsCard = ({ config, onConfigChange }: SystemSettingsCardProps) => {
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>System Settings</CardTitle>
        <CardDescription>
          Advanced system configuration options
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="debugMode">Debug Mode</Label>
            <p className="text-sm text-muted-foreground">Enable detailed logging and debug information</p>
          </div>
          <Switch
            id="debugMode"
            checked={config.debugMode}
            onCheckedChange={() => onConfigChange('debugMode')}
          />
        </div>
      </CardContent>
    </Card>
  );
};
