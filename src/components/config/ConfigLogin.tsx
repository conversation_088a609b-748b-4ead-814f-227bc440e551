
import { useState } from 'react';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { LockKeyhole, GithubIcon, FacebookIcon, TwitterIcon, Mail, Apple } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

interface ConfigLoginProps {
  onLogin: () => void;
  password: string;
  setPassword: (password: string) => void;
  configPassword: string;
}

export const ConfigLogin = ({ onLogin, password, setPassword, configPassword }: ConfigLoginProps) => {
  const [passwordError, setPasswordError] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleLogin = () => {
    if (password === configPassword) {
      setPasswordError(false);
      toast.success('Access granted to configuration');
      onLogin();
    } else {
      setPasswordError(true);
      toast.error('Invalid password');
    }
  };

  const handleSocialLogin = (provider: string) => {
    setLoading(true);
    // In a real implementation, this would authenticate with the provider
    // For demo purposes, we'll just grant access
    setTimeout(() => {
      toast.success(`Access granted via ${provider}`);
      onLogin();
      setLoading(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-4">
            <LockKeyhole className="h-12 w-12 text-marine-600" />
          </div>
          <CardTitle className="text-2xl text-center">Configuration Access</CardTitle>
          <CardDescription className="text-center">
            Enter the password to access system configuration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input 
                id="password" 
                type="password" 
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleLogin()}
                className={passwordError ? "border-red-500" : ""}
              />
              {passwordError && (
                <p className="text-sm text-red-500">Incorrect password</p>
              )}
            </div>
            <Button 
              className="w-full" 
              onClick={handleLogin}
              disabled={loading}
            >
              Access Configuration
            </Button>
            
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <Separator className="w-full" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-white px-2 text-muted-foreground">
                  Or continue with
                </span>
              </div>
            </div>
            
            <div className="grid grid-cols-5 gap-2">
              <Button 
                variant="outline" 
                type="button" 
                onClick={() => handleSocialLogin('Github')}
                disabled={loading}
                className="flex items-center justify-center"
              >
                <GithubIcon className="h-5 w-5" />
              </Button>
              <Button 
                variant="outline" 
                type="button" 
                onClick={() => handleSocialLogin('Facebook')}
                disabled={loading}
                className="flex items-center justify-center"
              >
                <FacebookIcon className="h-5 w-5" />
              </Button>
              <Button 
                variant="outline" 
                type="button" 
                onClick={() => handleSocialLogin('Twitter')}
                disabled={loading}
                className="flex items-center justify-center"
              >
                <TwitterIcon className="h-5 w-5" />
              </Button>
              <Button 
                variant="outline" 
                type="button" 
                onClick={() => handleSocialLogin('Google')}
                disabled={loading}
                className="flex items-center justify-center"
              >
                <Mail className="h-5 w-5" />
              </Button>
              <Button 
                variant="outline" 
                type="button" 
                onClick={() => handleSocialLogin('Apple')}
                disabled={loading}
                className="flex items-center justify-center"
              >
                <Apple className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
