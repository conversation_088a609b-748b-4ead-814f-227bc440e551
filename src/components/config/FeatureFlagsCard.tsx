
import { AppConfig } from '@/context/ConfigContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';

interface FeatureFlagsCardProps {
  config: AppConfig;
  onConfigChange: (key: keyof AppConfig) => void;
}

export const FeatureFlagsCard = ({ config, onConfigChange }: FeatureFlagsCardProps) => {
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Feature Flags</CardTitle>
        <CardDescription>
          Enable or disable specific features in the application
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <FeatureToggle
          id="teamCreation"
          label="Team Creation"
          description="Allow users to create new teams"
          checked={config.enableTeamCreation}
          onChange={() => onConfigChange('enableTeamCreation')}
        />
        
        <Separator />
        
        <FeatureToggle
          id="memberSignUp"
          label="Member Sign-up"
          description="Allow members to sign up for practices and coaches to manage lineups"
          checked={config.enableMemberSignUp}
          onChange={() => onConfigChange('enableMemberSignUp')}
        />
        
        <Separator />
        
        <FeatureToggle
          id="advancedScheduling"
          label="Advanced Scheduling"
          description="Enable advanced scheduling features"
          checked={config.enableAdvancedScheduling}
          onChange={() => onConfigChange('enableAdvancedScheduling')}
        />
        
        <Separator />
        
        <FeatureToggle
          id="weatherAlerts"
          label="Weather Alerts"
          description="Enable weather alert notifications"
          checked={config.enableWeatherAlerts}
          onChange={() => onConfigChange('enableWeatherAlerts')}
        />
        
        <Separator />
        
        <FeatureToggle
          id="maintenanceRequests"
          label="Maintenance Requests"
          description="Allow users to submit maintenance requests"
          checked={config.enableMaintenanceRequests}
          onChange={() => onConfigChange('enableMaintenanceRequests')}
        />
      </CardContent>
    </Card>
  );
};

interface FeatureToggleProps {
  id: string;
  label: string;
  description: string;
  checked: boolean;
  onChange: () => void;
}

const FeatureToggle = ({ id, label, description, checked, onChange }: FeatureToggleProps) => (
  <div className="flex items-center justify-between">
    <div className="space-y-0.5">
      <Label htmlFor={id}>{label}</Label>
      <p className="text-sm text-muted-foreground">{description}</p>
    </div>
    <Switch
      id={id}
      checked={checked}
      onCheckedChange={onChange}
    />
  </div>
);
