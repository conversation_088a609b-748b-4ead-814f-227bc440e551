
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { X } from 'lucide-react';
import { User } from '@/types';
import { useLanguage } from '@/context/LanguageContext';
import { getAvatarFallback } from '@/utils/avatar';

interface CoachListProps {
  coaches: User[];
  onRemoveCoach: (coachId: string) => void;
  readOnly?: boolean;
}

export const CoachList = ({ coaches, onRemoveCoach, readOnly = false }: CoachListProps) => {
  const { t } = useLanguage();
  
  return (
    <div className="space-y-4">
      {coaches.map(coach => (
        <div 
          key={coach.id} 
          className="flex items-center justify-between p-2 rounded-md border"
        >
          <div className="flex items-center gap-3">
            <Avatar>
              <AvatarFallback>{getAvatarFallback(coach.name)}</AvatarFallback>
              <AvatarImage src={`https://avatars.dicebear.com/api/initials/${coach.name}.svg`} />
            </Avatar>
            <div>
              <p className="font-medium">{coach.name}</p>
              <p className="text-sm text-muted-foreground">{coach.email}</p>
            </div>
          </div>
          {!readOnly && (
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => onRemoveCoach(coach.id)}
              aria-label={t('remove_coach')}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      ))}
    </div>
  );
};
