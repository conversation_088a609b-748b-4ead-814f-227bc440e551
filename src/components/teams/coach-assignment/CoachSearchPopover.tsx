
import { useState } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Command, 
  CommandEmpty, 
  CommandGroup, 
  CommandInput, 
  CommandItem, 
  CommandList 
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ChevronsUpDown, Check } from 'lucide-react';
import { User } from '@/types';
import { getAvatarFallback } from '@/utils/avatar';

interface CoachSearchPopoverProps {
  coaches: User[];
  assignedCoaches: User[];
  onAssignCoach: (coach: User) => void;
}

export const CoachSearchPopover = ({ 
  coaches, 
  assignedCoaches,
  onAssignCoach
}: CoachSearchPopoverProps) => {
  const { t } = useLanguage();
  const [searchOpen, setSearchOpen] = useState(false);

  return (
    <Popover open={searchOpen} onOpenChange={setSearchOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="w-full justify-between">
          {t('assign_coach')}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0" align="start" side="bottom">
        <Command>
          <CommandInput placeholder={t('search_coaches')} />
          <CommandList>
            <CommandEmpty>{t('no_coaches_found')}</CommandEmpty>
            <CommandGroup>
              <ScrollArea className="h-[200px]">
                {coaches.map(coach => (
                  <CommandItem
                    key={coach.id}
                    onSelect={() => {
                      onAssignCoach(coach);
                      setSearchOpen(false);
                    }}
                    className="flex items-center gap-2"
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="text-xs">{getAvatarFallback(coach.name)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 overflow-hidden">
                      <p className="truncate">{coach.name}</p>
                      <p className="text-xs text-muted-foreground truncate">{coach.email}</p>
                    </div>
                    <Check
                      className={`h-4 w-4 ${assignedCoaches.some(c => c.id === coach.id) ? 'opacity-100' : 'opacity-0'}`}
                    />
                  </CommandItem>
                ))}
              </ScrollArea>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
