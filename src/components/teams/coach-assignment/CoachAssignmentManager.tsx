import { useState, useEffect } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardFooter 
} from '@/components/ui/card';
import { UserCog } from 'lucide-react';
import { Team, User } from '@/types';
import { toast } from 'sonner';
import { CoachList } from './CoachList';
import { CoachSearchPopover } from './CoachSearchPopover';
import { CoachEmptyState } from './CoachEmptyState';

const fetchCoaches = async (): Promise<User[]> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return [
    {
      id: 'usr-1',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '************',
      role: 'coach',
      skills: ['Coaching', 'Rowing'],
      permissions: [{ watercraftType: 'boat', skillLevel: 3 }],
      language: 'en',
      favorites: [],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'usr-2',
      name: '<PERSON>',
      email: 'micha<PERSON>.<EMAIL>',
      phone: '************',
      role: 'coach',
      skills: ['Coaching', '<PERSON>swain'],
      permissions: [{ watercraftType: 'boat', skillLevel: 2 }],
      language: 'en',
      favorites: [],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'usr-3',
      name: '<PERSON> Williams',
      email: '<EMAIL>',
      phone: '************',
      role: 'coach',
      skills: ['Coaching', 'Rowing', 'Maintenance'],
      permissions: [{ watercraftType: 'boat', skillLevel: 3 }],
      language: 'en',
      favorites: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];
};

interface CoachAssignmentManagerProps {
  team: Team;
  onUpdate: (updatedCoaches: string[]) => void;
  readOnly?: boolean;
}

export const CoachAssignmentManager = ({ team, onUpdate, readOnly = false }: CoachAssignmentManagerProps) => {
  const { t } = useLanguage();
  const [coaches, setCoaches] = useState<User[]>([]);
  const [assignedCoaches, setAssignedCoaches] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadCoaches = async () => {
      setIsLoading(true);
      try {
        const allCoaches = await fetchCoaches();
        setCoaches(allCoaches);
        
        if (team.coachIds) {
          const teamCoaches = allCoaches.filter(coach => 
            team.coachIds?.includes(coach.id)
          );
          setAssignedCoaches(teamCoaches);
        }
      } catch (error) {
        console.error('Error loading coaches:', error);
        toast.error(t('error_loading_coaches'));
      } finally {
        setIsLoading(false);
      }
    };
    
    loadCoaches();
  }, [team.coachIds, t]);

  const handleAssignCoach = (coach: User) => {
    if (assignedCoaches.some(c => c.id === coach.id)) return;
    
    const updatedCoaches = [...assignedCoaches, coach];
    setAssignedCoaches(updatedCoaches);
    
    onUpdate(updatedCoaches.map(c => c.id));
    
    toast.success(t('coach_assigned'));
  };

  const handleRemoveCoach = (coachId: string) => {
    const updatedCoaches = assignedCoaches.filter(c => c.id !== coachId);
    setAssignedCoaches(updatedCoaches);
    
    onUpdate(updatedCoaches.map(c => c.id));
    
    toast.success(t('coach_removed'));
  };

  const unassignedCoaches = coaches.filter(
    coach => !assignedCoaches.some(ac => ac.id === coach.id)
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserCog className="h-5 w-5 text-marine-600" />
          {t('team_coaches')}
        </CardTitle>
        <CardDescription>
          {t('team_coaches_description')}
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-4">
            <div className="animate-pulse">{t('loading')}</div>
          </div>
        ) : assignedCoaches.length > 0 ? (
          <CoachList 
            coaches={assignedCoaches} 
            onRemoveCoach={handleRemoveCoach} 
            readOnly={readOnly} 
          />
        ) : (
          <CoachEmptyState />
        )}
      </CardContent>
      
      {!readOnly && (
        <CardFooter>
          <CoachSearchPopover 
            coaches={unassignedCoaches} 
            assignedCoaches={assignedCoaches}
            onAssignCoach={handleAssignCoach} 
          />
        </CardFooter>
      )}
    </Card>
  );
};
