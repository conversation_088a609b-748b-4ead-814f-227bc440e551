
import { useState } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON><PERSON>Title, DialogFooter, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { UserPlus } from 'lucide-react';

interface CreateTeamFormData {
  name: string;
  email: string;
  practiceSheetUrl: string;
}

interface CreateTeamDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateTeam: (formData: CreateTeamFormData) => void;
}

export const CreateTeamDialog = ({ open, onOpenChange, onCreateTeam }: CreateTeamDialogProps) => {
  const { t } = useLanguage();
  const [teamName, setTeamName] = useState('');
  const [teamEmail, setTeamEmail] = useState('');
  const [practiceSheetUrl, setPracticeSheetUrl] = useState('');

  const handleSubmit = () => {
    onCreateTeam({
      name: teamName,
      email: teamEmail,
      practiceSheetUrl: practiceSheetUrl
    });
    
    // Reset form
    setTeamName('');
    setTeamEmail('');
    setPracticeSheetUrl('');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button>
          <UserPlus className="mr-2 h-4 w-4" />
          {t('create_team')}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('create_new_team')}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="teamName">{t('team_name')}</Label>
            <Input
              id="teamName"
              value={teamName}
              onChange={(e) => setTeamName(e.target.value)}
              placeholder={t('enter_team_name')}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="teamEmail">{t('team_email')}</Label>
            <Input
              id="teamEmail"
              type="email"
              value={teamEmail}
              onChange={(e) => setTeamEmail(e.target.value)}
              placeholder={t('enter_team_email')}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="practiceSheet">
              {t('practice_signup_sheet_url')}
              <span className="text-muted-foreground text-xs ml-1">({t('optional')})</span>
            </Label>
            <Input
              id="practiceSheet"
              value={practiceSheetUrl}
              onChange={(e) => setPracticeSheetUrl(e.target.value)}
              placeholder="https://example.com/sheets/practice"
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t('cancel')}
          </Button>
          <Button onClick={handleSubmit}>
            {t('create_team')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
