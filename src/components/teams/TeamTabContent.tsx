
import { useLanguage } from '@/context/LanguageContext';
import { TeamList } from './TeamList';
import { Team } from '@/types';

interface TeamTabContentProps {
  teams: Team[];
  loading: boolean;
  onCreateTeamClick: () => void;
  isOtherTeams?: boolean;
  readOnly?: boolean;
  onTeamClick?: (team: Team) => void;
}

export const TeamTabContent = ({ 
  teams, 
  loading, 
  onCreateTeamClick, 
  isOtherTeams = false, 
  readOnly = false,
  onTeamClick
}: TeamTabContentProps) => {
  const { t } = useLanguage();

  if (loading) {
    return <TeamList teams={[]} loading={true} onCreateTeamClick={onCreateTeamClick} readOnly={readOnly} />;
  }

  if (teams.length === 0 && isOtherTeams) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">{t('no_other_teams')}</p>
      </div>
    );
  }

  return (
    <TeamList 
      teams={teams} 
      loading={false} 
      onCreateTeamClick={onCreateTeamClick} 
      readOnly={readOnly} 
      onTeamClick={onTeamClick}
    />
  );
};
