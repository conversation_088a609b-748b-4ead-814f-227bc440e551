
import { Team } from '@/types';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, Send, LinkIcon, Calendar, Copy } from 'lucide-react';
import { useLanguage } from '@/context/LanguageContext';
import { toast } from 'sonner';

interface TeamCardProps {
  team: Team;
  readOnly?: boolean;
  onClick?: (team: Team) => void;
}

export const TeamCard = ({ team, readOnly = false, onClick }: TeamCardProps) => {
  const { t } = useLanguage();

  const copyToClipboard = (text: string, successMessage: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        toast.success(successMessage);
      },
      () => {
        toast.error(t('failed_to_copy'));
      }
    );
  };

  // Helper to render coach count if available
  const renderCoachCount = () => {
    if (!team.coachIds || team.coachIds.length === 0) return null;
    
    return (
      <Badge variant="outline" className="ml-2">
        {team.coachIds.length} {team.coachIds.length === 1 ? t('coach') : t('coaches')}
      </Badge>
    );
  };

  // Helper to render practice count if available
  const renderPracticeCount = () => {
    if (!team.practiceSchedules || team.practiceSchedules.length === 0) return null;
    
    return (
      <Badge variant="outline" className="ml-2">
        {team.practiceSchedules.length} {t('practices')}
      </Badge>
    );
  };

  return (
    <Card 
      className="animate-in fade-in hover:shadow-md transition-shadow cursor-pointer"
      onClick={() => onClick && onClick(team)}
    >
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5 text-marine-600" />
          {team.name}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="pb-4">
        <div className="grid grid-cols-1 gap-3 text-sm">
          <div>
            <p className="text-gray-500">{t('team_members')}</p>
            <p className="font-medium">
              {team.memberIds.length} {t('members')}
              {renderCoachCount()}
              {renderPracticeCount()}
            </p>
          </div>
          
          <div>
            <p className="text-gray-500">{t('team_email')}</p>
            <div className="flex items-center gap-1">
              <p className="font-medium truncate">{team.email}</p>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-6 w-6" 
                onClick={(e) => {
                  e.stopPropagation(); // Prevent card click event
                  copyToClipboard(team.email, t('email_copied'));
                }}
              >
                <Copy className="h-3 w-3" />
              </Button>
            </div>
          </div>
          
          {team.whatsappGroupId && (
            <div>
              <p className="text-gray-500">{t('whatsapp_group')}</p>
              <Badge variant="outline" className="gap-1">
                <Send className="h-3 w-3" />
                {t('available')}
              </Badge>
            </div>
          )}
          
          {team.practiceSignupSheetUrl && (
            <div>
              <p className="text-gray-500">{t('practice_signup_sheet')}</p>
              <div className="flex items-center gap-1">
                <Badge variant="outline" className="gap-1">
                  <Calendar className="h-3 w-3" />
                  {t('available')}
                </Badge>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-6 w-6" 
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent card click event
                    copyToClipboard(team.practiceSignupSheetUrl!, t('url_copied'));
                  }}
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
      
      <CardFooter className="pt-0 flex gap-2">
        {team.practiceSignupSheetUrl && (
          <Button 
            variant="outline" 
            size="sm" 
            className="gap-1 flex-1" 
            onClick={(e) => {
              e.stopPropagation(); // Prevent card click event
              window.open(team.practiceSignupSheetUrl, '_blank');
            }}
          >
            <LinkIcon className="h-4 w-4" />
            {t('open_sheet')}
          </Button>
        )}
        {!readOnly && (
          <Button 
            size="sm" 
            className="gap-1 flex-1"
            onClick={(e) => {
              e.stopPropagation(); // Let the parent handle this
              onClick && onClick(team);
            }}
          >
            <Users className="h-4 w-4" />
            {t('manage')}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};
