
import { useState, useEffect } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { PracticeSchedule, WeekDay } from '@/types';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogFooter 
} from '@/components/ui/dialog';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

interface PracticeScheduleFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (scheduleData: {
    id?: string;
    day: WeekDay;
    startTime: string;
    endTime: string;
    location?: string;
    notes?: string;
  }) => void;
  editingSchedule: PracticeSchedule | null;
}

export const PracticeScheduleForm = ({ 
  isOpen, 
  onClose, 
  onSubmit, 
  editingSchedule 
}: PracticeScheduleFormProps) => {
  const { t } = useLanguage();
  
  const [day, setDay] = useState<WeekDay>('monday');
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [location, setLocation] = useState('');
  const [notes, setNotes] = useState('');

  // Initialize form when editing schedule changes
  useEffect(() => {
    if (editingSchedule) {
      setDay(editingSchedule.day);
      setStartTime(editingSchedule.startTime);
      setEndTime(editingSchedule.endTime);
      setLocation(editingSchedule.location || '');
      setNotes(editingSchedule.notes || '');
    } else {
      resetForm();
    }
  }, [editingSchedule, isOpen]);

  const resetForm = () => {
    setDay('monday');
    setStartTime('');
    setEndTime('');
    setLocation('');
    setNotes('');
  };

  const handleSubmit = () => {
    // Validate form
    if (!day || !startTime || !endTime) {
      toast.error(t('practice_schedule_required_fields'));
      return;
    }

    onSubmit({
      id: editingSchedule?.id,
      day,
      startTime,
      endTime,
      location: location || undefined,
      notes: notes || undefined
    });

    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {editingSchedule ? t('edit_practice') : t('add_practice')}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="day">{t('day')}</Label>
            <Select value={day} onValueChange={(value) => setDay(value as WeekDay)}>
              <SelectTrigger id="day">
                <SelectValue placeholder={t('select_day')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="monday">{t('monday')}</SelectItem>
                <SelectItem value="tuesday">{t('tuesday')}</SelectItem>
                <SelectItem value="wednesday">{t('wednesday')}</SelectItem>
                <SelectItem value="thursday">{t('thursday')}</SelectItem>
                <SelectItem value="friday">{t('friday')}</SelectItem>
                <SelectItem value="saturday">{t('saturday')}</SelectItem>
                <SelectItem value="sunday">{t('sunday')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startTime">{t('start_time')}</Label>
              <Input 
                id="startTime" 
                type="time" 
                value={startTime} 
                onChange={(e) => setStartTime(e.target.value)} 
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="endTime">{t('end_time')}</Label>
              <Input 
                id="endTime" 
                type="time" 
                value={endTime} 
                onChange={(e) => setEndTime(e.target.value)} 
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="location">
              {t('location')}
              <span className="text-muted-foreground text-xs ml-1">({t('optional')})</span>
            </Label>
            <Input 
              id="location" 
              value={location} 
              onChange={(e) => setLocation(e.target.value)} 
              placeholder={t('enter_practice_location')}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="notes">
              {t('notes')}
              <span className="text-muted-foreground text-xs ml-1">({t('optional')})</span>
            </Label>
            <Input 
              id="notes" 
              value={notes} 
              onChange={(e) => setNotes(e.target.value)} 
              placeholder={t('enter_practice_notes')}
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            {t('cancel')}
          </Button>
          <Button onClick={handleSubmit}>
            {editingSchedule ? t('update') : t('add')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
