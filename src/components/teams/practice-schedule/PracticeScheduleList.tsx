
import { useLanguage } from '@/context/LanguageContext';
import { PracticeSchedule, WeekDay } from '@/types';
import { 
  Table, 
  TableHeader, 
  TableRow, 
  TableHead, 
  TableBody, 
  TableCell 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Edit, Trash2 } from 'lucide-react';

interface PracticeScheduleListProps {
  schedules: PracticeSchedule[];
  onEdit: (schedule: PracticeSchedule) => void;
  onDelete: (scheduleId: string) => void;
  readOnly: boolean;
}

export const PracticeScheduleList = ({ 
  schedules, 
  onEdit, 
  onDelete, 
  readOnly 
}: PracticeScheduleListProps) => {
  const { t } = useLanguage();

  const formatDayName = (day: WeekDay): string => {
    return t(day);
  };

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>{t('day')}</TableHead>
          <TableHead>{t('time')}</TableHead>
          <TableHead>{t('location')}</TableHead>
          <TableHead>{t('notes')}</TableHead>
          {!readOnly && <TableHead>{t('actions')}</TableHead>}
        </TableRow>
      </TableHeader>
      <TableBody>
        {schedules.map((schedule) => (
          <TableRow key={schedule.id}>
            <TableCell className="font-medium">{formatDayName(schedule.day)}</TableCell>
            <TableCell>{schedule.startTime} - {schedule.endTime}</TableCell>
            <TableCell>{schedule.location || '-'}</TableCell>
            <TableCell>{schedule.notes || '-'}</TableCell>
            {!readOnly && (
              <TableCell>
                <div className="flex items-center gap-2">
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={() => onEdit(schedule)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={() => onDelete(schedule.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            )}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};
