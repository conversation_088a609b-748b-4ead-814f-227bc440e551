
import { useState } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription, 
  CardFooter 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PlusCircle, CalendarClock } from 'lucide-react';
import { PracticeSchedule, Team, WeekDay } from '@/types';
import { toast } from 'sonner';
import { generateId } from '@/utils/idGenerator';
import { PracticeScheduleList } from './PracticeScheduleList';
import { PracticeScheduleForm } from './PracticeScheduleForm';
import { PracticeScheduleEmptyState } from './PracticeScheduleEmptyState';

interface PracticeScheduleManagerProps {
  team: Team;
  onUpdate: (updatedTeam: Team) => void;
  readOnly?: boolean;
}

export const PracticeScheduleManager = ({ team, onUpdate, readOnly = false }: PracticeScheduleManagerProps) => {
  const { t } = useLanguage();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingSchedule, setEditingSchedule] = useState<PracticeSchedule | null>(null);
  
  const openAddDialog = () => {
    setEditingSchedule(null);
    setDialogOpen(true);
  };

  const openEditDialog = (schedule: PracticeSchedule) => {
    setEditingSchedule(schedule);
    setDialogOpen(true);
  };

  const handleSubmit = (scheduleData: {
    id?: string;
    day: WeekDay;
    startTime: string;
    endTime: string;
    location?: string;
    notes?: string;
  }) => {
    const schedules = [...(team.practiceSchedules || [])];
    
    if (scheduleData.id) {
      // Update existing schedule
      const index = schedules.findIndex(s => s.id === scheduleData.id);
      if (index !== -1) {
        schedules[index] = {
          ...schedules[index],
          day: scheduleData.day,
          startTime: scheduleData.startTime,
          endTime: scheduleData.endTime,
          location: scheduleData.location,
          notes: scheduleData.notes
        };
      }
    } else {
      // Add new schedule
      schedules.push({
        id: generateId('psc'),
        day: scheduleData.day,
        startTime: scheduleData.startTime,
        endTime: scheduleData.endTime,
        location: scheduleData.location,
        notes: scheduleData.notes
      });
    }

    // Sort schedules by day and start time
    schedules.sort((a, b) => {
      const dayOrder = [
        'monday', 'tuesday', 'wednesday', 'thursday', 
        'friday', 'saturday', 'sunday'
      ];
      const dayDiff = dayOrder.indexOf(a.day) - dayOrder.indexOf(b.day);
      if (dayDiff !== 0) return dayDiff;
      return a.startTime.localeCompare(b.startTime);
    });

    // Update the team with the modified schedules
    const updatedTeam = {
      ...team,
      practiceSchedules: schedules  // Using practiceSchedules instead of practiceSchedule
    };

    onUpdate(updatedTeam);

    toast.success(scheduleData.id ? t('practice_updated') : t('practice_added'));
  };

  const handleDelete = (scheduleId: string) => {
    if (!team.practiceSchedules) return;
    
    const updatedSchedules = team.practiceSchedules.filter(s => s.id !== scheduleId);
    
    // Update the team with filtered schedules
    const updatedTeam = {
      ...team,
      practiceSchedules: updatedSchedules  // Using practiceSchedules instead of practiceSchedule
    };

    onUpdate(updatedTeam);
    
    toast.success(t('practice_deleted'));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CalendarClock className="h-5 w-5 text-marine-600" />
          {t('practice_schedule')}
        </CardTitle>
        <CardDescription>
          {t('team_practice_schedule_description')}
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {team.practiceSchedules && team.practiceSchedules.length > 0 ? (
          <PracticeScheduleList 
            schedules={team.practiceSchedules}
            onEdit={openEditDialog}
            onDelete={handleDelete}
            readOnly={readOnly}
          />
        ) : (
          <PracticeScheduleEmptyState />
        )}
      </CardContent>
      
      {!readOnly && (
        <CardFooter>
          <Button onClick={openAddDialog}>
            <PlusCircle className="mr-2 h-4 w-4" />
            {t('add_practice')}
          </Button>
        </CardFooter>
      )}
      
      <PracticeScheduleForm
        isOpen={dialogOpen}
        onClose={() => setDialogOpen(false)}
        onSubmit={handleSubmit}
        editingSchedule={editingSchedule}
      />
    </Card>
  );
};
