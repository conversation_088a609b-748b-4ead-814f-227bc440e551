
// Mock user data - in a real app, this would be fetched from an API
export const mockAllUsers = [
  { id: "user-001", name: "<PERSON>", email: "<EMAIL>" },
  { id: "user-002", name: "<PERSON>", email: "<EMAIL>" },
  { id: "user-003", name: "<PERSON>", email: "<EMAIL>" },
  { id: "user-004", name: "<PERSON>", email: "<EMAIL>" },
  { id: "user-005", name: "<PERSON>", email: "<EMAIL>" },
  { id: "user-006", name: "<PERSON>", email: "<EMAIL>" },
  { id: "user-007", name: "<PERSON>", email: "<EMAIL>" },
  { id: "user-008", name: "<PERSON>", email: "<EMAIL>" },
  // Add missing user IDs that are referenced in the mock teams data
  { id: "user-009", name: "<PERSON>", email: "<EMAIL>" },
  { id: "user-010", name: "<PERSON>", email: "<EMAIL>" },
  { id: "user-011", name: "<PERSON>", email: "<EMAIL>" },
];
