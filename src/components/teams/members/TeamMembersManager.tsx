
import { useState } from "react";
import { useLanguage } from "@/context/LanguageContext";
import { Team, UUID } from "@/types";
import { toast } from "sonner";
import { 
  MemberSearchBar, 
  MembersList,
  AddMemberDialog,
  AddMemberButton
} from "./components";
import { mockAllUsers } from "./mockUsers";
import { TranslatedHeading, TranslatedText } from "@/components/i18n";

interface TeamMembersManagerProps {
  team: Team;
  onTeamUpdate: (updatedTeam: Team) => void;
  readOnly?: boolean;
}

export const TeamMembersManager = ({
  team,
  onTeamUpdate,
  readOnly = false,
}: TeamMembersManagerProps) => {
  const { t } = useLanguage();
  const [searchTerm, setSearchTerm] = useState("");
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [userSearchTerm, setUserSearchTerm] = useState("");

  // Get member details from mock data
  const teamMembers = team.memberIds
    .map((id) => mockAllUsers.find((user) => user.id === id))
    .filter(Boolean) as { id: string; name: string; email: string }[];

  // Filter members based on search term
  const filteredMembers = teamMembers.filter(
    (member) =>
      member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filter all users for the add member dialog
  const filteredAvailableUsers = mockAllUsers
    .filter(
      (user) =>
        !team.memberIds.includes(user.id) &&
        (user.name.toLowerCase().includes(userSearchTerm.toLowerCase()) ||
          user.email.toLowerCase().includes(userSearchTerm.toLowerCase()))
    )
    .slice(0, 10);

  const handleAddMember = (userId: UUID) => {
    if (team.memberIds.includes(userId)) {
      toast.error(t("user_already_in_team"));
      return;
    }

    const updatedTeam = {
      ...team,
      memberIds: [...team.memberIds, userId],
    };
    onTeamUpdate(updatedTeam);
    toast.success(t("member_added"));
    setAddDialogOpen(false);
  };

  const handleRemoveMember = (userId: UUID) => {
    if (team.memberIds.length <= 1) {
      toast.error(t("cannot_remove_last_member"));
      return;
    }
    
    const updatedTeam = {
      ...team,
      memberIds: team.memberIds.filter((id) => id !== userId),
    };
    onTeamUpdate(updatedTeam);
    toast.success(t("member_removed"));
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <TranslatedHeading translationKey="team_members" level="h3" className="text-lg font-medium" />
        {!readOnly && (
          <AddMemberButton onClick={() => setAddDialogOpen(true)} />
        )}
      </div>

      <MemberSearchBar
        value={searchTerm}
        onChange={setSearchTerm}
      />

      <MembersList
        members={filteredMembers}
        onRemoveMember={handleRemoveMember}
        readOnly={readOnly}
        canRemove={team.memberIds.length > 1}
      />

      {!readOnly && (
        <AddMemberDialog
          open={addDialogOpen}
          onOpenChange={setAddDialogOpen}
          availableUsers={filteredAvailableUsers}
          onAddMember={handleAddMember}
          searchTerm={userSearchTerm}
          onSearchChange={setUserSearchTerm}
        />
      )}
    </div>
  );
};
