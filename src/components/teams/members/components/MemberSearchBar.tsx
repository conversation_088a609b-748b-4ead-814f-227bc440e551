
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { useLanguage } from "@/context/LanguageContext";
import { TranslatedInput } from "@/components/i18n";

interface MemberSearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export const MemberSearchBar = ({
  value,
  onChange,
  placeholder,
}: MemberSearchBarProps) => {
  const { t } = useLanguage();
  
  return (
    <div className="relative mb-4">
      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
      {placeholder ? (
        <Input
          placeholder={placeholder}
          className="pl-8"
          value={value}
          onChange={(e) => onChange(e.target.value)}
        />
      ) : (
        <TranslatedInput
          placeholderKey="search_team_members"
          className="pl-8"
          value={value}
          onChange={(e) => onChange(e.target.value)}
        />
      )}
    </div>
  );
};
