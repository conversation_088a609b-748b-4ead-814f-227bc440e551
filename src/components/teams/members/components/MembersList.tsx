
import { Trash2 } from "lucide-react";
import { UUID } from "@/types";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import { MemberListEmpty } from "./MemberListEmpty";
import { 
  TranslatedText
} from "@/components/i18n";

interface Member {
  id: string;
  name: string;
  email: string;
}

interface MembersListProps {
  members: Member[];
  onRemoveMember: (userId: UUID) => void;
  readOnly?: boolean;
  canRemove: boolean;
}

export const MembersList = ({ 
  members, 
  onRemoveMember, 
  readOnly = false, 
  canRemove = true 
}: MembersListProps) => {
  if (members.length === 0) {
    return <MemberListEmpty />;
  }

  return (
    <div className="rounded-md border">
      <ScrollArea className="h-[320px]">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>
                <TranslatedText translationKey="name" />
              </TableHead>
              <TableHead>
                <TranslatedText translationKey="email" />
              </TableHead>
              {!readOnly && (
                <TableHead className="w-[100px]">
                  <TranslatedText translationKey="actions" />
                </TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {members.map((member) => (
              <TableRow key={member.id}>
                <TableCell className="font-medium">{member.name}</TableCell>
                <TableCell>{member.email}</TableCell>
                {!readOnly && (
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onRemoveMember(member.id)}
                      disabled={!canRemove}
                      title={
                        !canRemove
                          ? "cannot_remove_last_member"
                          : "remove_member"
                      }
                    >
                      <Trash2 className="h-4 w-4 text-destructive" />
                    </Button>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </ScrollArea>
    </div>
  );
};
