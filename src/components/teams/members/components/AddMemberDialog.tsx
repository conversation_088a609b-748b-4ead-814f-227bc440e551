
import { User<PERSON><PERSON>, UserRound, X } from "lucide-react";
import { UUID } from "@/types";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { MemberSearchBar } from "./MemberSearchBar";
import { 
  TranslatedText,
} from "@/components/i18n";

interface User {
  id: string;
  name: string;
  email: string;
}

interface AddMemberDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  availableUsers: User[];
  onAddMember: (userId: UUID) => void;
  searchTerm: string;
  onSearchChange: (value: string) => void;
}

export const AddMemberDialog = ({
  open,
  onOpenChange,
  availableUsers,
  onAddMember,
  searchTerm,
  onSearchChange,
}: AddMemberDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            <TranslatedText translationKey="add_member" />
          </DialogTitle>
          <DialogDescription>
            <TranslatedText translationKey="search_members" />
          </DialogDescription>
        </DialogHeader>
        <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </DialogClose>
        <div className="flex flex-col space-y-4 py-4">
          <MemberSearchBar
            value={searchTerm}
            onChange={onSearchChange}
            placeholder="search_members"
          />
          <div className="relative">
            <ScrollArea className="h-[250px] pr-4">
              {availableUsers.length > 0 ? (
                <div className="space-y-1">
                  {availableUsers.map((user) => (
                    <div
                      key={user.id}
                      className="flex items-center justify-between p-2 hover:bg-muted rounded-md cursor-pointer"
                      onClick={() => onAddMember(user.id)}
                    >
                      <div className="flex items-center gap-2">
                        <UserRound className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{user.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {user.email}
                          </p>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          onAddMember(user.id);
                        }}
                      >
                        <UserPlus className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center p-4 text-muted-foreground">
                  <TranslatedText translationKey="no_members_found" />
                </div>
              )}
            </ScrollArea>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export const AddMemberButton = ({
  onClick,
}: {
  onClick: () => void;
}) => {
  return (
    <Button 
      size="sm" 
      className="gap-1" 
      onClick={onClick}
    >
      <UserPlus className="h-4 w-4" />
      <TranslatedText translationKey="add_member" />
    </Button>
  );
};
