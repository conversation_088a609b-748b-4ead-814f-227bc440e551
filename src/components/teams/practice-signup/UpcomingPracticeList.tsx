
import { useLanguage } from '@/context/LanguageContext';
import { Team, PracticeInstance } from '@/types';
import { Calendar } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MonthSelector } from './components/MonthSelector';
import { PracticeCard } from './components/PracticeCard';
import { BatchSelectionControls } from './components/BatchSelectionControls';
import { usePracticeList } from './hooks/usePracticeList';

interface UpcomingPracticeListProps {
  team: Team;
  practices: PracticeInstance[];
  userId?: string;
  getUserSignUpStatus: (practiceId: string) => string | null;
  onSignUp: (practiceId: string, status: 'attending' | 'not-attending') => void;
}

export const UpcomingPracticeList = ({
  team,
  practices,
  userId,
  getUserSignUpStatus,
  onSignUp
}: UpcomingPracticeListProps) => {
  const { t } = useLanguage();
  
  const {
    months,
    selectedMonth,
    setSelectedMonth,
    sortedMonthPractices,
    allAttending,
    allNotAttending,
    hasUnlockedPractices,
    handleBatchSelection
  } = usePracticeList(practices, userId, getUserSignUpStatus, onSignUp);
  
  return (
    <Card className="border shadow-sm">
      <CardHeader className="pb-2 space-y-1">
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center gap-1 text-xl">
            <Calendar className="h-5 w-5 text-primary" />
            {t('upcoming_practices')}
          </CardTitle>
          
          {hasUnlockedPractices && (
            <BatchSelectionControls
              allAttending={allAttending}
              allNotAttending={allNotAttending}
              onBatchSelection={handleBatchSelection}
            />
          )}
        </div>
        <CardDescription>{t('practice_signup_description')}</CardDescription>
      </CardHeader>
      
      <MonthSelector
        months={months}
        selectedMonth={selectedMonth}
        onSelectMonth={setSelectedMonth}
      />
      
      <CardContent className="pt-2">
        {sortedMonthPractices.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-1">
            {sortedMonthPractices.map(practice => (
              <PracticeCard
                key={practice.id}
                practice={practice}
                userStatus={getUserSignUpStatus(practice.id)}
                onSignUp={onSignUp}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-3 bg-muted/20 rounded-md">
            <p className="text-sm text-muted-foreground">
              {t('no_upcoming_practices_for_month')}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
