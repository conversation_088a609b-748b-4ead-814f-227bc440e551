
import { useLanguage } from '@/context/LanguageContext';
import { Users } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface Attendee {
  id: string;
  name: string;
  role: string;
}

interface AttendeesCardProps {
  attendees: Attendee[];
}

export const AttendeesCard = ({ attendees }: AttendeesCardProps) => {
  const { t } = useLanguage();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          {t('attendees')}
        </CardTitle>
        <CardDescription>
          {t('members_attending_this_practice')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {attendees.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {attendees.map(member => (
              <div key={member.id} className="flex items-center gap-3 border rounded-md p-3">
                <div className="h-8 w-8 rounded-full bg-marine-200 flex items-center justify-center">
                  {member.name.substring(0, 1)}
                </div>
                <div>
                  <p className="font-medium">{member.name}</p>
                  <p className="text-xs text-muted-foreground">{member.role}</p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">{t('no_members_attending')}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
