
import { useLanguage } from '@/context/LanguageContext';
import { Calendar } from '@/components/ui/calendar';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { addMonths } from 'date-fns';

interface PracticeCalendarProps {
  selectedDate: Date | undefined;
  onSelectDate: (date: Date | undefined) => void;
  isDayWithPractice: (date: Date) => boolean;
}

export const PracticeCalendar = ({ 
  selectedDate, 
  onSelectDate, 
  isDayWithPractice 
}: PracticeCalendarProps) => {
  const { t } = useLanguage();
  const today = new Date();
  const oneYearOut = addMonths(today, 12);

  return (
    <Card className="border shadow-sm">
      <CardHeader className="pb-3 pt-4 px-4">
        <CardTitle className="text-lg">{t('select_practice_date')}</CardTitle>
        <CardDescription className="text-xs">{t('select_date_to_manage_lineups')}</CardDescription>
      </CardHeader>
      <CardContent className="px-2 pb-2 pt-0">
        <Calendar
          mode="single"
          selected={selectedDate}
          onSelect={onSelectDate}
          className="border rounded-md"
          modifiers={{
            practice: (date) => isDayWithPractice(date),
          }}
          modifiersClassNames={{
            practice: "bg-marine-100 font-bold text-marine-700",
          }}
          disabled={(date) => !isDayWithPractice(date)}
          fromDate={today}
          toDate={oneYearOut}
          numberOfMonths={1}
          showOutsideDays={false}
          fixedWeeks
          classNames={{
            months: "flex flex-col space-y-1",
            month: "space-y-1",
            caption: "flex justify-center pt-1 relative items-center text-sm",
            caption_label: "text-xs font-medium",
            nav: "flex items-center space-x-1",
            nav_button: "size-7 p-0 opacity-70 hover:opacity-100",
            table: "w-full border-collapse",
            head_row: "flex w-full",
            head_cell: "text-muted-foreground w-8 text-[0.7rem] font-normal",
            row: "flex w-full mt-1",
            cell: "text-center text-xs relative size-8 p-0 focus-within:relative focus-within:z-20",
            day: "size-8 p-0 font-normal aria-selected:opacity-100",
            day_today: "bg-accent text-accent-foreground",
            day_outside: "opacity-50",
            day_disabled: "opacity-50",
            day_hidden: "invisible",
          }}
        />
      </CardContent>
    </Card>
  );
};
