
import { useLanguage } from '@/context/LanguageContext';
import { Team, PracticeInstance, BoatAssignment } from '@/types';
import { 
  useLineupManager, 
  PracticeCalendarSection, 
  BoatAssignmentSection, 
  useLineupActions,
  TeamProvider
} from './components';

interface LineupManagerProps {
  team: Team;
  practices: PracticeInstance[];
  availableBoats: { id: string; name: string; seats: number }[];
  teamMembers: { id: string; name: string; role: string }[];
  onUpdateLineup: (practiceId: string, boatAssignments: BoatAssignment[]) => void;
}

export const LineupManager = ({ 
  team, 
  practices, 
  availableBoats, 
  teamMembers,
  onUpdateLineup 
}: LineupManagerProps) => {
  const { t } = useLanguage();
  const lineupActions = useLineupActions({ onUpdateLineup });
  
  const {
    selectedDate,
    setSelectedDate,
    selectedPractice,
    selectedDatePractices,
    editingBoatAssignment,
    selectedBoat,
    crewPositions,
    getAttendeesForPractice,
    isDayWithPractice,
    handleSelectPractice,
    handleAddBoat,
    handleEditBoat,
    handleSelectBoat,
    handlePositionChange,
    handleSaveBoatAssignment,
    handleDeleteBoatAssignment,
    handleCancelBoatAssignment
  } = useLineupManager({ 
    team, 
    practices, 
    availableBoats, 
    teamMembers, 
    onUpdateLineup: lineupActions.saveLineup 
  });

  const attendees = selectedPractice ? getAttendeesForPractice(selectedPractice.id) : [];

  return (
    <TeamProvider value={{ team, practices, availableBoats, teamMembers, onUpdateLineup }}>
      <div className="space-y-4">
        <PracticeCalendarSection
          selectedDate={selectedDate}
          onSelectDate={setSelectedDate}
          isDayWithPractice={isDayWithPractice}
          practices={selectedDatePractices}
          selectedPracticeId={selectedPractice?.id}
          onSelectPractice={handleSelectPractice}
        />
        
        {selectedPractice && (
          <BoatAssignmentSection
            selectedPractice={selectedPractice}
            editingBoatAssignment={editingBoatAssignment}
            selectedBoat={selectedBoat}
            crewPositions={crewPositions}
            attendees={attendees}
            availableBoats={availableBoats}
            teamMembers={teamMembers}
            onSelectBoat={handleSelectBoat}
            onPositionChange={handlePositionChange}
            onCancel={handleCancelBoatAssignment}
            onSave={handleSaveBoatAssignment}
            onEditBoat={handleEditBoat}
            onDeleteBoat={(assignmentId) => {
              handleDeleteBoatAssignment(assignmentId);
              lineupActions.deleteLineup(selectedPractice.id, 
                selectedPractice.boatAssignments?.filter(b => b.id !== assignmentId) || []);
            }}
            onAddBoat={handleAddBoat}
          />
        )}
      </div>
    </TeamProvider>
  );
};
