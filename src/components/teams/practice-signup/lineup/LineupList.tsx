
import { useLanguage } from '@/context/LanguageContext';
import { BoatAssignment } from '@/types';
import { Button } from '@/components/ui/button';
import { Anchor, Plus } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface LineupListProps {
  boatAssignments: BoatAssignment[];
  teamMembers: { id: string; name: string; role: string }[];
  availableBoats: { id: string; name: string; seats: number; boatType?: string }[];
  onEditBoat: (boatAssignment: BoatAssignment) => void;
  onDeleteBoat: (assignmentId: string) => void;
  onAddBoat: () => void;
}

export const LineupList = ({
  boatAssignments,
  teamMembers,
  availableBoats,
  onEditBoat,
  onDeleteBoat,
  onAddBoat
}: LineupListProps) => {
  const { t } = useLanguage();

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="flex items-center gap-2">
            <Anchor className="h-5 w-5" />
            {t('lineups')}
          </CardTitle>
          <CardDescription>
            {t('assign_boats_and_positions')}
          </CardDescription>
        </div>
        <Button onClick={onAddBoat} size="sm">
          <Plus className="h-4 w-4 mr-1" />
          {t('add_boat')}
        </Button>
      </CardHeader>
      <CardContent>
        {boatAssignments.length > 0 ? (
          <div className="space-y-4">
            {boatAssignments.map(assignment => {
              const boat = availableBoats.find(b => b.id === assignment.watercraftId);
              const hasCoxswain = boat?.boatType?.endsWith('+') || false;
              
              return (
                <div key={assignment.id} className="border rounded-md p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h4 className="font-medium">{boat?.name || t('unknown_boat')}</h4>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" onClick={() => onEditBoat(assignment)}>
                        {t('edit')}
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="text-destructive hover:text-destructive" 
                        onClick={() => onDeleteBoat(assignment.id)}
                      >
                        {t('delete')}
                      </Button>
                    </div>
                  </div>
                  
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>{t('position')}</TableHead>
                        <TableHead>{t('crew_member')}</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {assignment.positions.map(position => {
                        const member = teamMembers.find(m => m.id === position.userId);
                        return (
                          <TableRow key={position.positionNumber}>
                            <TableCell>
                              {position.role === 'coxswain' ? t('coxswain') : `${t('seat')} ${position.positionNumber}`}
                            </TableCell>
                            <TableCell>
                              {member?.name || t('unknown_member')}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">{t('no_lineups_created')}</p>
            <Button className="mt-4" onClick={onAddBoat}>
              <Plus className="h-4 w-4 mr-1" />
              {t('create_first_lineup')}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
