
import { useLanguage } from '@/context/LanguageContext';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface Attendee {
  id: string;
  name: string;
  role: string;
}

interface BoatAssignmentFormProps {
  availableBoats: { id: string; name: string; seats: number; boatType?: string }[];
  selectedBoat: string;
  onSelectBoat: (boatId: string) => void;
  crewPositions: Array<{ position: number; userId: string; role: 'coxswain' | 'rower' }>;
  onPositionChange: (positionNumber: number, userId: string) => void;
  attendees: Attendee[];
  onCancel: () => void;
  onSave: () => void;
}

export const BoatAssignmentForm = ({
  availableBoats,
  selectedBoat,
  onSelectBoat,
  crewPositions,
  onPositionChange,
  attendees,
  onCancel,
  onSave
}: BoatAssignmentFormProps) => {
  const { t } = useLanguage();
  
  // Find the selected boat to check if it has a coxswain
  const selectedBoatDetails = availableBoats.find(boat => boat.id === selectedBoat);
  const hasCoxswain = selectedBoatDetails?.boatType?.endsWith('+') || false;

  return (
    <div className="space-y-4 border rounded-md p-4">
      <div className="space-y-2">
        <label className="text-sm font-medium">{t('select_boat')}</label>
        <Select
          value={selectedBoat}
          onValueChange={onSelectBoat}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder={t('select_boat')} />
          </SelectTrigger>
          <SelectContent>
            {availableBoats.map(boat => (
              <SelectItem key={boat.id} value={boat.id}>
                {boat.name} ({boat.seats} {t('seats')}{boat.boatType?.endsWith('+') ? ', + cox' : ''})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      {selectedBoat && crewPositions.length > 0 && (
        <div className="space-y-4">
          <h4 className="font-medium">{t('assign_crew')}</h4>
          
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('position')}</TableHead>
                <TableHead>{t('crew_member')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {crewPositions.map(position => (
                <TableRow key={position.position}>
                  <TableCell>
                    {position.role === 'coxswain' ? t('coxswain') : `${t('seat')} ${position.position}`}
                  </TableCell>
                  <TableCell>
                    <Select
                      value={position.userId}
                      onValueChange={(value) => onPositionChange(position.position, value)}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder={t('select_member')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">{t('unassigned')}</SelectItem>
                        {attendees.map(member => (
                          <SelectItem key={member.id} value={member.id}>
                            {member.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onCancel}>
              {t('cancel')}
            </Button>
            <Button onClick={onSave}>
              {t('save_lineup')}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
