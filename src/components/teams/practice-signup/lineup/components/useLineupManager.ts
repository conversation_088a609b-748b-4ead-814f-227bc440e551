
import { Team, PracticeInstance, BoatAssignment } from '@/types';
import { UseLineupManagerProps, LineupManagerHelpers } from './types/lineupTypes';
import { getPracticesForDate, isDayWithPractice as checkDayWithPractice } from './utils/dateUtils';
import { getAttendeesForPractice as getAttendees } from './utils/lineupUtils';
import { useLineupStateActions } from './actions/lineupStateActions';

export const useLineupManager = ({ 
  team, 
  practices, 
  availableBoats, 
  teamMembers, 
  onUpdateLineup 
}: UseLineupManagerProps) => {
  const [state, actions] = useLineupStateActions(practices, availableBoats, onUpdateLineup);
  
  // Create helper functions
  const helpers: LineupManagerHelpers = {
    getPracticesForDate: (date) => getPracticesForDate(date, practices),
    getAttendeesForPractice: (practiceId) => getAttendees(practiceId, practices, teamMembers),
    isDayWithPractice: (date) => checkDayWithPractice(date, practices)
  };

  // Get practices for the selected date
  const selectedDatePractices = helpers.getPracticesForDate(state.selectedDate);

  return {
    // State
    ...state,
    
    // Helper computed values
    selectedDatePractices,
    
    // Helper functions
    ...helpers,
    
    // Actions
    ...actions
  };
};
