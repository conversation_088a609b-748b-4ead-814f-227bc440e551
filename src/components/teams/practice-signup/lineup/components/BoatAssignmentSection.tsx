
import { PracticeInstance, BoatAssignment } from '@/types';
import { AttendeesCard } from '../AttendeesCard';
import { BoatAssignmentForm } from '../BoatAssignmentForm';
import { LineupList } from '../LineupList';

interface BoatAssignmentSectionProps {
  selectedPractice: PracticeInstance | null;
  editingBoatAssignment: BoatAssignment | null;
  selectedBoat: string;
  crewPositions: Array<{ position: number; userId: string; role: 'coxswain' | 'rower' }>;
  attendees: { id: string; name: string; role: string }[];
  availableBoats: { id: string; name: string; seats: number }[];
  teamMembers: { id: string; name: string; role: string }[];
  onSelectBoat: (boatId: string) => void;
  onPositionChange: (positionNumber: number, userId: string) => void;
  onCancel: () => void;
  onSave: () => void;
  onEditBoat: (boatAssignment: BoatAssignment) => void;
  onDeleteBoat: (assignmentId: string) => void;
  onAddBoat: () => void;
}

export const BoatAssignmentSection = ({
  selectedPractice,
  editingBoatAssignment,
  selectedBoat,
  crewPositions,
  attendees,
  availableBoats,
  teamMembers,
  onSelectBoat,
  onPositionChange,
  onCancel,
  onSave,
  onEditBoat,
  onDeleteBoat,
  onAddBoat
}: BoatAssignmentSectionProps) => {
  if (!selectedPractice) return null;

  return (
    <>
      <AttendeesCard attendees={attendees} />
      
      {editingBoatAssignment ? (
        <BoatAssignmentForm
          availableBoats={availableBoats}
          selectedBoat={selectedBoat}
          onSelectBoat={onSelectBoat}
          crewPositions={crewPositions}
          onPositionChange={onPositionChange}
          attendees={attendees}
          onCancel={onCancel}
          onSave={onSave}
        />
      ) : (
        <LineupList
          boatAssignments={selectedPractice.boatAssignments || []}
          teamMembers={teamMembers}
          availableBoats={availableBoats}
          onEditBoat={onEditBoat}
          onDeleteBoat={onDeleteBoat}
          onAddBoat={onAddBoat}
        />
      )}
    </>
  );
};
