
import { Team, PracticeInstance, BoatAssignment, CrewPosition } from '@/types';

export interface UseLineupManagerProps {
  team: Team;
  practices: PracticeInstance[];
  availableBoats: { id: string; name: string; seats: number; boatType?: string }[];
  teamMembers: { id: string; name: string; role: string }[];
  onUpdateLineup: (practiceId: string, boatAssignments: BoatAssignment[]) => void;
}

export interface LineupManagerState {
  selectedDate: Date | undefined;
  selectedPractice: PracticeInstance | null;
  editingBoatAssignment: BoatAssignment | null;
  selectedBoat: string;
  crewPositions: Array<{ position: number; userId: string; role: 'coxswain' | 'rower' }>;
}

export interface LineupManagerActions {
  setSelectedDate: (date: Date | undefined) => void;
  handleSelectPractice: (practiceId: string) => void;
  handleAddBoat: () => void;
  handleEditBoat: (boatAssignment: BoatAssignment) => void;
  handleSelectBoat: (boatId: string) => void;
  handlePositionChange: (positionNumber: number, userId: string) => void;
  handleSaveBoatAssignment: () => void;
  handleDeleteBoatAssignment: (assignmentId: string) => void;
  handleCancelBoatAssignment: () => void;
}

export interface LineupManagerHelpers {
  getPracticesForDate: (date: Date | undefined) => PracticeInstance[];
  getAttendeesForPractice: (practiceId: string) => { id: string; name: string; role: string }[];
  isDayWithPractice: (date: Date) => boolean;
}
