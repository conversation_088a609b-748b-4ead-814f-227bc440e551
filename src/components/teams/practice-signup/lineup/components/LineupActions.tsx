
import { toast } from 'sonner';
import { useLanguage } from '@/context/LanguageContext';
import { BoatAssignment } from '@/types';

interface UseLineupActionsProps {
  onUpdateLineup: (practiceId: string, boatAssignments: BoatAssignment[]) => void;
}

export const useLineupActions = ({ onUpdateLineup }: UseLineupActionsProps) => {
  const { t } = useLanguage();

  const saveLineup = (practiceId: string, boatAssignments: BoatAssignment[]) => {
    onUpdateLineup(practiceId, boatAssignments);
    toast.success(t('lineup_saved'));
  };

  const deleteLineup = (practiceId: string, boatAssignments: BoatAssignment[]) => {
    onUpdateLineup(practiceId, boatAssignments);
    toast.success(t('lineup_deleted'));
  };

  return {
    saveLineup,
    deleteLineup
  };
};
