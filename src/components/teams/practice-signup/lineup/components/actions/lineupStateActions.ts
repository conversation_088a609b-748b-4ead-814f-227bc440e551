
import { useState } from 'react';
import { toast } from 'sonner';
import { useLanguage } from '@/context/LanguageContext';
import { PracticeInstance, BoatAssignment } from '@/types';
import { generateId } from '@/utils/idGenerator';
import { LineupManagerState, LineupManagerActions } from '../types/lineupTypes';
import { createPositionsFromBoat } from '../utils/lineupUtils';

export const useLineupStateActions = (
  practices: PracticeInstance[],
  availableBoats: { id: string; name: string; seats: number; boatType?: string }[],
  onUpdateLineup: (practiceId: string, boatAssignments: BoatAssignment[]) => void
): [LineupManagerState, LineupManagerActions] => {
  const { t } = useLanguage();
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [selectedPractice, setSelectedPractice] = useState<PracticeInstance | null>(null);
  const [editingBoatAssignment, setEditingBoatAssignment] = useState<BoatAssignment | null>(null);
  const [selectedBoat, setSelectedBoat] = useState<string>('');
  const [crewPositions, setCrewPositions] = useState<Array<{ position: number; userId: string; role: 'coxswain' | 'rower' }>>([]);

  const handleSelectPractice = (practiceId: string) => {
    const practice = practices.find(p => p.id === practiceId);
    if (practice) {
      setSelectedPractice(practice);
      setEditingBoatAssignment(null);
    }
  };

  const handleAddBoat = () => {
    if (!selectedPractice) {
      toast.error(t('select_practice_first'));
      return;
    }
    
    setEditingBoatAssignment({
      id: generateId('boat'),
      practiceInstanceId: selectedPractice.id,
      watercraftId: '',
      positions: []
    });
    setSelectedBoat('');
    setCrewPositions([]);
  };

  const handleEditBoat = (boatAssignment: BoatAssignment) => {
    setEditingBoatAssignment(boatAssignment);
    setSelectedBoat(boatAssignment.watercraftId);
    setCrewPositions(boatAssignment.positions.map(p => ({
      position: p.positionNumber,
      userId: p.userId,
      role: p.role
    })));
  };

  const handleSelectBoat = (boatId: string) => {
    setSelectedBoat(boatId);
    const positions = createPositionsFromBoat(boatId, availableBoats);
    setCrewPositions(positions);
  };

  const handlePositionChange = (positionNumber: number, userId: string) => {
    setCrewPositions(prev => 
      prev.map(pos => 
        pos.position === positionNumber ? { ...pos, userId } : pos
      )
    );
  };

  const handleSaveBoatAssignment = () => {
    if (!selectedPractice || !selectedBoat) {
      toast.error(t('missing_boat_selection'));
      return;
    }
    
    // Validate that at least one position is assigned
    const hasAssignedPositions = crewPositions.some(p => p.userId);
    if (!hasAssignedPositions) {
      toast.error(t('assign_at_least_one_crew_member'));
      return;
    }
    
    const newBoatAssignment: BoatAssignment = {
      id: editingBoatAssignment?.id || generateId('boat'),
      practiceInstanceId: selectedPractice.id,
      watercraftId: selectedBoat,
      positions: crewPositions
        .filter(p => p.userId) // Only include assigned positions
        .map(p => ({
          positionNumber: p.position,
          userId: p.userId,
          role: p.role
        }))
    };
    
    // Update the boat assignments
    const updatedAssignments = selectedPractice.boatAssignments 
      ? [...selectedPractice.boatAssignments.filter(b => b.id !== newBoatAssignment.id), newBoatAssignment]
      : [newBoatAssignment];
    
    // Lock the practice when saving a lineup
    onUpdateLineup(selectedPractice.id, updatedAssignments);
    setEditingBoatAssignment(null);
    
    toast.success(t('lineup_saved'));
  };

  const handleDeleteBoatAssignment = (assignmentId: string) => {
    if (!selectedPractice) return;
    
    const updatedAssignments = selectedPractice.boatAssignments 
      ? selectedPractice.boatAssignments.filter(b => b.id !== assignmentId)
      : [];
    
    onUpdateLineup(selectedPractice.id, updatedAssignments);
    toast.success(t('lineup_deleted'));
  };

  const handleCancelBoatAssignment = () => {
    setEditingBoatAssignment(null);
  };

  const state: LineupManagerState = {
    selectedDate,
    selectedPractice,
    editingBoatAssignment,
    selectedBoat,
    crewPositions
  };

  const actions: LineupManagerActions = {
    setSelectedDate,
    handleSelectPractice,
    handleAddBoat,
    handleEditBoat,
    handleSelectBoat,
    handlePositionChange,
    handleSaveBoatAssignment,
    handleDeleteBoatAssignment,
    handleCancelBoatAssignment
  };

  return [state, actions];
};
