
import { useLanguage } from '@/context/LanguageContext';
import { PracticeInstance } from '@/types';
import { PracticeCalendar } from '../PracticeCalendar';
import { PracticeSelection } from '../PracticeSelection';

interface PracticeCalendarSectionProps {
  selectedDate: Date | undefined;
  onSelectDate: (date: Date | undefined) => void;
  isDayWithPractice: (date: Date) => boolean;
  practices: PracticeInstance[];
  selectedPracticeId: string | undefined;
  onSelectPractice: (practiceId: string) => void;
}

export const PracticeCalendarSection = ({
  selectedDate,
  onSelectDate,
  isDayWithPractice,
  practices,
  selectedPracticeId,
  onSelectPractice
}: PracticeCalendarSectionProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
      <div>
        <PracticeCalendar 
          selectedDate={selectedDate}
          onSelectDate={onSelectDate}
          isDayWithPractice={isDayWithPractice}
        />
      </div>
      
      <div>
        <PracticeSelection
          selectedDate={selectedDate}
          practices={practices}
          selectedPracticeId={selectedPracticeId}
          onSelectPractice={onSelectPractice}
        />
      </div>
    </div>
  );
};
