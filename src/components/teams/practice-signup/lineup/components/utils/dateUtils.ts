
import { format, addDays, isSameDay, isAfter, isBefore, addMonths } from 'date-fns';
import { PracticeInstance } from '@/types';

export const getPracticesForDate = (
  date: Date | undefined, 
  practices: PracticeInstance[]
): PracticeInstance[] => {
  if (!date || !practices) return [];
  
  const dateString = format(date, 'yyyy-MM-dd');
  return practices.filter(practice => 
    format(new Date(practice.date), 'yyyy-MM-dd') === dateString && !practice.canceled
  );
};

export const isDayWithPractice = (
  date: Date, 
  practices: PracticeInstance[]
): boolean => {
  if (!practices) return false;
  
  const dateString = format(date, 'yyyy-MM-dd');
  return practices.some(practice => 
    format(new Date(practice.date), 'yyyy-MM-dd') === dateString && !practice.canceled
  );
};

// Improved utility function to get month names from a date range
export const getMonthsInRange = (
  startDate: Date,
  endDate: Date
): string[] => {
  const months: string[] = [];
  let currentDate = new Date(startDate);
  
  // Loop through each month until we reach or pass the end date
  while (isBefore(currentDate, endDate) || isSameDay(currentDate, endDate)) {
    const monthKey = format(currentDate, 'MMM-yyyy');
    if (!months.includes(monthKey)) {
      months.push(monthKey);
    }
    // Move to the next month
    currentDate = addMonths(currentDate, 1);
  }
  
  return months;
};

// Utility function to check if a practice is in a specific month
export const isPracticeInMonth = (
  practice: PracticeInstance,
  monthKey: string
): boolean => {
  const practiceDate = new Date(practice.date);
  return format(practiceDate, 'MMM-yyyy') === monthKey;
};
