
import { PracticeInstance } from '@/types';

export const getAttendeesForPractice = (
  practiceId: string,
  practices: PracticeInstance[],
  teamMembers: { id: string; name: string; role: string }[]
): { id: string; name: string; role: string }[] => {
  const practice = practices.find(p => p.id === practiceId);
  if (!practice) return [];
  
  return practice.attendees
    .filter(a => a.status === 'attending')
    .map(a => {
      const member = teamMembers.find(m => m.id === a.userId);
      return member || null;
    })
    .filter(m => m !== null) as { id: string; name: string; role: string }[];
};

export const createPositionsFromBoat = (
  boatId: string,
  availableBoats: { id: string; name: string; seats: number; boatType?: string }[]
): Array<{ position: number; userId: string; role: 'coxswain' | 'rower' }> => {
  // Get the selected boat
  const boat = availableBoats.find(b => b.id === boatId);
  if (!boat) return [];
  
  // Check if the boat needs a coxswain (has + in the boat type)
  const hasCoxswain = boat.boatType?.endsWith('+') || false;
  
  // Create empty positions based on boat seats
  const positions = [];
  
  // Add coxswain position if needed
  if (hasCoxswain) {
    positions.push({ position: 0, userId: '', role: 'coxswain' as const });
  }
  
  // Add rower positions
  for (let i = 1; i <= boat.seats; i++) {
    positions.push({ position: i, userId: '', role: 'rower' as const });
  }
  
  return positions;
};
