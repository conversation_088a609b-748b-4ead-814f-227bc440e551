
import { createContext, useContext, ReactNode } from 'react';
import { Team, PracticeInstance, BoatAssignment } from '@/types';

interface TeamContextProps {
  team: Team;
  practices: PracticeInstance[];
  availableBoats: { id: string; name: string; seats: number }[];
  teamMembers: { id: string; name: string; role: string }[];
  onUpdateLineup: (practiceId: string, boatAssignments: BoatAssignment[]) => void;
}

const TeamContext = createContext<TeamContextProps | undefined>(undefined);

export const TeamProvider = ({ 
  children, 
  value 
}: { 
  children: ReactNode; 
  value: TeamContextProps 
}) => {
  return (
    <TeamContext.Provider value={value}>
      {children}
    </TeamContext.Provider>
  );
};

export const useTeamContext = () => {
  const context = useContext(TeamContext);
  if (!context) {
    throw new Error('useTeamContext must be used within a TeamProvider');
  }
  return context;
};
