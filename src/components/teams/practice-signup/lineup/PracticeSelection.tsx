
import { useLanguage } from '@/context/LanguageContext';
import { PracticeInstance } from '@/types';
import { format } from 'date-fns';
import { UserCheck } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface PracticeSelectionProps {
  selectedDate: Date | undefined;
  practices: PracticeInstance[];
  selectedPracticeId: string | undefined;
  onSelectPractice: (practiceId: string) => void;
}

export const PracticeSelection = ({
  selectedDate,
  practices,
  selectedPracticeId,
  onSelectPractice
}: PracticeSelectionProps) => {
  const { t } = useLanguage();

  return (
    <Card className="border shadow-sm h-full">
      <CardHeader className="pb-3 pt-4 px-4">
        <CardTitle className="text-lg">{t('practice_selection')}</CardTitle>
        <CardDescription className="text-xs">
          {selectedDate ? format(selectedDate, 'PPPP') : t('please_select_date')}
        </CardDescription>
      </CardHeader>
      <CardContent className="px-3 pb-3 pt-0 overflow-y-auto max-h-[320px]">
        {practices.length > 0 ? (
          <div className="space-y-2">
            {practices.map(practice => (
              <div 
                key={practice.id} 
                className={`border rounded-md p-2 cursor-pointer transition-colors hover:bg-muted ${
                  selectedPracticeId === practice.id ? 'border-primary' : ''
                }`}
                onClick={() => onSelectPractice(practice.id)}
              >
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium text-sm">{practice.startTime} - {practice.endTime}</p>
                    {practice.location && <p className="text-xs text-muted-foreground">{practice.location}</p>}
                  </div>
                  <div className="flex items-center">
                    <div className="flex items-center bg-marine-100 text-marine-800 px-2 py-0.5 rounded-full text-xs">
                      <UserCheck className="h-3 w-3 mr-1" />
                      {practice.attendees.filter(a => a.status === 'attending').length}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-4">
            <p className="text-muted-foreground text-sm">{t('no_practices_on_this_date')}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
