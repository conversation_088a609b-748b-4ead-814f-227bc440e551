
import { Team, PracticeInstance } from '@/types';

interface PracticeSignUpCalendarProps {
  team: Team;
  practices: PracticeInstance[];
  selectedDate: Date | undefined;
  onSelectDate: (date: Date | undefined) => void;
  selectedStatus: 'attending' | 'not-attending';
  onSelectStatus: (status: 'attending' | 'not-attending') => void;
  getUserSignUpStatus: (practiceId: string) => string | null;
  isDayWithPractice: (date: Date) => boolean;
  onSignUp: (practiceId: string) => void;
  selectedDatePractices: PracticeInstance[];
}
