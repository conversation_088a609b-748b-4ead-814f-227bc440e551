
import { format } from 'date-fns';
import { useLanguage } from '@/context/LanguageContext';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useIsMobile } from '@/hooks/use-mobile';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useState, useRef, useEffect } from 'react';

interface MonthSelectorProps {
  months: string[];
  selectedMonth: string;
  onSelectMonth: (month: string) => void;
}

export const MonthSelector = ({ months, selectedMonth, onSelectMonth }: MonthSelectorProps) => {
  const isMobile = useIsMobile();
  const scrollRef = useRef<HTMLDivElement>(null);
  const [showLeftScroll, setShowLeftScroll] = useState(false);
  const [showRightScroll, setShowRightScroll] = useState(true);

  // Scroll to selected month when it changes
  useEffect(() => {
    if (scrollRef.current && selectedMonth) {
      const selectedButton = scrollRef.current.querySelector(`[data-month="${selectedMonth}"]`);
      if (selectedButton) {
        selectedButton.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
      }
    }
  }, [selectedMonth]);

  // Check if scroll indicators should be shown
  const checkScrollIndicators = () => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setShowLeftScroll(scrollLeft > 0);
      setShowRightScroll(scrollLeft < scrollWidth - clientWidth - 5); // 5px buffer
    }
  };

  useEffect(() => {
    const scrollElement = scrollRef.current;
    if (scrollElement) {
      scrollElement.addEventListener('scroll', checkScrollIndicators);
      // Initial check
      checkScrollIndicators();
      
      return () => {
        scrollElement.removeEventListener('scroll', checkScrollIndicators);
      };
    }
  }, []);

  // Scroll functions
  const scrollLeft = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ left: -100, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ left: 100, behavior: 'smooth' });
    }
  };

  return (
    <div className="relative px-2 pb-2">
      {showLeftScroll && (
        <Button 
          variant="ghost" 
          size="icon" 
          className="absolute left-0 top-1/2 -translate-y-1/2 z-10 h-7 w-7 bg-background/80 shadow-sm"
          onClick={scrollLeft}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
      )}
      
      <ScrollArea className="w-full">
        <div 
          ref={scrollRef}
          className="flex space-x-1 px-1 py-1 overflow-x-auto scrollbar-hide"
          onScroll={checkScrollIndicators}
        >
          {months.map(month => (
            <Button 
              key={month}
              data-month={month}
              variant={month === selectedMonth ? "default" : "outline"}
              size="sm"
              className={`
                text-xs whitespace-nowrap px-2 py-1 h-6 flex-shrink-0 rounded-md
                ${isMobile ? 'min-w-0' : ''}
              `}
              onClick={() => onSelectMonth(month)}
            >
              {format(new Date(month.replace('MMM-', 'MMM 01, ')), 'MMM yyyy')}
            </Button>
          ))}
        </div>
      </ScrollArea>
      
      {showRightScroll && (
        <Button 
          variant="ghost" 
          size="icon" 
          className="absolute right-0 top-1/2 -translate-y-1/2 z-10 h-7 w-7 bg-background/80 shadow-sm"
          onClick={scrollRight}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
};
