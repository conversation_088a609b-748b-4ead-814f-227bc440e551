
import { useLanguage } from '@/context/LanguageContext';
import { Team, PracticeInstance } from '@/types';
import { UpcomingPracticeList } from '../UpcomingPracticeList';
import { usePracticeSignUp } from '../hooks/usePracticeSignUp';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CalendarDays } from 'lucide-react';

interface SignUpTabContentProps {
  team: Team;
  practices: PracticeInstance[];
  userId?: string;
  onUpdatePractice: (updatedPractice: PracticeInstance) => void;
}

export const SignUpTabContent = ({ team, practices, userId, onUpdatePractice }: SignUpTabContentProps) => {
  const { t } = useLanguage();
  const { 
    getUserSignUpStatus,
    handleSignUp
  } = usePracticeSignUp({
    practices,
    teamId: team.id,
    onUpdatePractice
  });

  if (!team.practiceSchedules || team.practiceSchedules.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CalendarDays className="h-5 w-5" />
            {t('practice_schedule')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-muted-foreground">{t('no_practice_schedule')}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <UpcomingPracticeList 
      team={team}
      practices={practices}
      userId={userId}
      getUserSignUpStatus={getUserSignUpStatus}
      onSignUp={(practiceId, status) => handleSignUp(practiceId, status)}
    />
  );
};
