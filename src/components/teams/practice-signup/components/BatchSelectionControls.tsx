
import { useLanguage } from '@/context/LanguageContext';
import { Button } from '@/components/ui/button';
import { CheckSquare, X, Loader2 } from 'lucide-react';
import { useState } from 'react';

interface BatchSelectionControlsProps {
  allAttending: boolean;
  allNotAttending: boolean;
  onBatchSelection: (attending: boolean) => Promise<void>;
}

export const BatchSelectionControls = ({ 
  allAttending, 
  allNotAttending,
  onBatchSelection 
}: BatchSelectionControlsProps) => {
  const { t } = useLanguage();
  const [isLoading, setIsLoading] = useState(false);
  
  const handleBatchSelect = async (attending: boolean) => {
    setIsLoading(true);
    try {
      await onBatchSelection(attending);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="flex items-center gap-1">
      <Button 
        variant={allAttending ? "default" : "outline"}
        size="sm" 
        className="h-7 text-xs"
        onClick={() => handleBatchSelect(true)}
        disabled={isLoading}
      >
        {isLoading ? (
          <Loader2 className="h-3.5 w-3.5 mr-1 animate-spin" />
        ) : (
          <CheckSquare className="h-3.5 w-3.5 mr-1" />
        )}
        {t('select_all')}
      </Button>
      <Button 
        variant={allNotAttending ? "default" : "outline"}
        size="sm" 
        className="h-7 text-xs"
        onClick={() => handleBatchSelect(false)}
        disabled={isLoading}
      >
        {isLoading ? (
          <Loader2 className="h-3.5 w-3.5 mr-1 animate-spin" />
        ) : (
          <X className="h-3.5 w-3.5 mr-1" />
        )}
        {t('unselect_all')}
      </Button>
    </div>
  );
};
