
import { Team, PracticeInstance, BoatAssignment } from '@/types';
import { LineupManager } from '../lineup/LineupManager';

interface LineupTabContentProps {
  team: Team;
  practices: PracticeInstance[];
  availableBoats: { id: string; name: string; seats: number }[];
  teamMembers: { id: string; name: string; role: string }[];
  onUpdateLineup: (practiceId: string, boatAssignments: BoatAssignment[]) => void;
}

export const LineupTabContent = ({ 
  team, 
  practices, 
  availableBoats, 
  teamMembers,
  onUpdateLineup 
}: LineupTabContentProps) => {
  return (
    <LineupManager 
      team={team}
      practices={practices}
      availableBoats={availableBoats}
      teamMembers={teamMembers}
      onUpdateLineup={onUpdateLineup}
    />
  );
};
