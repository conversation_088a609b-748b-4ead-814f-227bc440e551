
import { format } from 'date-fns';
import { useLanguage } from '@/context/LanguageContext';
import { Checkbox } from '@/components/ui/checkbox';
import { CheckCircle, Clock, MapPin, UserX } from 'lucide-react';
import { PracticeInstance } from '@/types';
import { useEffect, useState, useRef } from 'react';

interface PracticeCardProps {
  practice: PracticeInstance;
  userStatus: string | null;
  onSignUp: (practiceId: string, status: 'attending' | 'not-attending') => void;
}

export const PracticeCard = ({ practice, userStatus, onSignUp }: PracticeCardProps) => {
  const { t } = useLanguage();
  const practiceDate = new Date(practice.date);
  const [isAttending, setIsAttending] = useState(userStatus === 'attending');
  const prevStatusRef = useRef(userStatus);
  
  // Update the local state when the userStatus prop changes
  useEffect(() => {
    if (prevStatusRef.current !== userStatus) {
      setIsAttending(userStatus === 'attending');
      prevStatusRef.current = userStatus;
    }
  }, [userStatus]);
  
  const handleCheckboxChange = (checked: boolean) => {
    const newStatus = checked ? 'attending' : 'not-attending';
    onSignUp(practice.id, newStatus);
    setIsAttending(checked);
    prevStatusRef.current = newStatus;
  };
  
  return (
    <div className="border rounded-md p-1 shadow-sm bg-card">
      <div className="flex justify-between items-start mb-0.5">
        <p className="font-medium text-xs">
          {format(practiceDate, 'EEE, MMM d')}
        </p>
        {practice.locked && (
          <span className="bg-amber-100 text-amber-800 text-[10px] px-1 py-0 rounded-full">
            {t('locked')}
          </span>
        )}
      </div>
      
      <div className="flex items-center text-[10px] text-muted-foreground">
        <Clock className="h-2.5 w-2.5 mr-0.5" />
        <span>{practice.startTime} - {practice.endTime}</span>
        
        {practice.location && (
          <>
            <span className="mx-1">•</span>
            <MapPin className="h-2.5 w-2.5 mr-0.5" />
            <span className="truncate">{practice.location}</span>
          </>
        )}
      </div>
      
      {!practice.locked ? (
        <div className="flex items-center mt-1">
          <Checkbox 
            id={`attendance-${practice.id}`}
            checked={isAttending}
            onCheckedChange={handleCheckboxChange}
            className="h-3.5 w-3.5 data-[state=checked]:bg-green-600"
          />
          <label 
            htmlFor={`attendance-${practice.id}`}
            className="text-[11px] font-medium ml-1.5 cursor-pointer"
          >
            {t('attending')}
          </label>
        </div>
      ) : (
        <div className="flex items-center justify-start mt-1">
          {userStatus === 'attending' ? (
            <span className="flex items-center text-[11px] text-green-600">
              <CheckCircle className="h-2.5 w-2.5 mr-0.5" />
              {t('you_are_attending')}
            </span>
          ) : userStatus === 'not-attending' ? (
            <span className="flex items-center text-[11px] text-destructive">
              <UserX className="h-2.5 w-2.5 mr-0.5" />
              {t('you_are_not_attending')}
            </span>
          ) : (
            <span className="text-[11px] text-muted-foreground">
              {t('you_did_not_respond')}
            </span>
          )}
        </div>
      )}
    </div>
  );
};
