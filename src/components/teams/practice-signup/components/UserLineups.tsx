
import { useLanguage } from '@/context/LanguageContext';
import { PracticeInstance } from '@/types';
import { format } from 'date-fns';
import { MapPin, Clock, Calendar } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface UserLineupsProps {
  practices: PracticeInstance[];
  availableBoats: { id: string; name: string; seats: number }[];
  userId: string | undefined;
}

export const UserLineups = ({ practices, availableBoats, userId }: UserLineupsProps) => {
  const { t } = useLanguage();
  
  // Filter practices to include only those with boat assignments for the user
  const userPractices = practices.filter(practice => 
    practice.boatAssignments && 
    practice.boatAssignments.some(boat => 
      boat.positions.some(pos => pos.userId === userId)
    )
  );
  
  if (userPractices.length === 0) {
    return null;
  }
  
  return (
    <Card className="mt-4">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">{t("my_lineups")}</CardTitle>
        <CardDescription className="text-sm">{t("my_assigned_lineups")}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {userPractices.map(practice => {
            // Find boat assignments for this user
            const userBoatAssignments = practice.boatAssignments?.filter(boat => 
              boat.positions.some(pos => pos.userId === userId)
            ) || [];
            
            if (userBoatAssignments.length === 0) return null;
            
            const practiceDate = new Date(practice.date);
            
            return (
              <div key={practice.id} className="border rounded-md p-3">
                <h3 className="font-medium mb-2 flex items-center text-sm">
                  <Calendar className="h-3 w-3 mr-1 text-marine-600" />
                  {format(practiceDate, 'EEEE, MMMM d')}
                </h3>
                
                <div className="flex flex-wrap items-center gap-x-3 mb-2 text-xs text-muted-foreground">
                  <div className="flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {practice.startTime} - {practice.endTime}
                  </div>
                  
                  {practice.location && (
                    <div className="flex items-center">
                      <MapPin className="h-3 w-3 mr-1" />
                      {practice.location}
                    </div>
                  )}
                </div>
                
                <div className="space-y-2">
                  {userBoatAssignments.map(boat => {
                    const boatDetails = availableBoats.find(b => b.id === boat.watercraftId);
                    const userPosition = boat.positions.find(pos => pos.userId === userId);
                    
                    return (
                      <div key={boat.id} className="bg-marine-50 rounded-md p-2 text-xs">
                        <div className="flex justify-between items-center">
                          <p className="font-medium">{boatDetails?.name || t('unknown_boat')}</p>
                          <span className="bg-marine-100 text-marine-800 px-2 py-0.5 rounded-full text-xs">
                            {userPosition?.role === 'coxswain' 
                              ? t('coxswain') 
                              : `${t('seat')} ${userPosition?.positionNumber}`}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};
