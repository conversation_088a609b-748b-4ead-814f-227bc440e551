
import { useLanguage } from '@/context/LanguageContext';
import { AlertTriangle } from 'lucide-react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';

export const FeatureDisabled = () => {
  const { t } = useLanguage();
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-amber-500" />
          {t('feature_disabled')}
        </CardTitle>
        <CardDescription>
          {t('member_sign_up_disabled_description')}
        </CardDescription>
      </CardHeader>
    </Card>
  );
};
