
import { useState } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { useAuth } from '@/context/AuthContext';
import { useConfig } from '@/context/ConfigContext';
import { Team, PracticeInstance, BoatAssignment } from '@/types';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CalendarDays, Users } from 'lucide-react';
import { UserLineups, FeatureDisabled } from './components';
import { SignUpTabContent } from './components/SignUpTabContent';
import { LineupTabContent } from './components/LineupTabContent';

interface PracticeSignUpManagerProps {
  team: Team;
  practices: PracticeInstance[];
  availableBoats: {
    id: string;
    name: string;
    seats: number;
  }[];
  teamMembers: {
    id: string;
    name: string;
    role: string;
  }[];
  onUpdatePractice: (updatedPractice: PracticeInstance) => void;
  initialTab?: 'sign-up' | 'lineup';
}

export const PracticeSignUpManager = ({
  team,
  practices,
  availableBoats,
  teamMembers,
  onUpdatePractice,
  initialTab = 'sign-up'
}: PracticeSignUpManagerProps) => {
  const { t } = useLanguage();
  const { user } = useAuth();
  const { isFeatureEnabled } = useConfig();
  const [activeTab, setActiveTab] = useState<'sign-up' | 'lineup'>(initialTab);
  
  const isCoach = user?.role === 'coach' || user?.role === 'admin';
  const isMemberSignUpEnabled = isFeatureEnabled('enableMemberSignUp');
  
  if (!isMemberSignUpEnabled) {
    return <FeatureDisabled />;
  }

  // Fix type issue with state management
  const handleTabChange = (value: string) => {
    if (value === 'sign-up' || value === 'lineup') {
      setActiveTab(value);
    }
  };
  
  const handleUpdateLineup = (practiceId: string, boatAssignments: BoatAssignment[]) => {
    const practice = practices.find(p => p.id === practiceId);
    if (!practice) return;

    // Update the practice with the new boat assignments and lock it
    const updatedPractice: PracticeInstance = {
      ...practice,
      boatAssignments,
      locked: true // Lock the practice when a lineup is created
    };
    
    onUpdatePractice(updatedPractice);
  };
  
  return (
    <div className="space-y-4">
      <Tabs defaultValue={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="sign-up" className="flex items-center">
            <CalendarDays className="h-4 w-4 mr-2" />
            {t('practice_sign_up')}
          </TabsTrigger>
          {isCoach && (
            <TabsTrigger value="lineup" className="flex items-center">
              <Users className="h-4 w-4 mr-2" />
              {t('lineup_management')}
            </TabsTrigger>
          )}
        </TabsList>
        
        <TabsContent value="sign-up" className="mt-4">
          <SignUpTabContent 
            team={team} 
            practices={practices} 
            userId={user?.id} 
            onUpdatePractice={onUpdatePractice} 
          />
        </TabsContent>
        
        {isCoach && (
          <TabsContent value="lineup" className="mt-4">
            <LineupTabContent 
              team={team} 
              practices={practices} 
              availableBoats={availableBoats} 
              teamMembers={teamMembers} 
              onUpdateLineup={handleUpdateLineup} 
            />
          </TabsContent>
        )}
      </Tabs>
      
      {!isCoach && activeTab === "sign-up" && (
        <UserLineups 
          practices={practices} 
          availableBoats={availableBoats} 
          userId={user?.id} 
        />
      )}
    </div>
  );
};
