
import { PracticeInstance, PracticeSignUp } from '@/types';

export interface UsePracticeSignUpProps {
  practices: PracticeInstance[];
  teamId: string;
  onUpdatePractice: (updatedPractice: PracticeInstance) => void;
}

export interface PracticeSignUpResult {
  getUserSignUpStatus: (practiceId: string) => string | null;
  isDayWithPractice: (date: Date) => boolean;
  handleSignUp: (practiceId: string, status: 'attending' | 'not-attending') => void;
  getPracticesForDate: (date: Date | undefined) => PracticeInstance[];
  getUpcomingPractices: () => PracticeInstance[];
  lockPractice: (practiceId: string) => void;
  unlockPractice: (practiceId: string) => void;
}
