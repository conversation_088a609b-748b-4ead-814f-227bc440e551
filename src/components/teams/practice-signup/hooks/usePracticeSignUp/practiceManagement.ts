
import { PracticeInstance } from '@/types';
import { findPracticeById } from './dateUtils';

/**
 * Lock a practice to prevent further sign-ups
 */
export const createLockedPractice = (practice: PracticeInstance): PracticeInstance => {
  return {
    ...practice,
    locked: true
  };
};

/**
 * Unlock a practice to allow sign-ups again
 */
export const createUnlockedPractice = (practice: PracticeInstance): PracticeInstance => {
  return {
    ...practice,
    locked: false
  };
};
