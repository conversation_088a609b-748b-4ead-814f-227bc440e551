
import { format } from 'date-fns';
import { PracticeInstance } from '@/types';

/**
 * Gets practices scheduled for a specific date
 */
export const getPracticesForDate = (
  practices: PracticeInstance[],
  date: Date | undefined
): PracticeInstance[] => {
  try {
    if (!date || !practices) return [];
    
    const dateString = format(date, 'yyyy-MM-dd');
    return practices.filter(practice => 
      format(new Date(practice.date), 'yyyy-MM-dd') === dateString
    );
  } catch (error) {
    console.error('Error getting practices for date:', error);
    return [];
  }
};

/**
 * Determines if a date has any scheduled practices
 */
export const isDayWithPractice = (
  practices: PracticeInstance[],
  date: Date
): boolean => {
  try {
    if (!practices) return false;
    
    const dateString = format(date, 'yyyy-MM-dd');
    return practices.some(practice => 
      format(new Date(practice.date), 'yyyy-MM-dd') === dateString && !practice.canceled
    );
  } catch (error) {
    console.error('Error checking if day has practice:', error);
    return false;
  }
};

/**
 * Gets upcoming practices (those that haven't happened yet)
 */
export const getUpcomingPractices = (
  practices: PracticeInstance[]
): PracticeInstance[] => {
  try {
    if (!practices) return [];
    
    const now = new Date();
    return practices
      .filter(practice => new Date(practice.date) >= now && !practice.canceled)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  } catch (error) {
    console.error('Error getting upcoming practices:', error);
    return [];
  }
};

/**
 * Find a practice by its ID
 */
export const findPracticeById = (
  practices: PracticeInstance[],
  practiceId: string
): PracticeInstance | undefined => {
  return practices.find(p => p.id === practiceId);
};
