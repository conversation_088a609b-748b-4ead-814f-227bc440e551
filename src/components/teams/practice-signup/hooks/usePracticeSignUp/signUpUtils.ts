
import { generateId } from '@/utils/idGenerator';
import { PracticeSignUp, PracticeInstance } from '@/types';

/**
 * Gets the user's sign-up status for a specific practice
 */
export const getUserSignUpStatus = (
  practices: PracticeInstance[],
  practiceId: string,
  userId?: string
): string | null => {
  try {
    if (!userId || !practices) return null;
    
    const practice = practices.find(p => p.id === practiceId);
    if (!practice) return null;
    
    const userSignUp = practice.attendees.find(a => a.userId === userId);
    // For backward compatibility, if the status is 'tentative', return 'attending'
    if (userSignUp && userSignUp.status === 'tentative') {
      return 'attending';
    }
    return userSignUp?.status || null;
  } catch (error) {
    console.error('Error getting user sign-up status:', error);
    return null;
  }
};

/**
 * Creates a new sign-up for a user
 */
export const createNewSignUp = (
  practiceId: string,
  userId: string,
  teamId: string,
  status: 'attending' | 'not-attending'
): PracticeSignUp => {
  return {
    id: generateId('signUp'),
    practiceId,
    userId,
    teamId,
    status,
    createdAt: new Date(),
    updatedAt: new Date()
  };
};

/**
 * Updates an existing sign-up
 */
export const updateExistingSignUp = (
  signUp: PracticeSignUp,
  status: 'attending' | 'not-attending'
): PracticeSignUp => {
  return {
    ...signUp,
    status,
    updatedAt: new Date()
  };
};
