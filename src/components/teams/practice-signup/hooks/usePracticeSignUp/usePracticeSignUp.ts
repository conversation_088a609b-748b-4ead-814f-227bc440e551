
import { useAuth } from '@/context/AuthContext';
import { useLanguage } from '@/context/LanguageContext';
import { toast } from 'sonner';
import { PracticeInstance } from '@/types';
import { 
  getPracticesForDate, 
  isDayWithPractice, 
  getUpcomingPractices, 
  findPracticeById 
} from './dateUtils';
import { 
  getUserSignUpStatus as getSignUpStatus, 
  createNewSignUp, 
  updateExistingSignUp 
} from './signUpUtils';
import { 
  createLockedPractice, 
  createUnlockedPractice 
} from './practiceManagement';
import { UsePracticeSignUpProps, PracticeSignUpResult } from './types';

/**
 * Hook for managing practice sign-ups
 */
export const usePracticeSignUp = ({ 
  practices, 
  teamId, 
  onUpdatePractice 
}: UsePracticeSignUpProps): PracticeSignUpResult => {
  const { t } = useLanguage();
  const { user } = useAuth();

  /**
   * Gets the user's sign-up status for a specific practice
   */
  const getUserSignUpStatus = (practiceId: string): string | null => {
    return getSignUpStatus(practices, practiceId, user?.id);
  };

  /**
   * Handle sign-up for a practice
   */
  const handleSignUp = (practiceId: string, status: 'attending' | 'not-attending'): void => {
    try {
      if (!user) {
        toast.error(t('login_required'));
        return;
      }
      
      const practice = findPracticeById(practices, practiceId);
      if (!practice) {
        toast.error(t('practice_not_found'));
        return;
      }
      
      // Check if practice is locked
      if (practice.locked) {
        toast.error(t('practice_signup_closed'));
        return;
      }
      
      // Check if user already has a sign-up
      const existingSignUpIndex = practice.attendees.findIndex(a => a.userId === user.id);
      
      let updatedAttendees;
      
      if (existingSignUpIndex >= 0) {
        // Update existing sign-up
        updatedAttendees = [...practice.attendees];
        updatedAttendees[existingSignUpIndex] = updateExistingSignUp(
          practice.attendees[existingSignUpIndex],
          status
        );
      } else {
        // Create new sign-up
        const newSignUp = createNewSignUp(practiceId, user.id, teamId, status);
        updatedAttendees = [...practice.attendees, newSignUp];
      }
      
      // Update the practice
      const updatedPractice: PracticeInstance = {
        ...practice,
        attendees: updatedAttendees
      };
      
      onUpdatePractice(updatedPractice);
      
      toast.success(
        status === 'attending' 
          ? t('practice_sign_up_success') 
          : t('practice_sign_out_success')
      );
    } catch (error) {
      console.error('Error handling sign-up:', error);
      toast.error(t('error_updating_signup'));
    }
  };

  /**
   * Lock a practice to prevent further sign-ups (coach only)
   */
  const lockPractice = (practiceId: string): void => {
    try {
      const practice = findPracticeById(practices, practiceId);
      if (!practice) {
        toast.error(t('practice_not_found'));
        return;
      }
      
      const updatedPractice = createLockedPractice(practice);
      onUpdatePractice(updatedPractice);
      toast.success(t('practice_locked'));
    } catch (error) {
      console.error('Error locking practice:', error);
      toast.error(t('error_locking_practice'));
    }
  };

  /**
   * Unlock a practice to allow sign-ups again (coach only)
   */
  const unlockPractice = (practiceId: string): void => {
    try {
      const practice = findPracticeById(practices, practiceId);
      if (!practice) {
        toast.error(t('practice_not_found'));
        return;
      }
      
      const updatedPractice = createUnlockedPractice(practice);
      onUpdatePractice(updatedPractice);
      toast.success(t('practice_unlocked'));
    } catch (error) {
      console.error('Error unlocking practice:', error);
      toast.error(t('error_unlocking_practice'));
    }
  };

  return {
    getUserSignUpStatus,
    isDayWithPractice: (date: Date) => isDayWithPractice(practices, date),
    handleSignUp,
    getPracticesForDate: (date: Date | undefined) => getPracticesForDate(practices, date),
    getUpcomingPractices: () => getUpcomingPractices(practices),
    lockPractice,
    unlockPractice
  };
};
