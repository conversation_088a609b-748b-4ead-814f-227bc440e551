
import { useState, useEffect, useMemo } from 'react';
import { toast } from 'sonner';
import { useLanguage } from '@/context/LanguageContext';
import { PracticeInstance } from '@/types';
import { 
  addMonths, 
  isBefore, 
  isToday, 
  isSameDay, 
  isAfter 
} from 'date-fns';
import { isPracticeInMonth } from '../lineup/components/utils/dateUtils';

export const usePracticeList = (
  practices: PracticeInstance[],
  userId: string | undefined,
  getUserSignUpStatus: (practiceId: string) => string | null,
  onSignUp: (practiceId: string, status: 'attending' | 'not-attending') => void
) => {
  const { t } = useLanguage();
  const [selectedMonth, setSelectedMonth] = useState<string | null>(null);
  const [lastBatchUpdate, setLastBatchUpdate] = useState<number>(0); // Add this to force re-evaluation
  
  // Memoize these date constants to avoid recalculating on every render
  const today = useMemo(() => new Date(), []);
  const oneYearLater = useMemo(() => addMonths(today, 12), [today]);
  
  // Memoize filtered practices for performance
  const futurePractices = useMemo(() => {
    return practices.filter(practice => {
      const practiceDate = new Date(practice.date);
      return (isToday(practiceDate) || isAfter(practiceDate, today)) && 
             (isBefore(practiceDate, oneYearLater) || isSameDay(practiceDate, oneYearLater)) &&
             !practice.canceled;
    });
  }, [practices, today, oneYearLater]);
  
  // Memoize available months from practices
  const months = useMemo(() => {
    return getMonthsInRange(today, oneYearLater);
  }, [today, oneYearLater]);
  
  // Set initial month that has practices
  useEffect(() => {
    if (!selectedMonth && months.length > 0) {
      // Find first month with practices using memoized values
      const monthWithPractices = months.find(month => 
        futurePractices.some(practice => isPracticeInMonth(practice, month))
      );
      
      setSelectedMonth(monthWithPractices || months[0]);
    }
  }, [futurePractices, months, selectedMonth]);
  
  const effectiveMonth = selectedMonth || (months.length > 0 ? months[0] : '');
  
  // Memoize filtered and sorted practices for selected month
  const sortedMonthPractices = useMemo(() => {
    // First filter practices for the selected month
    const monthPractices = futurePractices.filter(practice => 
      isPracticeInMonth(practice, effectiveMonth)
    );
    
    // Then sort them by date (only once after filtering)
    return [...monthPractices].sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );
  }, [futurePractices, effectiveMonth]);
  
  // Memoize attendance check results to avoid recalculating on every render
  // Include lastBatchUpdate in dependencies to force re-evaluation after batch operations
  const { allAttending, allNotAttending, hasUnlockedPractices } = useMemo(() => {
    // Check if all unlocked practices have the same attendance status
    const checkAllStatus = (targetStatus: 'attending' | 'not-attending' | null): boolean => {
      const unlockedPractices = sortedMonthPractices.filter(p => !p.locked);
      if (unlockedPractices.length === 0) return false;
      
      return unlockedPractices.every(p => {
        const status = getUserSignUpStatus(p.id);
        return status === targetStatus;
      });
    };
    
    return {
      allAttending: checkAllStatus('attending'),
      allNotAttending: checkAllStatus('not-attending'),
      hasUnlockedPractices: sortedMonthPractices.some(p => !p.locked)
    };
  }, [sortedMonthPractices, getUserSignUpStatus, lastBatchUpdate]);
  
  // Handle selection of all practices in the month
  const handleBatchSelection = async (attending: boolean) => {
    // Only operate on the currently displayed month's practices
    const unlockedPractices = sortedMonthPractices.filter(p => !p.locked);
    
    if (unlockedPractices.length === 0) {
      toast.info(t('no_upcoming_practices'));
      return;
    }
    
    let changedCount = 0;
    const targetStatus = attending ? 'attending' : 'not-attending';
    
    // Create an array of promises to process all sign-ups
    const updatePromises = unlockedPractices.map(practice => {
      return new Promise<void>(resolve => {
        const currentStatus = getUserSignUpStatus(practice.id);
        
        if (currentStatus !== targetStatus) {
          onSignUp(practice.id, targetStatus);
          changedCount++;
        }
        
        // Small delay to ensure UI state updates properly
        setTimeout(resolve, 10);
      });
    });
    
    // Wait for all updates to complete
    await Promise.all(updatePromises);
    
    // Force a re-evaluation of the attendance status
    setLastBatchUpdate(Date.now());
    
    if (changedCount > 0) {
      // Create the message with the count
      let message = attending 
        ? t('signed_up_for_all_practices') 
        : t('signed_out_from_all_practices');
      
      // Replace the {{count}} placeholder manually
      message = message.replace('{{count}}', String(changedCount));
      
      toast.success(message);
    } else {
      const message = attending 
        ? t('already_signed_up_for_all') 
        : t('already_signed_out_from_all');
      
      toast.info(message);
    }
  };
  
  return {
    months,
    selectedMonth: effectiveMonth,
    setSelectedMonth,
    sortedMonthPractices,
    allAttending,
    allNotAttending,
    hasUnlockedPractices,
    handleBatchSelection
  };
};

// Helper function to get available months between two dates
// This is kept outside the component to avoid recreation on renders
function getMonthsInRange(startDate: Date, endDate: Date): string[] {
  const months: string[] = [];
  const currentDate = new Date(startDate);
  
  while (currentDate <= endDate) {
    const monthFormat = `${currentDate.toLocaleString('en', { month: 'short' })}-${currentDate.getFullYear()}`;
    months.push(monthFormat);
    
    currentDate.setMonth(currentDate.getMonth() + 1);
  }
  
  return months;
}
