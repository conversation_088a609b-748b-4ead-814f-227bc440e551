
import { Team } from '@/types';
import { useLanguage } from '@/context/LanguageContext';
import { Button } from '@/components/ui/button';
import { UserPlus } from 'lucide-react';
import { TeamCard } from './TeamCard';
import { TeamSkeleton } from './TeamSkeleton';

interface TeamListProps {
  teams: Team[];
  loading: boolean;
  onCreateTeamClick: () => void;
  readOnly?: boolean;
  onTeamClick?: (team: Team) => void;
}

export const TeamList = ({ teams, loading, onCreateTeamClick, readOnly = false, onTeamClick }: TeamListProps) => {
  const { t } = useLanguage();

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3].map((i) => (
          <TeamSkeleton key={i} />
        ))}
      </div>
    );
  }

  if (teams.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">{t('no_teams_yet')}</p>
        {!readOnly && (
          <Button className="mt-4" onClick={onCreateTeamClick}>
            <UserPlus className="mr-2 h-4 w-4" />
            {t('create_your_first_team')}
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {teams.map((team) => (
        <TeamCard 
          key={team.id} 
          team={team} 
          readOnly={readOnly} 
          onClick={onTeamClick ? () => onTeamClick(team) : undefined}
        />
      ))}
    </div>
  );
};
