
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/card';

export const TeamSkeleton = () => {
  return (
    <Card className="animate-pulse">
      <CardHeader className="pb-2">
        <div className="h-6 bg-muted rounded w-2/3"></div>
      </CardHeader>
      <CardContent>
        <div className="h-4 bg-muted rounded mb-3 w-full"></div>
        <div className="h-4 bg-muted rounded mb-3 w-2/3"></div>
        <div className="flex space-x-2 mb-3">
          {[1, 2, 3].map((j) => (
            <div key={j} className="h-6 bg-muted rounded w-16"></div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
