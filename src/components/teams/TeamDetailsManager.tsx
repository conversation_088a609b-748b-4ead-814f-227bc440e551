
import { useState } from "react";
import { useLanguage } from "@/context/LanguageContext";
import { Team } from "@/types";
import { 
  Ta<PERSON>, 
  Ta<PERSON>Content, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { Users, CalendarDays, UserRound } from "lucide-react";
import { CoachAssignmentManager } from "./coach-assignment";
import { PracticeScheduleManager } from "./practice-schedule";
import { TeamMembersManager } from "./members";

interface TeamDetailsManagerProps {
  team: Team;
  onTeamUpdate: (updatedTeam: Team) => void;
  readOnly?: boolean;
}

export const TeamDetailsManager = ({ 
  team, 
  onTeamUpdate, 
  readOnly = false
}: TeamDetailsManagerProps) => {
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState("coaches");
  
  const handleCoachUpdate = (updatedCoaches: string[]) => {
    const updatedTeam = {
      ...team,
      coachIds: updatedCoaches,
    };
    onTeamUpdate(updatedTeam);
  };
  
  const handlePracticeScheduleUpdate = (updatedTeam: Team) => {
    onTeamUpdate(updatedTeam);
  };

  return (
    <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="w-full grid grid-cols-3">
        <TabsTrigger value="coaches" className="flex items-center">
          <Users className="h-4 w-4 mr-2" />
          {t("coaches")}
        </TabsTrigger>
        <TabsTrigger value="practices" className="flex items-center">
          <CalendarDays className="h-4 w-4 mr-2" />
          {t("practices")}
        </TabsTrigger>
        <TabsTrigger value="members" className="flex items-center">
          <UserRound className="h-4 w-4 mr-2" />
          {t("members")}
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="coaches" className="mt-6">
        <CoachAssignmentManager 
          team={team}
          onUpdate={handleCoachUpdate}
          readOnly={readOnly}
        />
      </TabsContent>
      
      <TabsContent value="practices" className="mt-6">
        <PracticeScheduleManager 
          team={team}
          onUpdate={handlePracticeScheduleUpdate}
          readOnly={readOnly}
        />
      </TabsContent>

      <TabsContent value="members" className="mt-6">
        <TeamMembersManager
          team={team}
          onTeamUpdate={onTeamUpdate}
          readOnly={readOnly}
        />
      </TabsContent>
    </Tabs>
  );
};
