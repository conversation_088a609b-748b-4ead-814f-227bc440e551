
import { Jaunt, Watercraft, Boat } from '@/types';
import { JauntCard } from './JauntCard';
import { JauntSkeleton } from './JauntSkeleton';
import { JauntEmptyState } from './JauntEmptyState';

interface JauntListProps {
  jaunts: Jaunt[];
  watercrafts: Record<string, Watercraft | Boat>;
  loading: boolean;
}

export const JauntList = ({ jaunts, watercrafts, loading }: JauntListProps) => {
  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3].map((i) => (
          <JauntSkeleton key={i} />
        ))}
      </div>
    );
  }

  if (jaunts.length === 0) {
    return <JauntEmptyState />;
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {jaunts.map((jaunt) => {
        const watercraft = watercrafts[jaunt.watercraftId];
        if (!watercraft) return null;
        
        return (
          <JauntCard 
            key={jaunt.id} 
            jaunt={jaunt} 
            watercraft={watercraft} 
          />
        );
      })}
    </div>
  );
};
