
import { useLanguage } from '@/context/LanguageContext';

interface JauntCommentsProps {
  comments?: string;
}

export const JauntComments = ({ comments }: JauntCommentsProps) => {
  const { t } = useLanguage();
  
  if (!comments) {
    return null;
  }
  
  return (
    <div className="mt-3 pt-3 border-t text-sm">
      <p className="text-gray-500">{t('comments')}:</p>
      <p>{comments}</p>
    </div>
  );
};
