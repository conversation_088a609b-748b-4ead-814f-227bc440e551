
import { Jaunt, Watercraft, Boat } from '@/types';
import { useLanguage } from '@/context/LanguageContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ship, Calendar } from 'lucide-react';
import { JauntMetadataGrid } from './JauntMetadataGrid';
import { JauntComments } from './JauntComments';

interface JauntCardProps {
  jaunt: Jaunt;
  watercraft: Watercraft | Boat;
}

export const JauntCard = ({ jaunt, watercraft }: JauntCardProps) => {
  const { t } = useLanguage();
  
  const isBoat = watercraft.type === 'boat';
  const boat = isBoat ? watercraft as Boat : null;
  
  const formatDate = (date: Date) => {
    return date.toLocaleDateString(undefined, {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  return (
    <Card className="overflow-hidden animate-in fade-in">
      {watercraft.imageUrl && (
        <div className="w-full h-40 overflow-hidden">
          <img 
            src={watercraft.imageUrl} 
            alt={watercraft.name} 
            className="w-full h-full object-cover" 
          />
        </div>
      )}
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Ship className="h-5 w-5 text-marine-600" />
          {watercraft.name}
          <span className="text-xs font-normal text-muted-foreground">
            ({isBoat && boat ? `${t('boat')} (${boat.boatType})` : t(watercraft.type)})
          </span>
        </CardTitle>
        <CardDescription>
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            {formatDate(new Date(jaunt.startTime))}
          </div>
        </CardDescription>
      </CardHeader>
      
      <CardContent className="pb-3">
        <JauntMetadataGrid jaunt={jaunt} />
        <JauntComments comments={jaunt.comments} />
      </CardContent>
    </Card>
  );
};
