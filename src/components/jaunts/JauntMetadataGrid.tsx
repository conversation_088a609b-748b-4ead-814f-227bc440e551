
import { Jaunt } from '@/types';
import { useLanguage } from '@/context/LanguageContext';
import { Clock } from 'lucide-react';

interface JauntMetadataGridProps {
  jaunt: Jaunt;
}

export const JauntMetadataGrid = ({ jaunt }: JauntMetadataGridProps) => {
  const { t } = useLanguage();
  
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString(undefined, {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (start: Date, end: Date) => {
    const durationMs = end.getTime() - start.getTime();
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };
  
  return (
    <div className="grid grid-cols-2 gap-3 text-sm">
      <div>
        <p className="text-gray-500">{t('start_time')}</p>
        <p className="font-medium flex items-center gap-1">
          <Clock className="h-3 w-3" />
          {formatTime(new Date(jaunt.startTime))}
        </p>
      </div>
      
      <div>
        <p className="text-gray-500">{t('end_time')}</p>
        <p className="font-medium flex items-center gap-1">
          <Clock className="h-3 w-3" />
          {jaunt.actualEndTime ? formatTime(new Date(jaunt.actualEndTime)) : '-'}
        </p>
      </div>
      
      <div>
        <p className="text-gray-500">{t('duration')}</p>
        <p className="font-medium">
          {jaunt.actualEndTime 
            ? formatDuration(new Date(jaunt.startTime), new Date(jaunt.actualEndTime))
            : '-'}
        </p>
      </div>
      
      <div>
        <p className="text-gray-500">{t('distance')}</p>
        <p className="font-medium">
          {jaunt.distanceTraveled ? `${jaunt.distanceTraveled} km` : '-'}
        </p>
      </div>
    </div>
  );
};
