
import { useLanguage } from '@/context/LanguageContext';
import { useEffect } from 'react';
import { toast } from 'sonner';

interface JauntErrorMessageProps {
  error: Error | null;
}

export const JauntErrorMessage = ({ error }: JauntErrorMessageProps) => {
  const { t } = useLanguage();
  
  useEffect(() => {
    if (error) {
      toast.error(t('error_loading_jaunts'));
    }
  }, [error, t]);
  
  return null; // This component doesn't render anything, it just shows a toast
};
