
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Boat, Watercraft, WatercraftStatus } from '@/types';
import { useLanguage } from '@/context/LanguageContext';
import { Heart, Calendar, Wrench, MoreHorizontal } from 'lucide-react';
import { cn } from '@/lib/utils';

interface WatercraftCardProps {
  watercraft: Watercraft | Boat;
  onToggleFavorite: (id: string) => void;
  onScheduleJaunt: (id: string) => void;
  onScheduleMaintenance: (id: string) => void;
  onViewDetails: (id: string) => void;
}

const WatercraftCard: React.FC<WatercraftCardProps> = ({
  watercraft,
  onToggleFavorite,
  onScheduleJaunt,
  onScheduleMaintenance,
  onViewDetails,
}) => {
  const { t } = useLanguage();

  // Type guard to check if the watercraft is a Boat
  const isBoat = (craft: Watercraft | Boat): craft is Boat => {
    return craft.type === 'boat' && 'boatType' in craft;
  };

  return (
    <Card className="h-full flex flex-col transition-all hover:shadow-md">
      {watercraft.imageUrl && (
        <div className="aspect-video w-full overflow-hidden rounded-t-lg">
          <img 
            src={watercraft.imageUrl} 
            alt={watercraft.name} 
            className="w-full h-full object-cover transition-transform hover:scale-105"
          />
        </div>
      )}
      
      <CardHeader className="pb-2 pt-4">
        <div className="flex justify-between items-start gap-2">
          <CardTitle className="text-lg font-bold line-clamp-1">{watercraft.name}</CardTitle>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 shrink-0"
            onClick={() => onToggleFavorite(watercraft.id)}
            aria-label={t("toggle_favorite")}
          >
            <Heart className={cn("h-5 w-5", {'fill-red-500 text-red-500': 'isFavorite' in watercraft && watercraft.isFavorite})} />
          </Button>
        </div>
        
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <Badge variant="outline" className="capitalize">
            {watercraft.type}
          </Badge>
          
          <Badge 
            variant="outline" 
            className={cn( 
              watercraft.status === "available" ? "bg-green-100 text-green-800 border-green-200" : 
              watercraft.status === "in-use" ? "bg-blue-100 text-blue-800 border-blue-200" : 
              watercraft.status === "maintenance" ? "bg-yellow-100 text-yellow-800 border-yellow-200" : 
              "bg-red-100 text-red-800 border-red-200"
            )}
          >
            {t(watercraft.status)}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="pb-2 flex-grow">
        <div className="grid grid-cols-2 gap-3 text-sm">
          {isBoat(watercraft) && (
            <>
              <div className="flex items-center">
                <span className="font-medium">{t("capacity")}:</span>
                <span className="ml-1">{watercraft.weightRange?.max || "N/A"}</span>
              </div>
              <div className="flex items-center">
                <span className="font-medium">{t("length")}:</span>
                <span className="ml-1">{watercraft.boatType || "N/A"}</span>
              </div>
            </>
          )}
          <div className="flex items-center col-span-2">
            <span className="font-medium">{t("location")}:</span>
            <span className="ml-1 truncate">{watercraft.location}</span>
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="pt-2 pb-4 grid grid-cols-3 gap-2">
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={() => onViewDetails(watercraft.id)}
          aria-label={t("details")}
        >
          <MoreHorizontal className="h-4 w-4 mr-1 shrink-0" />
          <span className="truncate">{t("details")}</span>
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={() => onScheduleJaunt(watercraft.id)}
          disabled={watercraft.status !== "available"}
          aria-label={t("book")}
        >
          <Calendar className="h-4 w-4 mr-1 shrink-0" />
          <span className="truncate">{t("book")}</span>
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={() => onScheduleMaintenance(watercraft.id)}
          aria-label={t("maintain")}
        >
          <Wrench className="h-4 w-4 mr-1 shrink-0" />
          <span className="truncate">{t("maintain")}</span>
        </Button>
      </CardFooter>
    </Card>
  );
};

export default WatercraftCard;
