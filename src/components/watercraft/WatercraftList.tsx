
import { useState, useEffect } from 'react';
import { Watercraft, Boat, WatercraftFilters } from '@/types';
import { WatercraftFilters as FiltersComponent } from './WatercraftFilters';
import WatercraftCard from './WatercraftCard';
import { useLanguage } from '@/context/LanguageContext';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2 } from 'lucide-react';
import { TranslationKey } from '@/i18n/types';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useIsMobile } from '@/hooks/use-mobile';

interface WatercraftListProps {
  watercrafts: (Watercraft | Boat)[];
  onFilterChange: (filters: WatercraftFilters) => void;
  onToggleFavorite: (watercraftId: string) => void;
  onCheckout: (watercraft: Watercraft | Boat) => void;
  onViewDetails: (watercraft: Watercraft | Boat) => void;
}

export function WatercraftList({
  watercrafts,
  onFilterChange,
  onToggleFavorite,
  onCheckout,
  onViewDetails
}: WatercraftListProps) {
  const { t } = useLanguage();
  const [isLoading, setIsLoading] = useState(true);
  const isMobile = useIsMobile();
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);
  
  const renderSkeletons = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {Array.from({ length: 8 }).map((_, index) => (
        <div key={index} className="bg-card rounded-lg shadow-sm border p-4 h-64 animate-pulse">
          <div className="h-5 bg-muted rounded w-3/4 mb-4"></div>
          <div className="grid grid-cols-2 gap-2 mb-4">
            <div className="h-4 bg-muted rounded"></div>
            <div className="h-4 bg-muted rounded"></div>
          </div>
          <div className="h-24 bg-muted rounded mb-4"></div>
          <div className="flex justify-between">
            <div className="h-8 bg-muted rounded w-20"></div>
            <div className="h-8 bg-muted rounded w-20"></div>
          </div>
        </div>
      ))}
    </div>
  );
  
  if (isLoading) {
    return (
      <div className="space-y-4">
        <FiltersComponent onFilterChange={onFilterChange} />
        
        <div className="text-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
          <p className="text-muted-foreground">{t('loading_watercrafts')}</p>
        </div>
        
        {renderSkeletons()}
      </div>
    );
  }
  
  const groupByType = (crafts: (Watercraft | Boat)[]) => {
    return crafts.reduce((acc, craft) => {
      if (!acc[craft.type]) {
        acc[craft.type] = [];
      }
      acc[craft.type].push(craft);
      return acc;
    }, {} as Record<string, (Watercraft | Boat)[]>);
  };
  
  const groupedWatercrafts = groupByType(watercrafts);
  
  // Helper function to convert watercraft type to translation key
  const typeToTranslationKey = (type: string): TranslationKey => {
    return type as TranslationKey;
  };
  
  return (
    <div className="space-y-4">
      <FiltersComponent onFilterChange={onFilterChange} />
      
      {watercrafts.length === 0 ? (
        <div className="text-center py-8 bg-muted/20 rounded-lg border">
          <p className="text-muted-foreground">{t('no_watercrafts_found')}</p>
          <p className="text-sm text-muted-foreground">{t('try_adjusting_filters')}</p>
        </div>
      ) : (
        <Tabs defaultValue="all">
          {isMobile ? (
            <ScrollArea className="w-full pb-2">
              <TabsList className="inline-flex w-auto mb-2">
                <TabsTrigger value="all">{t('all') || 'All'}</TabsTrigger>
                
                {Object.keys(groupedWatercrafts).map(type => (
                  <TabsTrigger key={type} value={type}>
                    {t(typeToTranslationKey(type))} ({groupedWatercrafts[type].length})
                  </TabsTrigger>
                ))}
              </TabsList>
            </ScrollArea>
          ) : (
            <TabsList>
              <TabsTrigger value="all">{t('all') || 'All'}</TabsTrigger>
              
              {Object.keys(groupedWatercrafts).map(type => (
                <TabsTrigger key={type} value={type}>
                  {t(typeToTranslationKey(type))} ({groupedWatercrafts[type].length})
                </TabsTrigger>
              ))}
            </TabsList>
          )}
          
          <TabsContent value="all" className="pt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {watercrafts.map(watercraft => (
                <WatercraftCard
                  key={watercraft.id}
                  watercraft={watercraft}
                  onToggleFavorite={() => onToggleFavorite(watercraft.id)}
                  onScheduleJaunt={() => onCheckout(watercraft)}
                  onScheduleMaintenance={() => console.log('Maintenance scheduled for', watercraft.id)}
                  onViewDetails={() => onViewDetails(watercraft)}
                />
              ))}
            </div>
          </TabsContent>
          
          {Object.keys(groupedWatercrafts).map(type => (
            <TabsContent key={type} value={type} className="pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {groupedWatercrafts[type].map(watercraft => (
                  <WatercraftCard
                    key={watercraft.id}
                    watercraft={watercraft}
                    onToggleFavorite={() => onToggleFavorite(watercraft.id)}
                    onScheduleJaunt={() => onCheckout(watercraft)}
                    onScheduleMaintenance={() => console.log('Maintenance scheduled for', watercraft.id)}
                    onViewDetails={() => onViewDetails(watercraft)}
                  />
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      )}
    </div>
  );
}
