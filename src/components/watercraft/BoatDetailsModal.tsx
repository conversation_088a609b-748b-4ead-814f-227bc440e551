
import { useState } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { Watercraft, Boat } from '@/types';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Ship } from 'lucide-react';
import { DetailsTabs } from './details/DetailsTabs';

interface BoatDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  watercraft: Watercraft | Boat | null;
  onCheckout?: (watercraft: Watercraft | Boat) => void;
  onToggleFavorite?: (watercraftId: string) => void;
  isFavorite?: boolean;
}

export function BoatDetailsModal({
  isOpen,
  onClose,
  watercraft,
  onCheckout,
  onToggleFavorite,
  isFavorite = false
}: BoatDetailsModalProps) {
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState('details');
  
  if (!watercraft) return null;
  
  const isBoat = watercraft.type === 'boat';
  const boat = isBoat ? watercraft as Boat : null;
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Ship className="h-5 w-5 text-marine-600" />
            <span>{watercraft.name}</span>
            <span className="text-sm font-normal text-muted-foreground">
              ({isBoat && boat ? `${t('boat')} (${boat.boatType})` : t(watercraft.type)})
            </span>
          </DialogTitle>
        </DialogHeader>
        
        <DetailsTabs 
          watercraft={watercraft} 
          activeTab={activeTab} 
          onTabChange={setActiveTab} 
        />
        
        <DialogFooter className="flex justify-between sm:justify-between">
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onToggleFavorite && onToggleFavorite(watercraft.id)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill={isFavorite ? "currentColor" : "none"}
                stroke="currentColor"
                className={`h-4 w-4 ${isFavorite ? 'text-red-500' : ''}`}
                strokeWidth="2"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12z"
                />
              </svg>
            </Button>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={onClose}>
              {t('close')}
            </Button>
            <Button 
              size="sm" 
              onClick={() => watercraft && onCheckout && onCheckout(watercraft)}
              disabled={watercraft.status !== 'available'}
            >
              {watercraft.status === 'available' ? t('checkout') : t('unavailable')}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
