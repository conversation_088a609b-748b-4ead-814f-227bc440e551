
import { useLanguage } from '@/context/LanguageContext';
import { Button } from '@/components/ui/button';

interface FavoritesToggleProps {
  showFavoritesOnly: boolean;
  onToggle: () => void;
}

export function FavoritesToggle({ showFavoritesOnly, onToggle }: FavoritesToggleProps) {
  const { t } = useLanguage();
  
  return (
    <Button
      variant={showFavoritesOnly ? "default" : "outline"}
      size="sm"
      onClick={onToggle}
      className="w-full"
    >
      {showFavoritesOnly ? t('showing_favorites_only') : t('show_favorites_only')}
    </Button>
  );
}
