
import { useLanguage } from '@/context/LanguageContext';
import { BoatType } from '@/types';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';

interface BoatTypeFilterProps {
  boatTypes: BoatType[];
  selectedBoatTypes: string[];
  onChange: (boatTypes: string[]) => void;
}

export function BoatTypeFilter({ boatTypes, selectedBoatTypes, onChange }: BoatTypeFilterProps) {
  const { t } = useLanguage();
  
  return (
    <div className="w-full">
      <h4 className="text-sm font-medium mb-2">{t('boat_type')}</h4>
      <ToggleGroup 
        type="multiple" 
        className="flex flex-wrap gap-1 max-w-full"
        value={selectedBoatTypes}
        onValueChange={onChange}
      >
        {boatTypes.map(type => (
          <ToggleGroupItem 
            key={type} 
            value={type}
            className="text-xs h-7 px-2 flex-shrink-0"
          >
            {type}
          </ToggleGroupItem>
        ))}
      </ToggleGroup>
    </div>
  );
}
