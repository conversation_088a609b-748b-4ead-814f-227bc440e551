
import { useLanguage } from '@/context/LanguageContext';
import { Slider } from '@/components/ui/slider';
import { TranslatedText } from '@/components/i18n';
import { TranslationKey } from '@/i18n/types';

interface RangeSliderProps {
  title: TranslationKey;
  min: number;
  max: number;
  step: number;
  unit?: string;
  value: [number, number];
  onChange: (value: number[]) => void;
}

export function RangeSlider({ title, min, max, step, unit, value, onChange }: RangeSliderProps) {
  const { t } = useLanguage();
  
  return (
    <div>
      <h4 className="text-sm font-medium mb-2">
        <TranslatedText translationKey={title} />: {value[0]} - {value[1]}{unit ? ` ${unit}` : ''}
      </h4>
      <Slider
        value={[value[0], value[1]]}
        min={min}
        max={max}
        step={step}
        onValueChange={onChange}
        className="my-4"
      />
    </div>
  );
}
