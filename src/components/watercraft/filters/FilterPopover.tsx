
import { useState } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { 
  WatercraftType, 
  BoatType, 
  WatercraftStatus, 
  WatercraftFilters 
} from '@/types';
import { Button } from '@/components/ui/button';
import { Filter, X } from 'lucide-react';
import { 
  Popover,
  PopoverContent,
  PopoverTrigger 
} from '@/components/ui/popover';
import { TypeFilter } from './TypeFilter';
import { BoatTypeFilter } from './BoatTypeFilter';
import { StatusFilter } from './StatusFilter';
import { RangeSlider } from './RangeSlider';
import { FavoritesToggle } from './FavoritesToggle';
import { FilterPopoverHeader } from './FilterPopoverHeader';
import { SkillLevelFilter } from './SkillLevelFilter';
import { WeightRangeFilter } from './WeightRangeFilter';
import { useIsMobile } from '@/hooks/use-mobile';

interface FilterPopoverProps {
  filters: WatercraftFilters;
  onFilterChange: (filters: WatercraftFilters) => void;
  onResetFilters: () => void;
}

export function FilterPopover({ filters, onFilterChange, onResetFilters }: FilterPopoverProps) {
  const [showFilters, setShowFilters] = useState(false);
  const isMobile = useIsMobile();
  
  // Define filter options
  const watercraftTypes: WatercraftType[] = ['boat', 'kayak', 'PB', 'launch', 'surfski', 'coastal'];
  const boatTypes: BoatType[] = ['1x', '2x', '2-', '4x', '4+', '3x', '8+'];
  const statusOptions: WatercraftStatus[] = ['available', 'in-use', 'maintenance', 'regatta'];
  
  // Count active filters (excluding search)
  const activeFilterCount = 
    (filters.type.length > 0 ? 1 : 0) +
    (filters.status.length > 0 ? 1 : 0) +
    (filters.showFavoritesOnly ? 1 : 0);
  
  // Filter handlers
  const handleTypeToggle = (types: string[]) => {
    onFilterChange({ ...filters, type: types });
  };
  
  const handleBoatTypeToggle = (types: string[]) => {
    onFilterChange({ ...filters, boatTypes: types });
  };
  
  const handleStatusToggle = (status: string[]) => {
    onFilterChange({ ...filters, status: status as WatercraftStatus[] });
  };
  
  const handleSkillLevelChange = (value: number[]) => {
    onFilterChange({ ...filters, skillLevel: [value[0], value[1]] as [number, number] });
  };
  
  const handleWeightRangeChange = (value: number[]) => {
    onFilterChange({ ...filters, weightRange: [value[0], value[1]] as [number, number] });
  };
  
  const handleFavoritesToggle = () => {
    onFilterChange({ ...filters, showFavoritesOnly: !filters.showFavoritesOnly });
  };
  
  return (
    <Popover open={showFilters} onOpenChange={setShowFilters}>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          className="flex gap-2 w-full sm:w-auto justify-center"
        >
          <Filter className="h-4 w-4" />
          <FilterPopoverTrigger activeFilterCount={activeFilterCount} />
        </Button>
      </PopoverTrigger>
      <PopoverContent 
        side={isMobile ? "bottom" : "right"} 
        align={isMobile ? "center" : "start"} 
        className="w-[calc(100vw-2rem)] sm:w-96 max-w-md"
        avoidCollisions={true}
        sticky="always"
        sideOffset={5}
      >
        <div className="space-y-4 max-h-[50vh] overflow-y-auto pr-1 pb-1">
          {/* Filter Header */}
          <FilterPopoverHeader onResetFilters={onResetFilters} />
          
          {/* Type Filter */}
          <TypeFilter 
            types={watercraftTypes} 
            selectedTypes={filters.type} 
            onChange={handleTypeToggle} 
          />
          
          {/* Boat Type Filter (only show if 'boat' is selected) */}
          {filters.type.includes('boat') && (
            <BoatTypeFilter 
              boatTypes={boatTypes} 
              selectedBoatTypes={filters.boatTypes || []} 
              onChange={handleBoatTypeToggle} 
            />
          )}
          
          {/* Status Filter */}
          <StatusFilter 
            statusOptions={statusOptions} 
            selectedStatus={filters.status} 
            onChange={handleStatusToggle} 
          />
          
          {/* Skill Level Slider */}
          <SkillLevelFilter
            value={filters.skillLevel || [1, 5]}
            onChange={handleSkillLevelChange}
          />
          
          {/* Weight Range Slider (only for boats) */}
          {filters.type.includes('boat') && (
            <WeightRangeFilter
              value={filters.weightRange || [50, 100]}
              onChange={handleWeightRangeChange}
            />
          )}
          
          {/* Favorites Toggle */}
          <FavoritesToggle 
            showFavoritesOnly={filters.showFavoritesOnly} 
            onToggle={handleFavoritesToggle} 
          />
        </div>
      </PopoverContent>
    </Popover>
  );
}

// Small component for the filter button content
function FilterPopoverTrigger({ activeFilterCount }: { activeFilterCount: number }) {
  const { t } = useLanguage();
  return (
    <>
      <span className="whitespace-nowrap overflow-hidden text-ellipsis">{t('filters') || 'Filters'}</span>
      {activeFilterCount > 0 && (
        <span className="inline-flex items-center justify-center rounded-full bg-primary w-5 h-5 text-[10px] text-white font-medium">
          {activeFilterCount}
        </span>
      )}
    </>
  );
}
