
import { useLanguage } from '@/context/LanguageContext';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';

interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
}

export function SearchInput({ value, onChange }: SearchInputProps) {
  const { t } = useLanguage();
  
  return (
    <div className="relative w-full">
      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
      <Input
        placeholder={t('search_watercraft') || 'Search watercraft'}
        className="pl-10 w-full pr-3 overflow-hidden text-ellipsis"
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
    </div>
  );
}
