
import { useLanguage } from '@/context/LanguageContext';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

interface FilterPopoverHeaderProps {
  onResetFilters: () => void;
}

export function FilterPopoverHeader({ onResetFilters }: FilterPopoverHeaderProps) {
  const { t } = useLanguage();
  
  return (
    <div className="flex items-center justify-between">
      <h3 className="font-medium text-sm">{t('filter_watercraft')}</h3>
      <Button
        variant="ghost"
        size="sm"
        className="h-8 text-xs"
        onClick={onResetFilters}
      >
        <X className="h-3 w-3 mr-1" />
        {t('reset')}
      </Button>
    </div>
  );
}
