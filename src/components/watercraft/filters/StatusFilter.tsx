
import { useLanguage } from '@/context/LanguageContext';
import { WatercraftStatus } from '@/types';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';

interface StatusFilterProps {
  statusOptions: WatercraftStatus[];
  selectedStatus: string[];
  onChange: (status: string[]) => void;
}

export function StatusFilter({ statusOptions, selectedStatus, onChange }: StatusFilterProps) {
  const { t } = useLanguage();
  
  return (
    <div className="w-full">
      <h4 className="text-sm font-medium mb-2">{t('status')}</h4>
      <ToggleGroup 
        type="multiple" 
        className="flex flex-wrap gap-1 max-w-full"
        value={selectedStatus}
        onValueChange={onChange}
      >
        {statusOptions.map(status => (
          <ToggleGroupItem 
            key={status} 
            value={status}
            className="text-xs h-7 px-2 flex-shrink-0"
          >
            {t(status === 'in-use' ? 'status_in_use' : `status_${status}`)}
          </ToggleGroupItem>
        ))}
      </ToggleGroup>
    </div>
  );
}
