
import { useLanguage } from '@/context/LanguageContext';
import { RangeSlider } from './RangeSlider';

interface WeightRangeFilterProps {
  value: [number, number];
  onChange: (value: number[]) => void;
}

export function WeightRangeFilter({ value, onChange }: WeightRangeFilterProps) {
  const { t } = useLanguage();
  
  return (
    <RangeSlider
      title="weight_range"
      min={50}
      max={100}
      step={5}
      unit="kg"
      value={value}
      onChange={onChange}
    />
  );
}
