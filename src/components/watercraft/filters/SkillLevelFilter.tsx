
import { useLanguage } from '@/context/LanguageContext';
import { RangeSlider } from './RangeSlider';

interface SkillLevelFilterProps {
  value: [number, number];
  onChange: (value: number[]) => void;
}

export function SkillLevelFilter({ value, onChange }: SkillLevelFilterProps) {
  const { t } = useLanguage();
  
  return (
    <RangeSlider 
      title="skill_level"
      min={1}
      max={3}
      step={1}
      value={value}
      onChange={onChange}
    />
  );
}
