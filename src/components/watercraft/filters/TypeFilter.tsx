
import { useLanguage } from '@/context/LanguageContext';
import { WatercraftType } from '@/types';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';

interface TypeFilterProps {
  types: WatercraftType[];
  selectedTypes: string[];
  onChange: (types: string[]) => void;
}

export function TypeFilter({ types, selectedTypes, onChange }: TypeFilterProps) {
  const { t } = useLanguage();
  
  return (
    <div className="w-full">
      <h4 className="text-sm font-medium mb-2">{t('watercraft_type')}</h4>
      <ToggleGroup 
        type="multiple" 
        className="flex flex-wrap gap-1 max-w-full"
        value={selectedTypes}
        onValueChange={onChange}
      >
        {types.map(type => (
          <ToggleGroupItem 
            key={type} 
            value={type}
            className="text-xs h-7 px-2 flex-shrink-0"
          >
            {t(type)}
          </ToggleGroupItem>
        ))}
      </ToggleGroup>
    </div>
  );
}
