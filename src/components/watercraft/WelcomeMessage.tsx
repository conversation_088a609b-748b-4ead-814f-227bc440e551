
import { useEffect, useState } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { User } from '@/types';

interface WelcomeMessageProps {
  user: User | null;
}

export function WelcomeMessage({ user }: WelcomeMessageProps) {
  const { t } = useLanguage();
  const [showWelcome, setShowWelcome] = useState(true);
  
  useEffect(() => {
    // Auto-hide welcome message after 4 seconds
    const timer = setTimeout(() => {
      setShowWelcome(false);
    }, 4000);
    
    return () => clearTimeout(timer);
  }, []);
  
  if (!user || !showWelcome) return null;
  
  return (
    <div className="mb-6 p-4 bg-blue-50 text-blue-800 rounded-lg border border-blue-200 animate-in fade-in">
      <h2 className="text-xl font-medium">
        {t('welcome_message')}
      </h2>
    </div>
  );
}
