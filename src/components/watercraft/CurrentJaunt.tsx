
import { useState } from 'react';
import { Jaunt, Watercraft, Boat } from '@/types';
import { useLanguage } from '@/context/LanguageContext';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, <PERSON>Footer, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Clock, Ship, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';
import { utcToLocalTime } from '@/utils/timeUtils';

interface CurrentJauntProps {
  jaunt: Jaunt;
  watercraft: Watercraft | Boat;
  onCheckin: (jaunt: Jaunt, comment?: string) => void;
  onReportIssue: (jaunt: Jaunt) => void;
}

export function CurrentJaunt({ jaunt, watercraft, onCheckin, onReportIssue }: CurrentJauntProps) {
  const { t } = useLanguage();
  const [comment, setComment] = useState<string>('');
  
  const now = new Date();
  // Convert UTC times to local for display
  const startTime = utcToLocalTime(new Date(jaunt.startTime));
  const plannedEndTime = utcToLocalTime(new Date(jaunt.plannedEndTime));
  
  // Calculate time remaining
  const timeRemaining = plannedEndTime.getTime() - now.getTime();
  const isOverdue = timeRemaining < 0;
  
  // Format time remaining in hours and minutes
  const formatTimeRemaining = () => {
    const absTimeRemaining = Math.abs(timeRemaining);
    const hours = Math.floor(absTimeRemaining / (1000 * 60 * 60));
    const minutes = Math.floor((absTimeRemaining % (1000 * 60 * 60)) / (1000 * 60));
    
    if (isOverdue) {
      return `${hours}h ${minutes}m ${t('overdue')}`;
    } else {
      return `${hours}h ${minutes}m ${t('remaining')}`;
    }
  };
  
  const handleCheckin = () => {
    onCheckin(jaunt, comment || undefined);
    toast.success(t('checked_in_success'));
  };
  
  const isBoat = watercraft.type === 'boat';
  const boat = isBoat ? watercraft as Boat : null;
  
  return (
    <Card className={`border-l-4 ${isOverdue ? 'border-l-red-500' : 'border-l-blue-500'}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Ship className="h-5 w-5 text-blue-500" />
            <span>{t('current_jaunt')}</span>
          </div>
          <div className={`text-sm font-normal rounded-full px-3 py-1 ${
            isOverdue ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
          }`}>
            <Clock className="inline-block h-3 w-3 mr-1" />
            {formatTimeRemaining()}
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="pb-2">
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div>
            <p className="text-gray-500">{t('watercraft')}</p>
            <p className="font-medium">
              {watercraft.name} 
              <span className="text-xs ml-1 font-normal text-gray-500">
                ({isBoat && boat ? `${t('boat')} (${boat.boatType})` : t(watercraft.type)})
              </span>
            </p>
          </div>
          
          <div>
            <p className="text-gray-500">{t('location')}</p>
            <p className="font-medium">{watercraft.location}</p>
          </div>
          
          <div>
            <p className="text-gray-500">{t('start_time')}</p>
            <p className="font-medium">{startTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</p>
          </div>
          
          <div>
            <p className="text-gray-500">{t('planned_end_time')}</p>
            <p className="font-medium">{plannedEndTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</p>
          </div>
        </div>
        
        <div className="mt-3">
          <Textarea
            placeholder={t('comment_placeholder')}
            className="h-20 text-sm"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
          />
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-between pt-2">
        <Button 
          variant="outline" 
          size="sm"
          className="text-red-600 border-red-200 hover:bg-red-50"
          onClick={() => onReportIssue(jaunt)}
        >
          <AlertTriangle className="h-4 w-4 mr-1" />
          {t('report_issue')}
        </Button>
        
        <Button onClick={handleCheckin} size="sm">
          {t('check_in')}
        </Button>
      </CardFooter>
    </Card>
  );
}
