
import { useState } from 'react';
import { Watercraft, Boat } from '@/types';
import { useLanguage } from '@/context/LanguageContext';
import { useAuth } from '@/context/AuthContext';
import { Dialog, DialogContent, DialogHeader, Di<PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Clock, Ship } from 'lucide-react';
import { toUTC, formatDateForInput } from '@/utils/timeUtils';

interface JauntModalProps {
  isOpen: boolean;
  onClose: () => void;
  watercraft: Watercraft | Boat | null;
  onSubmit: (data: {
    startTime: Date;
    plannedEndTime: Date;
    comment?: string;
  }) => void;
}

export function JauntModal({ isOpen, onClose, watercraft, onSubmit }: JauntModalProps) {
  const { t } = useLanguage();
  const { user } = useAuth();
  
  const now = new Date();
  const twoHoursLater = new Date(now.getTime() + 2 * 60 * 60 * 1000);
  
  const [startTime, setStartTime] = useState(now);
  const [plannedEndTime, setPlannedEndTime] = useState(twoHoursLater);
  const [comment, setComment] = useState('');
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Convert dates to UTC before submitting
    onSubmit({
      startTime: toUTC(startTime),
      plannedEndTime: toUTC(plannedEndTime),
      comment: comment.trim() || undefined,
    });
    
    // Reset form
    setStartTime(now);
    setPlannedEndTime(twoHoursLater);
    setComment('');
  };
  
  if (!watercraft) return null;
  
  const isBoat = watercraft.type === 'boat';
  const boat = isBoat ? watercraft as Boat : null;
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl font-bold">
            <Ship className="h-5 w-5" /> {t('checkout_watercraft')}
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-1">
            <Label>{t('watercraft')}</Label>
            <div className="text-lg font-medium flex items-center gap-2">
              {watercraft.name} 
              <span className="text-sm font-normal text-gray-500">
                ({isBoat && boat ? `${t('boat')} (${boat.boatType})` : t(watercraft.type)})
              </span>
            </div>
          </div>
          
          <div className="space-y-1">
            <Label htmlFor="start-time">{t('start_time')}</Label>
            <div className="relative">
              <Clock className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
              <Input
                id="start-time"
                type="datetime-local"
                className="pl-10"
                value={formatDateForInput(startTime)}
                onChange={(e) => setStartTime(new Date(e.target.value))}
                required
              />
            </div>
          </div>
          
          <div className="space-y-1">
            <Label htmlFor="end-time">{t('planned_end_time')}</Label>
            <div className="relative">
              <Clock className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
              <Input
                id="end-time"
                type="datetime-local"
                className="pl-10"
                value={formatDateForInput(plannedEndTime)}
                onChange={(e) => setPlannedEndTime(new Date(e.target.value))}
                required
              />
            </div>
          </div>
          
          <div className="space-y-1">
            <Label htmlFor="comment">{t('comment')} <span className="text-gray-500">({t('optional')})</span></Label>
            <Textarea
              id="comment"
              placeholder={t('comment_placeholder')}
              className="min-h-[100px]"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
            />
          </div>
          
          <DialogFooter className="sm:justify-between">
            <Button type="button" variant="outline" onClick={onClose}>{t('cancel')}</Button>
            <Button type="submit">{t('confirm_checkout')}</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
