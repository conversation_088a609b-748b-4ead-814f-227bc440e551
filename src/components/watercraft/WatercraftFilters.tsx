
import { useState, useEffect } from 'react';
import { type WatercraftFilters as WatercraftFiltersType } from '@/types';
import { SearchInput } from './filters/SearchInput';
import { FilterPopover } from './filters/FilterPopover';
import { useIsMobile } from '@/hooks/use-mobile';

interface WatercraftFiltersProps {
  onFilterChange: (filters: WatercraftFiltersType) => void;
}

const defaultFilters: WatercraftFiltersType = {
  search: '',
  type: [],
  status: [],
  showFavoritesOnly: false,
  skillLevel: [1, 3],
  weightRange: [50, 100]
};

export function WatercraftFilters({ onFilterChange }: WatercraftFiltersProps) {
  const [filters, setFilters] = useState<WatercraftFiltersType>(defaultFilters);
  const isMobile = useIsMobile();
  
  const handleSearchChange = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
  };
  
  const handleFilterChange = (updatedFilters: WatercraftFiltersType) => {
    setFilters(updatedFilters);
  };
  
  const resetFilters = () => {
    setFilters(defaultFilters);
  };
  
  // Update parent component when filters change
  useEffect(() => {
    onFilterChange(filters);
  }, [filters, onFilterChange]);
  
  return (
    <div className="mb-6 space-y-3 px-1 overflow-x-hidden">
      <div className={`flex ${isMobile ? 'flex-col' : 'flex-row'} gap-2 w-full`}>
        {/* Search Input */}
        <div className="w-full">
          <SearchInput 
            value={filters.search} 
            onChange={handleSearchChange} 
          />
        </div>
        
        {/* Filter Button */}
        <div className={`${isMobile ? 'w-full' : 'w-auto'}`}>
          <FilterPopover 
            filters={filters}
            onFilterChange={handleFilterChange}
            onResetFilters={resetFilters}
          />
        </div>
      </div>
    </div>
  );
}
