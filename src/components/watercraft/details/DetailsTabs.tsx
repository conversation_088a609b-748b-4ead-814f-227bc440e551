
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';
import { Info, Map, History } from 'lucide-react';
import { useLanguage } from '@/context/LanguageContext';
import { Watercraft, Boat } from '@/types';
import { DetailsTabContent } from './DetailsTabContent';
import { LocationTabContent } from './LocationTabContent';
import { HistoryTabContent } from './HistoryTabContent';

interface DetailsTabsProps {
  watercraft: Watercraft | Boat;
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export function DetailsTabs({ watercraft, activeTab, onTabChange }: DetailsTabsProps) {
  const { t } = useLanguage();
  
  return (
    <Tabs value={activeTab} onValueChange={onTabChange}>
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="details" className="flex items-center gap-1">
          <Info className="h-4 w-4" />
          <span className="hidden sm:inline">{t('details')}</span>
        </TabsTrigger>
        <TabsTrigger value="location" className="flex items-center gap-1">
          <Map className="h-4 w-4" />
          <span className="hidden sm:inline">{t('location')}</span>
        </TabsTrigger>
        <TabsTrigger value="history" className="flex items-center gap-1">
          <History className="h-4 w-4" />
          <span className="hidden sm:inline">{t('history')}</span>
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="details" className="pt-4">
        <DetailsTabContent watercraft={watercraft} />
      </TabsContent>
      
      <TabsContent value="location" className="pt-4">
        <LocationTabContent location={watercraft.location} />
      </TabsContent>
      
      <TabsContent value="history" className="pt-4">
        <HistoryTabContent />
      </TabsContent>
    </Tabs>
  );
}
