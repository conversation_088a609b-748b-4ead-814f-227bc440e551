
import { Card, CardContent } from '@/components/ui/card';
import { useLanguage } from '@/context/LanguageContext';

interface LocationTabContentProps {
  location: string;
}

export function LocationTabContent({ location }: LocationTabContentProps) {
  const { t } = useLanguage();
  
  return (
    <Card>
      <CardContent className="p-4">
        <h3 className="font-medium mb-2">{t('current_location')}</h3>
        <p className="mb-4">{location}</p>
        
        <div className="w-full h-[200px] bg-gray-100 rounded-lg flex items-center justify-center">
          <p className="text-muted-foreground">{t('location_map_placeholder')}</p>
        </div>
      </CardContent>
    </Card>
  );
}
