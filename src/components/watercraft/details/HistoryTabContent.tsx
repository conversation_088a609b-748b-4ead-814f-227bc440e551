
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/context/LanguageContext';

export function HistoryTabContent() {
  const { t } = useLanguage();
  
  // This would typically come from props or an API call
  const historyEntries = [
    {
      user: '<PERSON>',
      date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      duration: '1h 20m'
    },
    {
      user: '<PERSON>',
      date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      duration: '2h 05m'
    },
    {
      user: '<PERSON>',
      date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      duration: '45m'
    }
  ];
  
  return (
    <Card>
      <CardContent className="p-4">
        <h3 className="font-medium mb-2">{t('usage_history')}</h3>
        
        <div className="space-y-3">
          {historyEntries.map((entry, index) => (
            <div key={index} className="flex justify-between items-center border-b pb-2 last:border-0 last:pb-0">
              <div>
                <p className="font-medium">{entry.user}</p>
                <p className="text-sm text-muted-foreground">
                  {entry.date.toLocaleDateString(undefined, {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                  })}
                </p>
              </div>
              <Badge variant="outline">{entry.duration}</Badge>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
