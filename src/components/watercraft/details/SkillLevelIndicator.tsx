
import React from 'react';

interface SkillLevelIndicatorProps {
  level: number;
}

export function SkillLevelIndicator({ level }: SkillLevelIndicatorProps) {
  // Normalize level to ensure it's within the 1-3 range
  const normalizedLevel = Math.max(1, Math.min(3, level));
  
  return (
    <div className="flex items-center gap-1">
      {[1, 2, 3].map((i) => (
        <div 
          key={i} 
          className={`h-2 w-6 rounded-sm ${
            i <= normalizedLevel ? 'bg-primary' : 'bg-muted'
          }`}
        />
      ))}
      <span className="ml-2 text-sm text-muted-foreground">
        {normalizedLevel}/3
      </span>
    </div>
  );
}
