
import { Badge } from '@/components/ui/badge';
import { Check, Clock, AlertTriangle, Users } from 'lucide-react';
import { useLanguage } from '@/context/LanguageContext';
import { TranslatedText } from '@/components/i18n';
import { TranslationKey } from '@/i18n/types';

interface StatusBadgeProps {
  status: string;
}

export function StatusBadge({ status }: StatusBadgeProps) {
  const { t } = useLanguage();
  
  // Map the status to a valid TranslationKey
  const getStatusTranslationKey = (status: string): TranslationKey => {
    const keyMap: Record<string, TranslationKey> = {
      'available': 'available',
      'in-use': 'in-use',
      'maintenance': 'maintenance',
      'regatta': 'regatta'
    };
    
    return keyMap[status] || ('status_' + status) as TranslationKey;
  };
  
  switch (status) {
    case 'available':
      return (
        <Badge variant="success" className="flex items-center gap-1">
          <Check className="h-3 w-3" />
          <TranslatedText translationKey="available" />
        </Badge>
      );
    case 'in-use':
      return (
        <Badge variant="info" className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          <TranslatedText translationKey="in-use" />
        </Badge>
      );
    case 'maintenance':
      return (
        <Badge variant="warning" className="flex items-center gap-1">
          <AlertTriangle className="h-3 w-3" />
          <TranslatedText translationKey="maintenance" />
        </Badge>
      );
    case 'regatta':
      return (
        <Badge variant="regatta" className="flex items-center gap-1">
          <Users className="h-3 w-3" />
          <TranslatedText translationKey="regatta" />
        </Badge>
      );
    default:
      return (
        <Badge variant="outline">
          {status} {/* Fallback to using the raw status string */}
        </Badge>
      );
  }
}
