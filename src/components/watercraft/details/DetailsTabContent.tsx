
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Watercraft, Boat } from '@/types';
import { useLanguage } from '@/context/LanguageContext';
import { StatusBadge } from './StatusBadge';
import { SkillLevelIndicator } from './SkillLevelIndicator';

interface DetailsTabContentProps {
  watercraft: Watercraft | Boat;
}

export function DetailsTabContent({ watercraft }: DetailsTabContentProps) {
  const { t } = useLanguage();
  const isBoat = watercraft.type === 'boat';
  const boat = isBoat ? watercraft as Boat : null;
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Card>
        <CardContent className="p-4">
          <h3 className="font-medium text-sm text-muted-foreground mb-2">
            {t('status')}
          </h3>
          <div><StatusBadge status={watercraft.status} /></div>
          
          <Separator className="my-3" />
          
          <h3 className="font-medium text-sm text-muted-foreground mb-2">
            {t('skill_level_required')}
          </h3>
          <div><SkillLevelIndicator level={watercraft.skillLevel} /></div>
          
          <Separator className="my-3" />
          
          <h3 className="font-medium text-sm text-muted-foreground mb-2">
            {t('ownership')}
          </h3>
          <p>{t(watercraft.ownershipType)}</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4">
          <h3 className="font-medium text-sm text-muted-foreground mb-2">
            {t('specifications')}
          </h3>
          
          <div className="space-y-2">
            <div className="grid grid-cols-2 text-sm">
              <span className="text-muted-foreground">{t('type')}:</span>
              <span>
                {isBoat && boat ? t('boat') : t(watercraft.type)}
              </span>
            </div>
            
            {isBoat && boat && (
              <>
                <div className="grid grid-cols-2 text-sm">
                  <span className="text-muted-foreground">{t('boat_type')}:</span>
                  <span>{boat.boatType}</span>
                </div>
                
                {boat.weightRange && (
                  <div className="grid grid-cols-2 text-sm">
                    <span className="text-muted-foreground">{t('weight_range')}:</span>
                    <span>
                      {boat.weightRange.min} - {boat.weightRange.max} kg
                    </span>
                  </div>
                )}
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
