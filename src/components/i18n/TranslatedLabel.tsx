
import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { TranslationKey } from '@/i18n/types';
import { Label } from '@/components/ui/label';

interface TranslatedLabelProps {
  translationKey: TranslationKey;
  htmlFor?: string;
  className?: string;
  required?: boolean;
}

/**
 * A reusable component that wraps form labels with the translation function
 */
export function TranslatedLabel({
  translationKey,
  htmlFor,
  className,
  required = false,
}: TranslatedLabelProps) {
  const { t } = useLanguage();
  
  return (
    <Label htmlFor={htmlFor} className={className}>
      {t(translationKey)}
      {required && <span className="text-red-500 ml-1">*</span>}
    </Label>
  );
}
