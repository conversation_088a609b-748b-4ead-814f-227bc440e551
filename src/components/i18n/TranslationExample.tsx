
import React from 'react';
import {
  TranslatedText,
  TranslatedHeading,
  TranslatedLabel,
  TranslatedButton,
  TranslatedParagraph,
  TranslatedInput,
  TranslatedFormattedText
} from '@/components/i18n';

export function TranslationExample() {
  return (
    <div className="space-y-6 p-4">
      <TranslatedHeading 
        translationKey="profile_title" 
        level="h1" 
        className="text-2xl font-bold"
      />
      
      <TranslatedParagraph 
        translationKey="profile_description" 
        className="text-muted-foreground"
      />
      
      <div className="space-y-4">
        <div>
          <TranslatedLabel 
            translationKey="settings_name" 
            htmlFor="name" 
            required
          />
          <TranslatedInput 
            placeholderKey="enter_team_name" 
            id="name" 
            className="mt-1"
          />
        </div>
        
        <div>
          <TranslatedLabel 
            translationKey="team_email" 
            htmlFor="email"
          />
          <TranslatedInput 
            placeholderKey="enter_team_email" 
            id="email" 
            className="mt-1"
          />
        </div>
      </div>
      
      <TranslatedFormattedText 
        translationKey="member_since" 
        values={{ date: '2023-01-15' }}
        className="text-sm text-muted-foreground"
      />
      
      <div className="flex gap-2">
        <TranslatedButton 
          translationKey="close" 
          variant="outline"
        />
        <TranslatedButton 
          translationKey="submit" 
          variant="default"
        />
      </div>
    </div>
  );
}
