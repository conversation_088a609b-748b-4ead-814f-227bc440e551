
import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { TranslationKey } from '@/i18n/types';

interface TranslatedParagraphProps {
  translationKey: TranslationKey;
  className?: string;
}

/**
 * A reusable component that wraps paragraph text with the translation function
 */
export function TranslatedParagraph({
  translationKey,
  className,
}: TranslatedParagraphProps) {
  const { t } = useLanguage();
  
  return (
    <p className={className}>
      {t(translationKey)}
    </p>
  );
}
