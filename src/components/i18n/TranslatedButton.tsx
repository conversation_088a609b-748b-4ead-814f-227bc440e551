
import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { TranslationKey } from '@/i18n/types';
import { Button } from '@/components/ui/button';
import { ButtonProps } from '@/components/ui/button';

interface TranslatedButtonProps extends Omit<ButtonProps, 'children'> {
  translationKey: TranslationKey;
}

/**
 * A reusable component that wraps buttons with the translation function
 */
export function TranslatedButton({
  translationKey,
  ...buttonProps
}: TranslatedButtonProps) {
  const { t } = useLanguage();
  
  return (
    <Button {...buttonProps}>
      {t(translationKey)}
    </Button>
  );
}
