
import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { TranslationKey } from '@/i18n/types';

type HeadingLevel = 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';

interface TranslatedHeadingProps {
  translationKey: TranslationKey;
  level?: HeadingLevel;
  className?: string;
}

/**
 * A reusable component that wraps headings with the translation function
 */
export function TranslatedHeading({
  translationKey,
  level = 'h2',
  className,
}: TranslatedHeadingProps) {
  const { t } = useLanguage();
  const Component = level;
  
  return (
    <Component className={className}>
      {t(translationKey)}
    </Component>
  );
}
