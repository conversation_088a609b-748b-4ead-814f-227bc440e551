
import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { TranslationKey } from '@/i18n/types';
import { Input } from '@/components/ui/input';

interface TranslatedInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'placeholder'> {
  placeholderKey: TranslationKey;
}

/**
 * A reusable component that wraps inputs with translated placeholders
 */
export function TranslatedInput({
  placeholderKey,
  ...inputProps
}: TranslatedInputProps) {
  const { t } = useLanguage();
  
  return (
    <Input
      placeholder={t(placeholderKey)}
      {...inputProps}
    />
  );
}
