
import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { TranslationKey } from '@/i18n/types';

interface TranslatedFormattedTextProps {
  translationKey: TranslationKey;
  values?: Record<string, string | number>;
  className?: string;
  as?: React.ElementType;
}

/**
 * A reusable component that wraps text with the translation function
 * and allows for variable interpolation
 */
export function TranslatedFormattedText({
  translationKey,
  values = {},
  className,
  as: Component = 'span',
}: TranslatedFormattedTextProps) {
  const { t } = useLanguage();
  const rawText = t(translationKey);
  
  // Simple variable interpolation
  const formattedText = Object.entries(values).reduce(
    (text, [key, value]) => {
      return text.replace(new RegExp(`{${key}}`, 'g'), String(value));
    },
    rawText
  );
  
  return <Component className={className}>{formattedText}</Component>;
}
