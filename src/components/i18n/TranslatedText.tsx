
import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { TranslationKey } from '@/i18n/types';

interface TranslatedTextProps {
  translationKey: TranslationKey;
  className?: string;
  as?: React.ElementType;
}

/**
 * A reusable component that wraps text with the translation function
 */
export function TranslatedText({
  translationKey,
  className,
  as: Component = 'span',
}: TranslatedTextProps) {
  const { t } = useLanguage();
  
  return (
    <Component className={className}>
      {t(translationKey)}
    </Component>
  );
}
