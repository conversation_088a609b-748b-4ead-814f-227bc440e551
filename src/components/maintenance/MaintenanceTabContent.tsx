
import { useLanguage } from '@/context/LanguageContext';
import { MaintenanceRequest, Watercraft, Boat, MaintenanceRequestStatus } from '@/types';
import { MaintenanceRequestCard } from './MaintenanceRequestCard';
import { MaintenanceRequestSkeleton } from './MaintenanceRequestSkeleton';

interface MaintenanceTabContentProps {
  status: 'all' | MaintenanceRequestStatus;
  loading: boolean;
  maintenanceRequests: MaintenanceRequest[];
  watercrafts: Record<string, Watercraft | Boat>;
}

export function MaintenanceTabContent({
  status,
  loading,
  maintenanceRequests,
  watercrafts
}: MaintenanceTabContentProps) {
  const { t } = useLanguage();
  
  const filteredRequests = maintenanceRequests.filter(
    (request) => status === 'all' || request.status === status
  );
  
  if (loading) {
    return <MaintenanceRequestSkeleton />;
  }
  
  if (filteredRequests.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">{t('no_maintenance_requests')}</p>
      </div>
    );
  }
  
  return (
    <div className="space-y-4">
      {filteredRequests.map((request) => (
        <MaintenanceRequestCard
          key={request.id}
          request={request}
          watercraft={watercrafts[request.watercraftId]}
        />
      ))}
    </div>
  );
}
