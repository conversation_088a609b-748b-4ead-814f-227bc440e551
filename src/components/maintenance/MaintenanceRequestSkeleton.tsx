
import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface MaintenanceRequestSkeletonProps {
  count?: number;
}

export function MaintenanceRequestSkeleton({ count = 3 }: MaintenanceRequestSkeletonProps) {
  return (
    <div className="grid gap-4">
      {Array.from({ length: count }).map((_, i) => (
        <Card key={i} className="animate-pulse">
          <CardHeader className="pb-2">
            <div className="h-5 bg-muted rounded w-1/3"></div>
          </CardHeader>
          <CardContent>
            <div className="h-4 bg-muted rounded mb-3 w-2/3"></div>
            <div className="h-4 bg-muted rounded mb-3 w-1/2"></div>
            <div className="h-4 bg-muted rounded w-full"></div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
