
import { useState } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Watercraft, Boat, MaintenanceIssueType } from '@/types';
import { toast } from 'sonner';
import { TranslatedButton } from '@/components/i18n';

interface MaintenanceRequestFormProps {
  watercrafts: Record<string, Watercraft | Boat>;
  onSubmit: (request: {
    watercraftId: string;
    watercraftState: 'useable' | 'unuseable';
    issueType: MaintenanceIssueType;
    note: string;
  }) => void;
  onCancel: () => void;
}

export function MaintenanceRequestForm({
  watercrafts,
  onSubmit,
  onCancel
}: MaintenanceRequestFormProps) {
  const { t } = useLanguage();
  
  // Form state
  const [selectedWatercraftId, setSelectedWatercraftId] = useState<string>('');
  const [issueType, setIssueType] = useState<MaintenanceIssueType>('broken');
  const [watercraftState, setWatercraftState] = useState<'useable' | 'unuseable'>('useable');
  const [note, setNote] = useState<string>('');
  
  const handleSubmit = () => {
    if (!selectedWatercraftId || !note.trim()) {
      toast.error(t('fill_all_fields'));
      return;
    }
    
    onSubmit({
      watercraftId: selectedWatercraftId,
      watercraftState,
      issueType,
      note
    });
    
    // Reset form state
    setSelectedWatercraftId('');
    setIssueType('broken');
    setWatercraftState('useable');
    setNote('');
  };
  
  return (
    <div className="space-y-4 py-4">
      <div className="space-y-2">
        <Label htmlFor="watercraft">{t('watercraft')}</Label>
        <Select 
          value={selectedWatercraftId} 
          onValueChange={setSelectedWatercraftId}
        >
          <SelectTrigger>
            <SelectValue placeholder={t('select_watercraft')} />
          </SelectTrigger>
          <SelectContent>
            {Object.values(watercrafts).map(craft => (
              <SelectItem key={craft.id} value={craft.id}>
                {craft.name} {craft.type === 'boat' ? `(${(craft as Boat).boatType})` : `(${craft.type})`}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="issueType">{t('issue_type')}</Label>
        <Select 
          value={issueType} 
          onValueChange={(value) => setIssueType(value as MaintenanceIssueType)}
        >
          <SelectTrigger>
            <SelectValue placeholder={t('select_issue_type')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="broken">{t('broken')}</SelectItem>
            <SelectItem value="damaged">{t('damaged')}</SelectItem>
            <SelectItem value="missing part">{t('missing_part')}</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="watercraftState">{t('watercraft_state')}</Label>
        <Select 
          value={watercraftState} 
          onValueChange={(value) => setWatercraftState(value as 'useable' | 'unuseable')}
        >
          <SelectTrigger>
            <SelectValue placeholder={t('select_watercraft_state')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="useable">{t('useable')}</SelectItem>
            <SelectItem value="unuseable">{t('unuseable')}</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="note">{t('description')}</Label>
        <Textarea 
          id="note" 
          placeholder={t('describe_issue')}
          value={note}
          onChange={(e) => setNote(e.target.value)}
          className="min-h-[100px]"
        />
      </div>
      
      <div className="flex justify-end space-x-2 pt-4">
        <Button variant="outline" onClick={onCancel}>
          {t('cancel')}
        </Button>
        <TranslatedButton 
          onClick={handleSubmit}
          translationKey="submit_request"
        />
      </div>
    </div>
  );
}
