
import { useLanguage } from '@/context/LanguageContext';
import { MaintenanceRequest, Watercraft, Boat, MaintenanceRequestStatus, MaintenanceIssueType } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, CheckCircle, Clock, Ship } from 'lucide-react';
import { TranslationKey } from '@/i18n/types';

interface MaintenanceRequestCardProps {
  request: MaintenanceRequest;
  watercraft: Watercraft | Boat | undefined;
}

export function MaintenanceRequestCard({ request, watercraft }: MaintenanceRequestCardProps) {
  const { t } = useLanguage();
  
  if (!watercraft) return null;
  
  const isBoat = watercraft.type === 'boat';
  const boat = isBoat ? watercraft as Boat : null;
  
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  const getStatusColorClasses = (status: MaintenanceRequestStatus) => {
    switch (status) {
      case 'open':
        return 'bg-yellow-100 text-yellow-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'closed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  const getStatusIcon = (status: MaintenanceRequestStatus) => {
    switch (status) {
      case 'open':
        return <AlertTriangle className="h-4 w-4" />;
      case 'in-progress':
        return <Clock className="h-4 w-4" />;
      case 'closed':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return null;
    }
  };
  
  // Helper function to safely convert status to a TranslationKey
  const statusToTranslationKey = (status: MaintenanceRequestStatus): TranslationKey => {
    // Map status values to valid translation keys
    const statusMap: Record<MaintenanceRequestStatus, TranslationKey> = {
      'open': 'open',
      'in-progress': 'in_progress',
      'closed': 'closed'
    };
    
    return statusMap[status] || status as TranslationKey;
  };
  
  // Helper function to safely convert issue type to a TranslationKey
  const issueTypeToTranslationKey = (issue: MaintenanceIssueType): TranslationKey => {
    // Map issue types to valid translation keys
    const issueMap: Record<MaintenanceIssueType, TranslationKey> = {
      'broken': 'broken',
      'damaged': 'damaged',
      'missing part': 'missing_part'
    };
    
    return issueMap[issue] || issue as TranslationKey;
  };
  
  return (
    <Card className="animate-in fade-in">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Ship className="h-5 w-5 text-marine-600" />
            <span>{watercraft.name}</span>
            <span className="text-xs font-normal text-muted-foreground">
              ({isBoat && boat ? `${t('boat')} (${boat.boatType})` : t(watercraft.type as TranslationKey)})
            </span>
          </div>
          <div className={`text-xs px-2 py-1 rounded-full flex items-center gap-1 ${getStatusColorClasses(request.status)}`}>
            {getStatusIcon(request.status)}
            <span>{t(statusToTranslationKey(request.status))}</span>
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-2 gap-3 text-sm mb-3">
          <div>
            <p className="text-gray-500">{t('issue_type')}</p>
            <p className="font-medium">{t(issueTypeToTranslationKey(request.issueType))}</p>
          </div>
          
          <div>
            <p className="text-gray-500">{t('request_date')}</p>
            <p className="font-medium">{formatDate(request.requestDate)}</p>
          </div>
          
          <div>
            <p className="text-gray-500">{t('watercraft_state')}</p>
            <p className="font-medium">{t(request.watercraftState as TranslationKey)}</p>
          </div>
          
          {request.status === 'closed' && request.resolutionDate && (
            <div>
              <p className="text-gray-500">{t('resolved_date')}</p>
              <p className="font-medium">{formatDate(request.resolutionDate)}</p>
            </div>
          )}
        </div>
        
        <div className="text-sm">
          <p className="text-gray-500">{t('description')}</p>
          <p className="mt-1">{request.note}</p>
        </div>
        
        {request.status === 'closed' && request.resolution && (
          <div className="mt-3 pt-3 border-t text-sm">
            <p className="text-gray-500">{t('resolution')}</p>
            <p className="mt-1">{request.resolution}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
