
import React from 'react';
import { useAuth } from '@/context/AuthContext';
import { useIsMobile } from '@/hooks/use-mobile';
import { MobileNavigation } from './MobileNavigation';
import { DesktopNavigation } from './DesktopNavigation';

export function NavigationHeader() {
  const { user } = useAuth();
  const isMobile = useIsMobile();
  
  // If the user is not logged in or loading, don't show the navigation
  if (!user) return null;
  
  return (
    <div className="border-b bg-background sticky top-0 z-50">
      <div className="container flex h-14 items-center px-4 sm:px-6">
        {/* User identifier for mobile */}
        {isMobile && (
          <div className="flex-1 flex items-center">
            <span className="font-medium text-sm truncate mr-2">{user?.name}</span>
          </div>
        )}
        
        {/* Conditional rendering based on screen size */}
        {isMobile ? <MobileNavigation /> : <DesktopNavigation />}
      </div>
    </div>
  );
}
