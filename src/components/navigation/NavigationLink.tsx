
import React from 'react';
import { useLocation } from 'react-router-dom';
import { navigationMenuTriggerStyle } from '@/components/ui/navigation-menu';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';

interface NavigationLinkProps {
  path: string;
  label: string;
  icon: LucideIcon;
  onClick: () => void;
  isMobile?: boolean;
}

export const NavigationLink: React.FC<NavigationLinkProps> = ({
  path,
  label,
  icon: Icon,
  onClick,
  isMobile = false
}) => {
  const location = useLocation();
  
  // Function to check if the current path matches a given path
  const isActive = (path: string) => {
    // Handle the root path separately
    if (path === '/' && location.pathname === '/') {
      return true;
    }
    // For other paths, check if the pathname starts with the path
    return path !== '/' && location.pathname.startsWith(path);
  };

  // Custom style for navigation items
  const navItemStyle = isMobile 
    ? cn(
        "flex items-center w-full p-3 rounded-md hover:bg-accent cursor-pointer mb-1",
        isActive(path) && "bg-primary/10 font-semibold"
      )
    : cn(
        navigationMenuTriggerStyle(),
        isActive(path) && "bg-primary/10 font-semibold border-b-2 border-primary"
      );

  return isMobile ? (
    <button 
      className={navItemStyle}
      onClick={onClick}
    >
      <Icon className="h-4 w-4 mr-3" />
      {label}
    </button>
  ) : (
    <div 
      className={navItemStyle}
      onClick={onClick}
    >
      <Icon className="h-4 w-4 mr-2" />
      {label}
    </div>
  );
};
