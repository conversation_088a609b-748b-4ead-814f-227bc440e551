
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/context/LanguageContext';
import { useAuth } from '@/context/AuthContext';
import { Menu, LogOut } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { getNavigationItems } from './navigationItems';
import { NavigationLink } from './NavigationLink';

export const MobileNavigation: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { user, logout } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  
  // If the user is not logged in, don't show navigation
  if (!user) return null;
  
  const isAdmin = user?.role === 'admin';
  
  // Handle navigation and close mobile menu
  const handleNavigation = (path: string) => {
    navigate(path);
    setIsOpen(false);
  };
  
  const navigationItems = getNavigationItems(t, isAdmin);
  
  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-[250px] sm:w-[300px]">
        <SheetHeader>
          <SheetTitle>{t('app_name')}</SheetTitle>
        </SheetHeader>
        <div className="py-4">
          <nav className="flex flex-col space-y-1">
            {navigationItems.map(item => (
              <NavigationLink
                key={item.path}
                path={item.path}
                label={item.label}
                icon={item.icon}
                onClick={() => handleNavigation(item.path)}
                isMobile={true}
              />
            ))}
            
            <div className="border-t my-2"></div>
            
            <NavigationLink
              path="/profile"
              label={t('nav_profile')}
              icon={navigationItems.find(item => item.path === '/profile')?.icon!}
              onClick={() => handleNavigation('/profile')}
              isMobile={true}
            />
            
            <NavigationLink
              path="/settings"
              label={t('nav_settings')}
              icon={navigationItems.find(item => item.path === '/settings')?.icon!}
              onClick={() => handleNavigation('/settings')}
              isMobile={true}
            />
            
            <button 
              className="flex items-center w-full p-3 rounded-md hover:bg-accent cursor-pointer mb-1"
              onClick={() => logout()}
            >
              <LogOut className="h-4 w-4 mr-3" />
              {t('nav_logout')}
            </button>
          </nav>
        </div>
      </SheetContent>
    </Sheet>
  );
};
