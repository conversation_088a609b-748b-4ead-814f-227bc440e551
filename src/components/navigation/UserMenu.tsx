
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useLanguage } from '@/context/LanguageContext';
import { User, LogOut, Settings } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';

export const UserMenu: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const { t } = useLanguage();
  
  if (!user) return null;
  
  const isActive = (path: string) => {
    if (path === '/' && location.pathname === '/') {
      return true;
    }
    return path !== '/' && location.pathname.startsWith(path);
  };
  
  return (
    <NavigationMenu>
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuTrigger className={isActive('/profile') || isActive('/settings') ? "bg-primary/10 font-semibold" : ""}>
            <User className="h-4 w-4 mr-2" />
            {user?.name || t('nav_profile')}
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className="grid w-[200px] gap-3 p-4">
              <li>
                <NavigationMenuLink
                  className={cn(
                    "flex w-full items-center justify-start rounded-md p-2 text-sm hover:bg-accent cursor-pointer",
                    isActive('/profile') && "bg-primary/10 font-semibold"
                  )}
                  onClick={() => navigate('/profile')}
                >
                  <User className="h-4 w-4 mr-2" />
                  {t('nav_profile')}
                </NavigationMenuLink>
              </li>
              <li>
                <NavigationMenuLink
                  className={cn(
                    "flex w-full items-center justify-start rounded-md p-2 text-sm hover:bg-accent cursor-pointer",
                    isActive('/settings') && "bg-primary/10 font-semibold"
                  )}
                  onClick={() => navigate('/settings')}
                >
                  <Settings className="h-4 w-4 mr-2" />
                  {t('nav_settings')}
                </NavigationMenuLink>
              </li>
              <li>
                <NavigationMenuLink
                  className={cn("flex w-full items-center justify-start rounded-md p-2 text-sm hover:bg-accent cursor-pointer")}
                  onClick={() => logout()}
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  {t('nav_logout')}
                </NavigationMenuLink>
              </li>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  );
};
