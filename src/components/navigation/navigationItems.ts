
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  User, 
  Anchor, 
  Users,
  Calendar,
  Wrench,
} from 'lucide-react';
import { NavigationTranslationKey } from '@/i18n/types/navigation';
import { LucideIcon } from 'lucide-react';

interface NavigationItem {
  path: string;
  label: string;
  icon: LucideIcon;
  requiresAdmin?: boolean;
}

export const getNavigationItems = (
  t: (key: NavigationTranslationKey, ...args: any[]) => string,
  isAdmin: boolean
): NavigationItem[] => {
  const items: NavigationItem[] = [
    {
      path: '/jaunts',
      label: t('nav_jaunts'),
      icon: LifeBuoy,
    },
    {
      path: '/',
      label: t('nav_boats'),
      icon: Anchor,
    },
    {
      path: '/maintenance',
      label: t('nav_maintenance'),
      icon: Wrench,
    },
    {
      path: '/teams',
      label: t('nav_teams'),
      icon: Users,
    },
    {
      path: '/lineups',
      label: t('nav_lineups'),
      icon: Calendar,
    },
    {
      path: '/admin',
      label: t('nav_admin'),
      icon: <PERSON><PERSON><PERSON>,
      requiresAdmin: true,
    },
    {
      path: '/profile',
      label: t('nav_profile'),
      icon: User,
    },
    {
      path: '/settings',
      label: t('nav_settings'),
      icon: Settings,
    },
  ];

  return isAdmin 
    ? items 
    : items.filter(item => !item.requiresAdmin);
};
