
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/context/LanguageContext';
import { useAuth } from '@/context/AuthContext';
import {
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuItem,
  NavigationMenuLink,
} from '@/components/ui/navigation-menu';
import { getNavigationItems } from './navigationItems';
import { NavigationLink } from './NavigationLink';
import { UserMenu } from './UserMenu';

export const DesktopNavigation: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { user } = useAuth();
  
  if (!user) return null;
  
  const isAdmin = user?.role === 'admin';
  const navigationItems = getNavigationItems(t, isAdmin);
  const mainNavItems = navigationItems.filter(item => 
    item.path !== '/profile' && item.path !== '/settings'
  );
  
  return (
    <>
      <NavigationMenu className="max-w-full justify-start flex-1">
        <NavigationMenuList className="flex flex-wrap gap-1">
          {mainNavItems.map(item => (
            <NavigationMenuItem key={item.path}>
              <NavigationMenuLink 
                className="focus:outline-none"
                onClick={() => navigate(item.path)}
              >
                <NavigationLink
                  path={item.path}
                  label={item.label}
                  icon={item.icon}
                  onClick={() => navigate(item.path)}
                />
              </NavigationMenuLink>
            </NavigationMenuItem>
          ))}
        </NavigationMenuList>
      </NavigationMenu>
      
      <UserMenu />
    </>
  );
};
