
import { useLanguage } from "@/context/LanguageContext";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PlusCircle, Search } from "lucide-react";
import { WatercraftDialog } from "./watercraft/WatercraftDialog";
import { WatercraftList } from "./watercraft/WatercraftList";
import { useWatercraftManagement } from "./watercraft/useWatercraftManagement";
import { useWatercraftDialog } from "./watercraft/useWatercraftDialog";

export const AdminWatercraftPanel = () => {
  const { t } = useLanguage();
  const {
    watercraft,
    searchTerm,
    setSearchTerm,
    handleSubmit,
    changeStatus,
    deleteWatercraft
  } = useWatercraftManagement();
  
  const {
    open,
    handleOpenChange,
    editingWatercraft,
    dialogKey,
    openCreateDialog,
    openEditDialog,
    closeDialog
  } = useWatercraftDialog();

  // Wrap the submit handler to close the dialog after submission
  const handleDialogSubmit = (data: any) => {
    handleSubmit(data);
    closeDialog();
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold">{t("manage_watercraft")}</h2>
          <div className="flex items-center gap-2">
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t("search_watercraft")}
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button onClick={openCreateDialog} className="gap-1">
              <PlusCircle className="h-4 w-4" />
              {t("add_watercraft")}
            </Button>
          </div>
        </div>

        <WatercraftList 
          watercraft={watercraft} 
          onEdit={openEditDialog}
          onDelete={deleteWatercraft}
          onChangeStatus={changeStatus}
        />
      </CardContent>

      <WatercraftDialog
        key={`dialog-${dialogKey}`}
        open={open}
        onOpenChange={handleOpenChange}
        onSubmit={handleDialogSubmit}
        editingWatercraft={editingWatercraft}
      />
    </Card>
  );
};
