
import { useState } from "react";
import { useLanguage } from "@/context/LanguageContext";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useTeams } from "@/hooks/useTeams";
import { AdminCreateTeamDialog } from "./AdminCreateTeamDialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { TeamTabContent } from "@/components/teams/TeamTabContent";
import { TeamDetailsManager } from "@/components/teams/TeamDetailsManager";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Team } from "@/types";
import { toast } from "sonner";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  UserPlus,
  Users
} from "lucide-react";

export const AdminTeamsPanel = () => {
  const { t } = useLanguage();
  const { teams, loading, getMyTeams, getOtherTeams } = useTeams();
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);

  const handleTeamClick = (team: Team) => {
    setSelectedTeam(team);
    setDetailsDialogOpen(true);
  };

  const handleTeamUpdate = (updatedTeam: Team) => {
    // In a real app, this would call an API endpoint
    // Here we're just showing a toast notification
    toast.success(t('team_updated'));
    setSelectedTeam(updatedTeam);
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold">{t("manage_teams")}</h2>
          <Button onClick={() => setCreateDialogOpen(true)} className="gap-1">
            <UserPlus className="h-4 w-4" />
            {t("create_team")}
          </Button>
        </div>

        <Tabs defaultValue="all-teams">
          <TabsList className="mb-4">
            <TabsTrigger value="all-teams">{t("all_teams")}</TabsTrigger>
            <TabsTrigger value="table-view">{t("table_view")}</TabsTrigger>
          </TabsList>

          <TabsContent value="all-teams">
            {loading ? (
              <div className="py-8 text-center text-muted-foreground">{t("loading")}</div>
            ) : (
              <div className="space-y-8">
                <TeamTabContent 
                  teams={teams} 
                  loading={loading} 
                  onCreateTeamClick={() => setCreateDialogOpen(true)} 
                  readOnly={false}
                  onTeamClick={handleTeamClick}
                />
              </div>
            )}
          </TabsContent>

          <TabsContent value="table-view">
            {loading ? (
              <div className="py-8 text-center text-muted-foreground">{t("loading")}</div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("team_name")}</TableHead>
                      <TableHead>{t("email")}</TableHead>
                      <TableHead>{t("members")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {teams.length > 0 ? (
                      teams.map((team) => (
                        <TableRow key={team.id} className="cursor-pointer" onClick={() => handleTeamClick(team)}>
                          <TableCell>
                            <div className="font-medium">{team.name}</div>
                            <div className="text-sm text-muted-foreground">{team.id}</div>
                          </TableCell>
                          <TableCell>{team.email}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Users className="h-4 w-4 mr-1" />
                              <span>{team.memberIds.length}</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={3} className="h-24 text-center">
                          {t("no_teams_yet")}
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            )}
          </TabsContent>
        </Tabs>

        <AdminCreateTeamDialog 
          open={createDialogOpen} 
          onOpenChange={setCreateDialogOpen} 
        />

        {selectedTeam && (
          <Dialog open={detailsDialogOpen} onOpenChange={setDetailsDialogOpen}>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>{selectedTeam.name}</DialogTitle>
              </DialogHeader>
              <div className="mt-4">
                <TeamDetailsManager 
                  team={selectedTeam}
                  onTeamUpdate={handleTeamUpdate}
                  readOnly={false}
                />
              </div>
            </DialogContent>
          </Dialog>
        )}
      </CardContent>
    </Card>
  );
};
