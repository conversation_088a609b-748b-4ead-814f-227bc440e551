
import { Watercraft, WatercraftType, WatercraftStatus, BoatType, Boat } from "@/types";

// Defines the form values structure used in the watercraft form
export interface WatercraftFormValues {
  id?: string;
  name: string;
  type: WatercraftType;
  boatType: BoatType;
  ownershipType: "club" | "member";
  memberId: string;
  location: string;
  skillLevel: string;
  status: WatercraftStatus;
  weightMin: string;
  weightMax: string;
  imageUrl?: string;
}

/**
 * Returns the default values for the watercraft form
 */
export const getDefaultWatercraftFormValues = (): WatercraftFormValues => ({
  name: "",
  type: "boat",
  boatType: "1x",
  ownershipType: "club",
  memberId: "",
  location: "",
  skillLevel: "1",
  status: "available",
  weightMin: "70",
  weightMax: "85",
  imageUrl: ""
});

/**
 * Extracts weight range information from a boat
 */
const extractBoatWeightRange = (boat: Boat): { min: string, max: string } => {
  if (boat.weightRange) {
    return {
      min: boat.weightRange.min.toString(),
      max: boat.weightRange.max.toString()
    };
  }
  return { min: "70", max: "85" };
};

/**
 * Maps boat-specific properties to form values
 */
const mapBoatSpecificProperties = (boat: Boat): Pick<WatercraftFormValues, 'boatType' | 'weightMin' | 'weightMax'> => {
  const { min, max } = extractBoatWeightRange(boat);
  return {
    boatType: boat.boatType || "1x", // Provide default if missing
    weightMin: min,
    weightMax: max
  };
};

/**
 * Maps common watercraft properties to form values
 */
const mapCommonWatercraftProperties = (watercraft: Watercraft): Omit<WatercraftFormValues, 'boatType' | 'weightMin' | 'weightMax'> => {
  return {
    id: watercraft.id,
    name: watercraft.name || "",
    type: watercraft.type,
    ownershipType: watercraft.ownershipType,
    memberId: watercraft.memberId || "",
    location: watercraft.location || "",
    skillLevel: watercraft.skillLevel?.toString() || "1",
    status: watercraft.status || "available",
    imageUrl: watercraft.imageUrl || ""
  };
};

/**
 * Converts a watercraft object to form values
 */
export const watercraftToFormValues = (watercraft: Watercraft | null): WatercraftFormValues => {
  if (!watercraft) {
    return getDefaultWatercraftFormValues();
  }

  // Get common properties for all watercraft types
  const commonProperties = mapCommonWatercraftProperties(watercraft);
  
  // Add boat-specific properties if the watercraft is a boat
  if (watercraft.type === 'boat') {
    const boatProperties = mapBoatSpecificProperties(watercraft as Boat);
    return {
      ...commonProperties,
      ...boatProperties
    };
  } else if (watercraft.type === 'coastal' && (watercraft as any).boatType) {
    // Handle coastal boats that might have boatType
    const boatProperties = mapBoatSpecificProperties(watercraft as any);
    return {
      ...commonProperties,
      ...boatProperties
    };
  }
  
  // For non-boat watercraft, use default boat-specific values
  return {
    ...commonProperties,
    boatType: '1x',
    weightMin: "70",
    weightMax: "85"
  };
};
