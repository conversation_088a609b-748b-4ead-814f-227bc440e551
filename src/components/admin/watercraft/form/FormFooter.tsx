
import { useLanguage } from "@/context/LanguageContext";
import { Button } from "@/components/ui/button";
import { DialogFooter } from "@/components/ui/dialog";

interface FormFooterProps {
  isEditing: boolean;
}

export const FormFooter = ({ isEditing }: FormFooterProps) => {
  const { t } = useLanguage();

  return (
    <DialogFooter>
      <Button type="submit">
        {isEditing ? t("update") : t("create")}
      </Button>
    </DialogFooter>
  );
};
