
import { useLanguage } from "@/context/LanguageContext";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { WatercraftStatus } from "@/types";
import { UseFormReturn } from "react-hook-form";
import { WatercraftFormValues } from "../WatercraftFormModel";

interface StatusAndSkillFieldsProps {
  form: UseFormReturn<WatercraftFormValues>;
  statusOptions: WatercraftStatus[];
}

export const StatusAndSkillFields = ({ form, statusOptions }: StatusAndSkillFieldsProps) => {
  const { t } = useLanguage();

  return (
    <div className="grid grid-cols-2 gap-4">
      <FormField
        control={form.control}
        name="skillLevel"
        render={({ field }) => (
          <FormItem>
            <FormLabel>{t("skill_level")}</FormLabel>
            <Select value={field.value} onValueChange={field.onChange}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder={t("select_skill_level")} />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="1">1 - {t("beginner")}</SelectItem>
                <SelectItem value="2">2 - {t("intermediate")}</SelectItem>
                <SelectItem value="3">3 - {t("advanced")}</SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="status"
        render={({ field }) => (
          <FormItem>
            <FormLabel>{t("status")}</FormLabel>
            <Select value={field.value} onValueChange={field.onChange}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder={t("select_status")} />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {statusOptions.map(status => (
                  <SelectItem key={status} value={status}>
                    {t(status as any)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
