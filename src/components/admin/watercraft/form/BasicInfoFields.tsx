
import { useLanguage } from "@/context/LanguageContext";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { BoatType, WatercraftType } from "@/types";
import { UseFormReturn } from "react-hook-form";
import { WatercraftFormValues } from "../WatercraftFormModel";
import { useEffect } from "react";

interface BasicInfoFieldsProps {
  form: UseFormReturn<WatercraftFormValues>;
  watercraftTypes: WatercraftType[];
}

export const BasicInfoFields = ({ form, watercraftTypes }: BasicInfoFieldsProps) => {
  const { t } = useLanguage();

  // Reset conditional fields when type changes
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "type") {
        // Reset boat-specific fields when type changes away from boat
        if (value.type !== "boat") {
          form.setValue("boatType", "1x", { shouldValidate: false });
          form.setValue("weightMin", "", { shouldValidate: false });
          form.setValue("weightMax", "", { shouldValidate: false });
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  return (
    <>
      <FormField
        control={form.control}
        name="name"
        render={({ field }) => (
          <FormItem>
            <FormLabel>{t("name")}</FormLabel>
            <FormControl>
              <Input {...field} required />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("watercraft_type")}</FormLabel>
              <Select 
                value={field.value} 
                onValueChange={field.onChange}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t("select_type")} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {watercraftTypes.map(type => (
                    <SelectItem key={type} value={type}>
                      {t(type as any)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </>
  );
};
