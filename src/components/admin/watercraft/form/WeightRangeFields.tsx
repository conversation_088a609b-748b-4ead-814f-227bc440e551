
import { useLanguage } from "@/context/LanguageContext";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { UseFormReturn } from "react-hook-form";
import { WatercraftFormValues } from "../WatercraftFormModel";

interface WeightRangeFieldsProps {
  form: UseFormReturn<WatercraftFormValues>;
}

export const WeightRangeFields = ({ form }: WeightRangeFieldsProps) => {
  const { t } = useLanguage();

  if (form.watch("type") !== "boat") {
    return null;
  }

  return (
    <div className="grid grid-cols-2 gap-4">
      <FormField
        control={form.control}
        name="weightMin"
        render={({ field }) => (
          <FormItem>
            <FormLabel>{t("min_weight")} (kg)</FormLabel>
            <FormControl>
              <Input {...field} type="number" min="40" max="120" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="weightMax"
        render={({ field }) => (
          <FormItem>
            <FormLabel>{t("max_weight")} (kg)</FormLabel>
            <FormControl>
              <Input {...field} type="number" min="40" max="120" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};
