
import { useLanguage } from "@/context/LanguageContext";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { BoatType } from "@/types";
import { UseFormReturn } from "react-hook-form";
import { WatercraftFormValues } from "../WatercraftFormModel";

interface BoatTypeFieldProps {
  form: UseFormReturn<WatercraftFormValues>;
  boatTypes: BoatType[];
}

export const BoatTypeField = ({ form, boatTypes }: BoatTypeFieldProps) => {
  const { t } = useLanguage();

  if (form.watch("type") !== "boat") {
    return null;
  }

  return (
    <FormField
      control={form.control}
      name="boatType"
      render={({ field }) => (
        <FormItem>
          <FormLabel>{t("boat_type")}</FormLabel>
          <Select value={field.value} onValueChange={field.onChange}>
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder={t("select_boat_type")} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {boatTypes.map(type => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
