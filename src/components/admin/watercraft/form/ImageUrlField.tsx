
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useLanguage } from "@/context/LanguageContext";
import { UseFormReturn } from "react-hook-form";
import { WatercraftFormValues } from "../WatercraftFormModel";

interface ImageUrlFieldProps {
  form: UseFormReturn<WatercraftFormValues>;
}

export const ImageUrlField = ({ form }: ImageUrlFieldProps) => {
  const { t } = useLanguage();

  return (
    <FormField
      control={form.control}
      name="imageUrl"
      render={({ field }) => (
        <FormItem>
          <FormLabel>{t("watercraft_image_url")}</FormLabel>
          <FormControl>
            <Input 
              placeholder="https://example.com/image.jpg" 
              {...field} 
              value={field.value || ""}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
