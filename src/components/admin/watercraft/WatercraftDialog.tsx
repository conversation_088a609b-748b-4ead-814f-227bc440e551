
import { useLanguage } from "@/context/LanguageContext";
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { useForm } from "react-hook-form";
import { Watercraft } from "@/types";
import { WatercraftForm } from "./WatercraftForm";
import { WatercraftFormValues, watercraftToFormValues } from "./WatercraftFormModel";
import { useEffect } from "react";

interface WatercraftDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: WatercraftFormValues) => void;
  editingWatercraft: Watercraft | null;
}

export const WatercraftDialog = ({ 
  open, 
  onOpenChange, 
  onSubmit, 
  editingWatercraft 
}: WatercraftDialogProps) => {
  const { t } = useLanguage();
  const isEditing = !!editingWatercraft;

  // Initialize form with empty values
  const form = useForm<WatercraftFormValues>({
    defaultValues: watercraftToFormValues(null)
  });

  // Reset form when dialog opens or editing watercraft changes
  useEffect(() => {
    if (open) {
      const defaultValues = editingWatercraft 
        ? watercraftToFormValues(JSON.parse(JSON.stringify(editingWatercraft)))
        : watercraftToFormValues(null);
        
      // Use a timeout to ensure clean state update
      setTimeout(() => {
        form.reset(defaultValues);
      }, 0);
    }
  }, [open, editingWatercraft, form]);

  const handleSubmit = (data: WatercraftFormValues) => {
    // Call parent handler with form data
    onSubmit(data);
    // Form will be reset when the dialog closes
  };

  // Clean dialog closing
  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      // Reset form when closing to prevent state leaks
      setTimeout(() => {
        form.reset(watercraftToFormValues(null));
      }, 0);
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? t("edit_watercraft") : t("add_watercraft")}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? t("edit_watercraft_description") 
              : t("add_watercraft_description")}
          </DialogDescription>
        </DialogHeader>
        
        <WatercraftForm 
          form={form} 
          onSubmit={handleSubmit} 
          isEditing={isEditing} 
        />
      </DialogContent>
    </Dialog>
  );
};
