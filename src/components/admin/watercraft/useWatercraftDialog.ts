
import { useState, useEffect } from "react";
import { Watercraft } from "@/types";

/**
 * Hook to manage watercraft dialog state
 * Handles opening, closing, and editing state of the watercraft dialog
 */
export const useWatercraftDialog = () => {
  const [open, setOpen] = useState(false);
  const [editingWatercraft, setEditingWatercraft] = useState<Watercraft | null>(null);
  const [dialogKey, setDialogKey] = useState(0);

  // Force dialog re-render when state changes
  useEffect(() => {
    if (open) {
      setDialogKey(prev => prev + 1);
    }
  }, [open, editingWatercraft]);

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    
    // Clear editing state when dialog closes
    if (!newOpen) {
      // Use timeout to avoid state conflicts
      setTimeout(() => {
        setEditingWatercraft(null);
      }, 100);
    }
  };

  const openCreateDialog = () => {
    // First close any existing dialog
    setOpen(false);
    
    // Wait for the close animation to complete
    setTimeout(() => {
      setEditingWatercraft(null);
      setOpen(true);
    }, 50);
  };

  const openEditDialog = (wc: Watercraft) => {
    // First close any existing dialog to ensure clean state
    setOpen(false);
    
    // Wait for the close animation to complete
    setTimeout(() => {
      // Create a clean deep copy to avoid reference issues
      setEditingWatercraft(JSON.parse(JSON.stringify(wc)));
      // Then open the dialog 
      setOpen(true);
    }, 50);
  };

  const closeDialog = () => {
    setOpen(false);
    setTimeout(() => {
      setEditingWatercraft(null);
    }, 100);
  };

  return {
    open,
    setOpen,
    editingWatercraft,
    dialogKey,
    handleOpenChange,
    openCreateDialog,
    openEditDialog,
    closeDialog
  };
};
