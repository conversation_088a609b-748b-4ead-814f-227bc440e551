
import { useState } from "react";
import { Watercraft, WatercraftStatus, Boat } from "@/types";
import { getCleanMockWatercrafts } from "@/services/watercraft/mock-data";
import { toast } from "sonner";
import { useLanguage } from "@/context/LanguageContext";
import { generateWatercraftId } from "@/utils/idGenerator";
import { WatercraftFormValues } from "./WatercraftFormModel";

export const useWatercraftManagement = () => {
  const { t } = useLanguage();
  const [watercraft, setWatercraft] = useState<Watercraft[]>(getCleanMockWatercrafts());
  const [searchTerm, setSearchTerm] = useState("");

  const filteredWatercraft = watercraft.filter(wc => 
    (wc.name?.toLowerCase().includes(searchTerm.toLowerCase()) || 
     wc.id.toLowerCase().includes(searchTerm.toLowerCase())) &&
    !wc.deleted
  );

  const handleSubmit = (data: WatercraftFormValues) => {
    try {
      // Check if we're updating an existing watercraft (by looking for its ID in the data)
      const existingIndex = watercraft.findIndex(wc => wc.id === data.id);
      
      if (existingIndex >= 0) {
        // Update existing watercraft
        const updatedWatercraft = [...watercraft];
        
        // Create the updated watercraft object
        const updated: Watercraft = {
          ...updatedWatercraft[existingIndex],
          name: data.name,
          type: data.type,
          ownershipType: data.ownershipType,
          memberId: data.ownershipType === 'member' ? data.memberId : undefined,
          location: data.location || '',
          skillLevel: parseInt(data.skillLevel) || 0,
          status: data.status,
          imageUrl: data.imageUrl || undefined,
          updatedAt: new Date(),
        };

        // Add boat-specific properties if type is boat
        if (data.type === 'boat') {
          (updated as Boat).boatType = data.boatType;
          (updated as Boat).weightRange = {
            min: parseInt(data.weightMin) || 70,
            max: parseInt(data.weightMax) || 85
          };
        } else if (data.type === 'coastal' && data.boatType) {
          // For coastal boats that might have boatType
          (updated as any).boatType = data.boatType;
        }

        updatedWatercraft[existingIndex] = updated;
        setWatercraft(updatedWatercraft);
        toast.success(t("watercraft_updated"));
      } else {
        // Create new watercraft
        const newId = generateWatercraftId();
        const baseWatercraft: Watercraft = {
          id: newId,
          name: data.name,
          type: data.type,
          ownershipType: data.ownershipType,
          memberId: data.ownershipType === 'member' ? data.memberId : undefined,
          location: data.location || '',
          skillLevel: parseInt(data.skillLevel) || 0,
          status: data.status,
          imageUrl: data.imageUrl || undefined,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        // Add boat-specific properties if type is boat
        if (data.type === 'boat') {
          const boatWatercraft = baseWatercraft as Boat;
          boatWatercraft.boatType = data.boatType;
          boatWatercraft.weightRange = {
            min: parseInt(data.weightMin) || 70,
            max: parseInt(data.weightMax) || 85
          };
        } else if (data.type === 'coastal' && data.boatType) {
          // For coastal boats that might have boatType
          (baseWatercraft as any).boatType = data.boatType;
        }

        setWatercraft(prev => [...prev, baseWatercraft]);
        toast.success(t("watercraft_created"));
      }
    } catch (error) {
      console.error("Error in handleSubmit:", error);
      toast.error(t("error_message")); 
    }
  };

  const changeStatus = (watercraftId: string, newStatus: WatercraftStatus) => {
    setWatercraft(prevWatercraft => 
      prevWatercraft.map(wc => 
        wc.id === watercraftId ? { ...wc, status: newStatus, updatedAt: new Date() } : wc
      )
    );
    toast.success(t("watercraft_status_updated"));
  };

  const deleteWatercraft = (watercraftId: string) => {
    setWatercraft(prevWatercraft => 
      prevWatercraft.map(wc => 
        wc.id === watercraftId ? { ...wc, deleted: true, updatedAt: new Date() } : wc
      )
    );
    toast.success(t("watercraft_deleted"));
  };

  return {
    watercraft: filteredWatercraft,
    searchTerm,
    setSearchTerm,
    handleSubmit,
    changeStatus,
    deleteWatercraft
  };
};
