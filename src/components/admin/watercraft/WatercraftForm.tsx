
import { useLanguage } from "@/context/LanguageContext";
import { WatercraftFormValues } from "./WatercraftFormModel";
import { Form } from "@/components/ui/form";
import { BoatType, WatercraftStatus, WatercraftType } from "@/types";
import { UseFormReturn } from "react-hook-form";
import {
  BasicInfoFields,
  BoatTypeField,
  OwnershipFields,
  StatusAndSkillFields,
  WeightRangeFields,
  FormFooter,
  ImageUrlField
} from "./form";

interface WatercraftFormProps {
  form: UseFormReturn<WatercraftFormValues>;
  onSubmit: (data: WatercraftFormValues) => void;
  isEditing: boolean;
}

export const WatercraftForm = ({ form, onSubmit, isEditing }: WatercraftFormProps) => {
  const { t } = useLanguage();
  
  const boatTypes: BoatType[] = ['1x', '2x', '2-', '4x', '4+', '3x', '8+'];
  const watercraftTypes: WatercraftType[] = ['boat', 'kayak', 'PB', 'launch', 'surfski', 'coastal'];
  const statusOptions: WatercraftStatus[] = ['available', 'in-use', 'maintenance', 'regatta'];

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <BasicInfoFields form={form} watercraftTypes={watercraftTypes} />
        
        <BoatTypeField form={form} boatTypes={boatTypes} />
        
        <OwnershipFields form={form} />
        
        <ImageUrlField form={form} />
        
        <StatusAndSkillFields form={form} statusOptions={statusOptions} />
        
        <WeightRangeFields form={form} />
        
        <FormFooter isEditing={isEditing} />
      </form>
    </Form>
  );
};
