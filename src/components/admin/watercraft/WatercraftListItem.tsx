
import { useLanguage } from "@/context/LanguageContext";
import { Watercraft, Boat, WatercraftStatus } from "@/types";
import { TableCell, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { StatusBadge } from "@/components/watercraft/details/StatusBadge";
import { mockUsers } from "@/services/mockData/users";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
  MoreHorizontal,
  Edit,
  Trash,
  CheckCircle,
  AlertCircle,
  Hammer
} from "lucide-react";

interface WatercraftListItemProps {
  watercraft: Watercraft;
  onEdit: (wc: Watercraft) => void;
  onDelete: (id: string) => void;
  onChangeStatus: (id: string, status: WatercraftStatus) => void;
}

export const WatercraftListItem = ({
  watercraft: wc,
  onEdit,
  onDelete,
  onChangeStatus
}: WatercraftListItemProps) => {
  const { t } = useLanguage();
  
  const formatOwnership = (wc: Watercraft) => {
    switch (wc.ownershipType) {
      case "club":
        return t("club_owned");
      case "member":
        if (wc.memberId) {
          const member = mockUsers.find(u => u.id === wc.memberId);
          return `${t("member_owned")} (${member?.name || 'Unknown'})`;
        }
        return t("member_owned");
      default:
        return wc.ownershipType;
    }
  };

  // Get boat type for display
  const getBoatType = () => {
    if (wc.type === 'boat' || wc.type === 'coastal') {
      const boat = wc as Boat;
      return boat.boatType;
    }
    return '';
  };

  const boatType = getBoatType();

  return (
    <TableRow>
      <TableCell>
        <div>
          <p className="font-medium">{wc.name || `${wc.type} ${wc.id.slice(-4)}`}</p>
          <p className="text-sm text-muted-foreground">{wc.id}</p>
        </div>
      </TableCell>
      <TableCell>
        <Badge variant="outline">
          {wc.type === 'boat' 
            ? (boatType ? `${t('boat')} (${boatType})` : t('boat'))
            : wc.type === 'coastal' 
              ? (boatType ? `${t('coastal')} (${boatType})` : t('coastal'))
              : t(wc.type as any)}
        </Badge>
      </TableCell>
      <TableCell>
        {formatOwnership(wc)}
      </TableCell>
      <TableCell>
        <StatusBadge status={wc.status} />
      </TableCell>
      <TableCell>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">{t("open_menu")}</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{t("actions")}</DropdownMenuLabel>
            <DropdownMenuItem 
              className="cursor-pointer"
              onClick={() => onEdit(wc)}
            >
              <Edit className="mr-2 h-4 w-4" />
              {t("edit")}
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuLabel>{t("change_status")}</DropdownMenuLabel>
            {wc.status !== "available" && (
              <DropdownMenuItem 
                className="cursor-pointer"
                onClick={() => onChangeStatus(wc.id, "available")}
              >
                <CheckCircle className="mr-2 h-4 w-4" />
                {t("mark_available")}
              </DropdownMenuItem>
            )}
            {wc.status !== "in-use" && (
              <DropdownMenuItem 
                className="cursor-pointer"
                onClick={() => onChangeStatus(wc.id, "in-use")}
              >
                <AlertCircle className="mr-2 h-4 w-4" />
                {t("mark_in_use")}
              </DropdownMenuItem>
            )}
            {wc.status !== "maintenance" && (
              <DropdownMenuItem 
                className="cursor-pointer"
                onClick={() => onChangeStatus(wc.id, "maintenance")}
              >
                <Hammer className="mr-2 h-4 w-4" />
                {t("mark_maintenance")}
              </DropdownMenuItem>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              className="text-destructive cursor-pointer"
              onClick={() => onDelete(wc.id)}
            >
              <Trash className="mr-2 h-4 w-4" />
              {t("delete_watercraft")}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  );
};
