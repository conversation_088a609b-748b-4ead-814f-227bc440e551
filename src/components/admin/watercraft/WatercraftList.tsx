
import { useLanguage } from "@/context/LanguageContext";
import { Watercraft, WatercraftStatus } from "@/types";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { WatercraftListItem } from "./WatercraftListItem";

interface WatercraftListProps {
  watercraft: Watercraft[];
  onEdit: (wc: Watercraft) => void;
  onDelete: (id: string) => void;
  onChangeStatus: (id: string, status: WatercraftStatus) => void;
}

export const WatercraftList = ({
  watercraft,
  onEdit,
  onDelete,
  onChangeStatus
}: WatercraftListProps) => {
  const { t } = useLanguage();

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t("watercraft")}</TableHead>
            <TableHead>{t("type")}</TableHead>
            <TableHead>{t("ownership")}</TableHead>
            <TableHead>{t("status")}</TableHead>
            <TableHead>{t("actions")}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {watercraft.length > 0 ? (
            watercraft.map((wc) => (
              <WatercraftListItem
                key={wc.id}
                watercraft={wc}
                onEdit={onEdit}
                onDelete={onDelete}
                onChangeStatus={onChangeStatus}
              />
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={5} className="text-center py-6 text-muted-foreground">
                {t("no_watercraft_found")}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};
