
import { useEffect, useState } from "react";
import { useLanguage } from "@/context/LanguageContext";
import { Card, CardContent } from "@/components/ui/card";
import { 
  Users, 
  Ship, 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp
} from "lucide-react";
import { mockUsers } from "@/services/mockData/users";
import { mockWatercrafts } from "@/services/watercraft/mock-data";
import { mockMaintenanceRequests } from "@/services/mockData/maintenance";

export const AdminStatsPanel = () => {
  const { t } = useLanguage();
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalWatercraft: 0,
    maintenanceRequests: 0,
    activeJaunts: 0
  });

  useEffect(() => {
    // In a real app, these would be API calls
    const totalUsers = mockUsers.length;
    const totalWatercraft = mockWatercrafts.length;
    const maintenanceRequests = mockMaintenanceRequests.filter(
      req => req.status === 'open' || req.status === 'in-progress'
    ).length;
    const activeJaunts = 2; // Mock value for now

    setStats({
      totalUsers,
      totalWatercraft,
      maintenanceRequests,
      activeJaunts
    });
  }, []);

  const statCards = [
    {
      title: t("total_users"),
      value: stats.totalUsers,
      icon: Users,
      color: "text-blue-500",
      bgColor: "bg-blue-50"
    },
    {
      title: t("total_watercraft"),
      value: stats.totalWatercraft,
      icon: Ship,
      color: "text-green-500",
      bgColor: "bg-green-50"
    },
    {
      title: t("maintenance_requests"),
      value: stats.maintenanceRequests,
      icon: AlertTriangle,
      color: "text-amber-500",
      bgColor: "bg-amber-50"
    },
    {
      title: t("active_jaunts"),
      value: stats.activeJaunts,
      icon: TrendingUp,
      color: "text-purple-500", 
      bgColor: "bg-purple-50"
    }
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
      {statCards.map((stat, index) => (
        <Card key={index}>
          <CardContent className="p-6 flex items-center gap-4">
            <div className={`p-3 rounded-full ${stat.bgColor}`}>
              <stat.icon className={`h-6 w-6 ${stat.color}`} />
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
              <p className="text-2xl font-bold">{stat.value}</p>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
