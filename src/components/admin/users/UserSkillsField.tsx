
import { useLanguage } from "@/context/LanguageContext";
import { SkillType } from "@/types";
import { FormField, FormItem, FormLabel, FormDescription, FormControl } from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { UseFormReturn } from "react-hook-form";
import { UserFormValues } from "./userFormSchema";

interface UserSkillsFieldProps {
  form: UseFormReturn<UserFormValues>;
}

export const UserSkillsField = ({ form }: UserSkillsFieldProps) => {
  const { t } = useLanguage();
  const availableSkills: SkillType[] = ["Rowing", "Coaching", "Maintenance", "Coxswain"];

  return (
    <FormField
      control={form.control}
      name="skills"
      render={() => (
        <FormItem>
          <div className="mb-2">
            <FormLabel>{t("skills")}</FormLabel>
            <FormDescription>
              {t("select_skills_description")}
            </FormDescription>
          </div>
          <div className="grid grid-cols-2 gap-2">
            {availableSkills.map((skill) => (
              <FormField
                key={skill}
                control={form.control}
                name="skills"
                render={({ field }) => {
                  return (
                    <FormItem
                      key={skill}
                      className="flex flex-row items-start space-x-3 space-y-0"
                    >
                      <FormControl>
                        <Checkbox
                          checked={field.value?.includes(skill)}
                          onCheckedChange={(checked) => {
                            const current = field.value || [];
                            const updated = checked
                              ? [...current, skill]
                              : current.filter((item) => item !== skill);
                            field.onChange(updated);
                          }}
                        />
                      </FormControl>
                      <FormLabel className="font-normal">
                        {skill}
                      </FormLabel>
                    </FormItem>
                  );
                }}
              />
            ))}
          </div>
        </FormItem>
      )}
    />
  );
};
