
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useLanguage } from "@/context/LanguageContext";
import { User, SkillType } from "@/types";
import { generateUserId } from "@/utils/idGenerator";
import { toast } from "sonner";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";

import { UserBasicInfoFields } from "./UserBasicInfoFields";
import { UserSkillsField } from "./UserSkillsField";
import { userFormSchema, UserFormValues } from "./userFormSchema";
import { getDefaultPermissions } from "./userFormUtils";

interface UserFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (user: Partial<User>) => void;
  initialData?: Partial<User>;
  mode: "create" | "edit";
}

export function UserFormDialog({
  open,
  onOpenChange,
  onSubmit,
  initialData,
  mode,
}: UserFormDialogProps) {
  const { t } = useLanguage();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const form = useForm<UserFormValues>({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
      name: initialData?.name || "",
      email: initialData?.email || "",
      phone: initialData?.phone || "",
      role: initialData?.role || "member",
      skills: initialData?.skills || [],
    },
  });

  const availableSkills: SkillType[] = ["Rowing", "Coaching", "Maintenance", "Coxswain"];

  const handleSubmit = async (values: UserFormValues) => {
    setIsSubmitting(true);
    try {
      // Map string[] skills to SkillType[]
      const typedSkills = values.skills?.filter((skill): skill is SkillType => 
        availableSkills.includes(skill as SkillType)
      ) || [];

      // In a real app, this would be an API call
      // For now, we just wait a bit to simulate an API call
      await new Promise(resolve => setTimeout(resolve, 500));

      // Create a new user object or update existing one
      const userData: Partial<User> = {
        ...values,
        skills: typedSkills,
        // Add default permissions based on role
        permissions: initialData?.permissions || getDefaultPermissions(values.role),
        language: initialData?.language || "en",
        favorites: initialData?.favorites || [],
        ...(mode === "create" && { id: generateUserId() }),
      };

      onSubmit(userData);
      onOpenChange(false);
      toast.success(mode === "create" ? t("user_created") : t("user_updated"));
    } catch (error) {
      console.error("Error submitting user form:", error);
      toast.error(t("error"));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {mode === "create" ? t("create_user") : t("edit_user")}
          </DialogTitle>
          <DialogDescription>
            {mode === "create"
              ? t("create_user_description")
              : t("edit_user_description")}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <UserBasicInfoFields form={form} />
            <UserSkillsField form={form} />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                {t("cancel")}
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? t("saving") : t("save")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
