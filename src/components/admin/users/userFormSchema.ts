
import { z } from "zod";

// Form schema using zod
export const userFormSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Invalid email address" }),
  phone: z.string().min(5, { message: "Phone number is required" }),
  role: z.enum(["admin", "coach", "member"] as const),
  skills: z.array(z.string()).optional().default([]),
});

export type UserFormValues = z.infer<typeof userFormSchema>;
