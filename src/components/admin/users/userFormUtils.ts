
import { UserRole, WatercraftPermission } from "@/types";

/**
 * Returns default permissions based on user role
 */
export const getDefaultPermissions = (role: UserRole): WatercraftPermission[] => {
  switch (role) {
    case "admin":
      return [
        { watercraftType: "boat", skillLevel: 3 },
        { watercraftType: "kayak", skillLevel: 3 },
        { watercraftType: "PB", skillLevel: 3 },
        { watercraftType: "launch", skillLevel: 3 },
        { watercraftType: "surfski", skillLevel: 3 },
      ];
    case "coach":
      return [
        { watercraftType: "boat", skillLevel: 2 },
        { watercraftType: "kayak", skillLevel: 2 },
        { watercraftType: "PB", skillLevel: 2 },
        { watercraftType: "launch", skillLevel: 2 },
      ];
    case "member":
    default:
      return [
        { watercraftType: "boat", skillLevel: 1 },
        { watercraftType: "kayak", skillLevel: 0 },
      ];
  }
};
