
import { User, UserRole } from "@/types";
import { useLanguage } from "@/context/LanguageContext";
import { 
  TableRow,
  TableCell
} from "@/components/ui/table";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { getAvatarFallback } from "@/utils/avatar";
import { UserActionsMenu } from "./UserActionsMenu";

interface UserRowProps {
  user: User;
  onChangeRole: (userId: string, newRole: UserRole) => void;
  onEdit: (user: User) => void;
  onDelete: (user: User) => void;
}

export const UserRow = ({
  user,
  onChangeRole,
  onEdit,
  onDelete
}: UserRowProps) => {
  const { t } = useLanguage();

  const getRoleBadgeClasses = (role: UserRole) => {
    switch (role) {
      case "admin":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "coach":
        return "bg-green-100 text-green-800 border-green-200";
      case "member":
        return "bg-blue-100 text-blue-800 border-blue-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <TableRow>
      <TableCell>
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarFallback>{getAvatarFallback(user.name)}</AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium">{user.name}</p>
            <p className="text-sm text-muted-foreground">{user.email}</p>
          </div>
        </div>
      </TableCell>
      <TableCell>
        <Badge className={getRoleBadgeClasses(user.role)}>
          {t(user.role as any)}
        </Badge>
      </TableCell>
      <TableCell>
        <div className="flex flex-wrap gap-1">
          {user.skills.slice(0, 2).map((skill, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {skill}
            </Badge>
          ))}
          {user.skills.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{user.skills.length - 2}
            </Badge>
          )}
        </div>
      </TableCell>
      <TableCell>
        <UserActionsMenu 
          user={user}
          onChangeRole={onChangeRole}
          onEdit={onEdit}
          onDelete={onDelete}
        />
      </TableCell>
    </TableRow>
  );
};
