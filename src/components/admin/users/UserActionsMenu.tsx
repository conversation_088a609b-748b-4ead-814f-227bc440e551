
import { User, UserRole } from "@/types";
import { useLanguage } from "@/context/LanguageContext";
import { But<PERSON> } from "@/components/ui/button";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { 
  MoreHorizontal, 
  Edit, 
  UserX, 
  ShieldCheck, 
  ShieldAlert 
} from "lucide-react";

interface UserActionsMenuProps {
  user: User;
  onChangeRole: (userId: string, newRole: UserRole) => void;
  onEdit: (user: User) => void;
  onDelete: (user: User) => void;
}

export const UserActionsMenu = ({
  user,
  onChangeRole,
  onEdit,
  onDelete
}: UserActionsMenuProps) => {
  const { t } = useLanguage();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon">
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">{t("open_menu")}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>{t("actions")}</DropdownMenuLabel>
        <DropdownMenuItem 
          className="cursor-pointer"
          onClick={() => onEdit(user)}
        >
          <Edit className="mr-2 h-4 w-4" />
          {t("edit")}
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuLabel>{t("change_role")}</DropdownMenuLabel>
        {user.role !== "admin" && (
          <DropdownMenuItem 
            className="cursor-pointer"
            onClick={() => onChangeRole(user.id, "admin")}
          >
            <ShieldAlert className="mr-2 h-4 w-4" />
            {t("make_admin")}
          </DropdownMenuItem>
        )}
        {user.role !== "coach" && (
          <DropdownMenuItem 
            className="cursor-pointer"
            onClick={() => onChangeRole(user.id, "coach")}
          >
            <ShieldCheck className="mr-2 h-4 w-4" />
            {t("make_coach")}
          </DropdownMenuItem>
        )}
        {user.role !== "member" && (
          <DropdownMenuItem 
            className="cursor-pointer"
            onClick={() => onChangeRole(user.id, "member")}
          >
            <ShieldCheck className="mr-2 h-4 w-4" />
            {t("make_member")}
          </DropdownMenuItem>
        )}
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          className="text-destructive cursor-pointer"
          onClick={() => onDelete(user)}
        >
          <UserX className="mr-2 h-4 w-4" />
          {t("delete_user")}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
