
import { useState } from "react";
import { User, UserRole } from "@/types";
import { useLanguage } from "@/context/LanguageContext";
import { toast } from "sonner";
import { mockUsers } from "@/services/mockData/users";

export const useUserManagement = () => {
  const { t } = useLanguage();
  const [users, setUsers] = useState<User[]>(mockUsers);
  const [searchTerm, setSearchTerm] = useState("");
  
  // For Create/Edit user dialog
  const [userFormOpen, setUserFormOpen] = useState(false);
  const [formMode, setFormMode] = useState<"create" | "edit">("create");
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  
  // For delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);

  const filteredUsers = users.filter(user => 
    !user.deleted && (
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  const changeUserRole = (userId: string, newRole: UserRole) => {
    setUsers(prevUsers => 
      prevUsers.map(user => 
        user.id === userId ? { ...user, role: newRole } : user
      )
    );
    toast.success(t("user_role_updated"));
  };

  const handleCreateUser = () => {
    setSelectedUser(null);
    setFormMode("create");
    setUserFormOpen(true);
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setFormMode("edit");
    setUserFormOpen(true);
  };

  const handleDeleteUserPrompt = (user: User) => {
    setUserToDelete(user);
    setDeleteDialogOpen(true);
  };

  const handleDeleteUser = () => {
    if (!userToDelete) return;
    
    setUsers(prevUsers => 
      prevUsers.map(user => 
        user.id === userToDelete.id ? { ...user, deleted: true } : user
      )
    );
    setDeleteDialogOpen(false);
    toast.success(t("user_deleted"));
  };

  const handleUserFormSubmit = (userData: Partial<User>) => {
    if (formMode === "create") {
      // Create new user
      const newUser = {
        ...userData,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as User;
      
      setUsers(prevUsers => [...prevUsers, newUser]);
    } else if (formMode === "edit" && selectedUser) {
      // Update existing user
      setUsers(prevUsers => 
        prevUsers.map(user => 
          user.id === selectedUser.id ? { ...user, ...userData, updatedAt: new Date() } : user
        )
      );
    }
  };

  return {
    users,
    filteredUsers,
    searchTerm,
    setSearchTerm,
    userFormOpen,
    setUserFormOpen,
    formMode,
    selectedUser,
    deleteDialogOpen, 
    setDeleteDialogOpen,
    userToDelete,
    changeUserRole,
    handleCreateUser,
    handleEditUser,
    handleDeleteUserPrompt,
    handleDeleteUser,
    handleUserFormSubmit
  };
};
