
import { useState } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { UserPlus } from 'lucide-react';
import { toast } from 'sonner';
import { useTeams } from '@/hooks/useTeams';

interface AdminCreateTeamDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const AdminCreateTeamDialog = ({ open, onOpenChange }: AdminCreateTeamDialogProps) => {
  const { t } = useLanguage();
  const { createTeam } = useTeams();
  const [teamName, setTeamName] = useState('');
  const [teamEmail, setTeamEmail] = useState('');
  const [practiceSheetUrl, setPracticeSheetUrl] = useState('');

  const handleSubmit = () => {
    if (!teamName || !teamEmail) {
      toast.error(t("team_name_email_required"));
      return;
    }

    createTeam({
      name: teamName,
      email: teamEmail,
      practiceSheetUrl: practiceSheetUrl
    });
    
    // Reset form
    setTeamName('');
    setTeamEmail('');
    setPracticeSheetUrl('');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('create_new_team')}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="teamName">{t('team_name')}</Label>
            <Input
              id="teamName"
              value={teamName}
              onChange={(e) => setTeamName(e.target.value)}
              placeholder={t('enter_team_name')}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="teamEmail">{t('team_email')}</Label>
            <Input
              id="teamEmail"
              type="email"
              value={teamEmail}
              onChange={(e) => setTeamEmail(e.target.value)}
              placeholder={t('enter_team_email')}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="practiceSheet">
              {t('practice_signup_sheet_url')}
              <span className="text-muted-foreground text-xs ml-1">({t('optional')})</span>
            </Label>
            <Input
              id="practiceSheet"
              value={practiceSheetUrl}
              onChange={(e) => setPracticeSheetUrl(e.target.value)}
              placeholder="https://example.com/sheets/practice"
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t('cancel')}
          </Button>
          <Button onClick={handleSubmit}>
            {t('create_team')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
