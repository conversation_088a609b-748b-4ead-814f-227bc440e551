
import { useLanguage } from "@/context/LanguageContext";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { UserRow } from "./users/UserRow";
import { UserFormDialog } from "./users/UserFormDialog";
import { DeleteUserDialog } from "./users/DeleteUserDialog";
import { useUserManagement } from "./users/useUserManagement";
import { Search, UserPlus } from "lucide-react";

export const AdminUsersPanel = () => {
  const { t } = useLanguage();
  const {
    filteredUsers,
    searchTerm,
    setSearchTerm,
    userFormOpen,
    setUserFormOpen,
    formMode,
    selectedUser,
    deleteDialogOpen,
    setDeleteDialogOpen,
    changeUserRole,
    handleCreateUser,
    handleEditUser,
    handleDeleteUserPrompt,
    handleDeleteUser,
    handleUserFormSubmit
  } = useUserManagement();

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold">{t("manage_users")}</h2>
          <div className="flex items-center gap-4">
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t("search_users")}
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button onClick={handleCreateUser}>
              <UserPlus className="mr-2 h-4 w-4" />
              {t("create_user")}
            </Button>
          </div>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t("user")}</TableHead>
                <TableHead>{t("role")}</TableHead>
                <TableHead>{t("skills")}</TableHead>
                <TableHead>{t("actions")}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.length > 0 ? (
                filteredUsers.map((user) => (
                  <UserRow
                    key={user.id}
                    user={user}
                    onChangeRole={changeUserRole}
                    onEdit={handleEditUser}
                    onDelete={handleDeleteUserPrompt}
                  />
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-6 text-muted-foreground">
                    {t("no_users_found")}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      {/* Create/Edit User Dialog */}
      <UserFormDialog
        open={userFormOpen}
        onOpenChange={setUserFormOpen}
        onSubmit={handleUserFormSubmit}
        initialData={selectedUser || undefined}
        mode={formMode}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteUserDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteUser}
      />
    </Card>
  );
};
