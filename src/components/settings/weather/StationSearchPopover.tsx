
import React, { useState, useRef, useEffect } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { searchWeatherStations } from '@/services/weather.service';
import { WeatherStation } from '@/types/weather';
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { MapPin, Search, X, Loader2 } from 'lucide-react';

interface StationSearchPopoverProps {
  currentStation: WeatherStation | null;
  onSelectStation: (station: WeatherStation) => void;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

export const StationSearchPopover: React.FC<StationSearchPopoverProps> = ({
  currentStation,
  onSelectStation,
  isOpen,
  setIsOpen
}) => {
  const { t } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<WeatherStation[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Effect to focus the search input when the popover opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Clear search results when popover closes
  useEffect(() => {
    if (!isOpen) {
      setSearchQuery('');
      setSearchResults([]);
    }
  }, [isOpen]);

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    
    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    if (query.length < 2) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }
    
    // Set a new timeout to search after 300ms of user input pause
    setIsSearching(true);
    searchTimeoutRef.current = setTimeout(async () => {
      try {
        console.log('Searching for weather stations:', query);
        const results = await searchWeatherStations(query);
        setSearchResults(results || []);
      } catch (error) {
        console.error('Error searching stations:', error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    }, 300);
  };

  const handleSelectStation = (station: WeatherStation) => {
    onSelectStation(station);
    setIsOpen(false);
  };
  
  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    inputRef.current?.focus();
  };
  
  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          className="w-full justify-between"
          id="weatherStation"
        >
          <div className="flex items-center">
            <MapPin className="h-4 w-4 mr-2" />
            {currentStation ? currentStation.name : t('weather_no_station_selected')}
          </div>
          <Search className="h-4 w-4 ml-2 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[300px]">
        <div className="flex flex-col">
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <input
              ref={inputRef}
              className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground"
              placeholder={t('weather_search_placeholder')}
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="icon"
                className="h-5 w-5 p-0"
                onClick={clearSearch}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          
          <div className="max-h-[300px] overflow-y-auto overflow-x-hidden p-1">
            {isSearching ? (
              <div className="flex flex-col items-center justify-center p-4 space-y-2">
                <Loader2 className="h-5 w-5 animate-spin text-muted-foreground mb-2" />
                <p className="text-xs text-center text-muted-foreground">
                  {t('loading')}...
                </p>
              </div>
            ) : (
              <>
                {searchQuery.length >= 2 && searchResults.length === 0 ? (
                  <p className="py-6 text-center text-sm text-muted-foreground">
                    {t('weather_no_results')}
                  </p>
                ) : (
                  <div>
                    {searchResults.map((station) => (
                      <div
                        key={station.id}
                        onClick={() => handleSelectStation(station)}
                        className="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground"
                      >
                        <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
                        <div className="flex flex-col">
                          <span className="font-medium">{station.name}</span>
                          {station.state && (
                            <span className="text-xs text-muted-foreground">
                              {station.state}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};
