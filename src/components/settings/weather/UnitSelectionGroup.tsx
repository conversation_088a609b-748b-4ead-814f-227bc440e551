
import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { WeatherUnit } from '@/types/weather';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface UnitSelectionGroupProps {
  currentUnit: WeatherUnit;
  onUnitChange: (unit: WeatherUnit) => void;
}

export const UnitSelectionGroup: React.FC<UnitSelectionGroupProps> = ({
  currentUnit,
  onUnitChange
}) => {
  const { t } = useLanguage();
  
  return (
    <div className="space-y-2">
      <Label>{t('weather_unit_setting')}</Label>
      <RadioGroup 
        defaultValue={currentUnit} 
        onValueChange={onUnitChange}
        className="flex flex-col space-y-1"
      >
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="metric" id="metric" />
          <Label htmlFor="metric" className="cursor-pointer">{t('weather_unit_metric')}</Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="imperial" id="imperial" />
          <Label htmlFor="imperial" className="cursor-pointer">{t('weather_unit_imperial')}</Label>
        </div>
      </RadioGroup>
    </div>
  );
};
