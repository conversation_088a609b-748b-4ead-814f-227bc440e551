
import { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useWeather } from '@/context/WeatherContext';
import { WeatherStation, WeatherUnit } from '@/types/weather';
import { toast } from 'sonner';
import { useLanguage } from '@/context/LanguageContext';

export const useWeatherSettings = () => {
  const { user, updateUser } = useAuth();
  const { t } = useLanguage();
  const { currentStation, setCurrentStation, weatherUnit, setWeatherUnit } = useWeather();
  const [isStationOpen, setIsStationOpen] = useState(false);
  
  const handleSelectStation = (station: WeatherStation) => {
    setCurrentStation(station);
    setIsStationOpen(false);

    // Save to user preferences
    if (user) {
      const updatedUser = {
        ...user,
        preferredWeatherStation: station,
        updatedAt: new Date() // Update the timestamp
      };

      updateUser(updatedUser);
      localStorage.setItem("user", JSON.stringify(updatedUser));

      console.log(`Weather station preference updated to: ${station.name} (${station.id})`);
    }

    toast.success(t('weather_station_saved'));
  };
  
  const handleUnitChange = (value: WeatherUnit) => {
    setWeatherUnit(value);

    // Save to user preferences
    if (user) {
      const updatedUser = {
        ...user,
        preferredWeatherUnit: value,
        updatedAt: new Date() // Update the timestamp
      };

      updateUser(updatedUser);
      localStorage.setItem("user", JSON.stringify(updatedUser));

      console.log(`Weather unit preference updated to: ${value}`);
    }

    toast.success(t('weather_unit_saved'));
  };
  
  return {
    currentStation,
    weatherUnit,
    isStationOpen,
    setIsStationOpen,
    handleSelectStation,
    handleUnitChange
  };
};
