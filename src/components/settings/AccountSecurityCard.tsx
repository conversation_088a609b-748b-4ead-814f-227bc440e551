
import { useLanguage } from "@/context/LanguageContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

export function AccountSecurityCard() {
  const { t } = useLanguage();
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("settings_account_security")}</CardTitle>
        <CardDescription>
          {t("settings_account_security_description")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="current-password">{t("settings_current_password")}</Label>
            <Input id="current-password" type="password" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="new-password">{t("settings_new_password")}</Label>
            <Input id="new-password" type="password" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="confirm-password">{t("settings_confirm_password")}</Label>
            <Input id="confirm-password" type="password" />
          </div>
          <Button className="mt-2">
            {t("settings_update_password")}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
