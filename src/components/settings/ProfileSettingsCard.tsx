
import { useState } from "react";
import { toast } from "sonner";
import { User } from "@/types";
import { useLanguage } from "@/context/LanguageContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { User as UserIcon, Mail, Phone, Languages, Check } from "lucide-react";

interface ProfileSettingsCardProps {
  user: User;
}

export function ProfileSettingsCard({ user }: ProfileSettingsCardProps) {
  const { t } = useLanguage();
  const [formData, setFormData] = useState<Partial<User>>(user);
  const [saving, setSaving] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => prev ? { ...prev, [name]: value } : {});
  };

  const handleSaveProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData) return;

    setSaving(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success(t("settings_profile_saved"));
    } catch (error) {
      console.error("Error saving profile:", error);
      toast.error(t("settings_save_error"));
    } finally {
      setSaving(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("settings_personal_information")}</CardTitle>
        <CardDescription>
          {t("settings_personal_info_description")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSaveProfile} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="name">
                <UserIcon className="h-4 w-4 inline mr-2" />
                {t("settings_name")}
              </Label>
              <Input
                id="name"
                name="name"
                value={formData?.name || ""}
                onChange={handleChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">
                <Mail className="h-4 w-4 inline mr-2" />
                {t("settings_email")}
              </Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData?.email || ""}
                onChange={handleChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">
                <Phone className="h-4 w-4 inline mr-2" />
                {t("settings_phone")}
              </Label>
              <Input
                id="phone"
                name="phone"
                value={formData?.phone || ""}
                onChange={handleChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="language">
                <Languages className="h-4 w-4 inline mr-2" />
                {t("settings_preferred_language")}
              </Label>
              <Input
                id="language"
                name="language"
                value={formData?.language || ""}
                disabled
              />
              <p className="text-xs text-muted-foreground">
                {t("settings_change_language_note")}
              </p>
            </div>
          </div>
          <Button type="submit" disabled={saving} className="mt-4">
            {saving ? t("settings_saving") : t("settings_save_changes")}
            {saving ? null : <Check className="ml-2 h-4 w-4" />}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
