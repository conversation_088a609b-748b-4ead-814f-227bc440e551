
import { User } from "@/types";
import { useLanguage } from "@/context/LanguageContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

interface SkillsPermissionsCardProps {
  user: User;
}

export function SkillsPermissionsCard({ user }: SkillsPermissionsCardProps) {
  const { t } = useLanguage();

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("settings_skills_permissions")}</CardTitle>
        <CardDescription>
          {t("settings_skills_permissions_description")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h3 className="font-medium mb-2">{t("settings_your_skills")}</h3>
            <div className="flex flex-wrap gap-2">
              {user.skills.map((skill) => (
                <div key={skill} className="bg-secondary text-secondary-foreground px-3 py-1 rounded-full text-sm">
                  {skill}
                </div>
              ))}
            </div>
          </div>
          <Separator />
          <div>
            <h3 className="font-medium mb-2">{t("settings_your_permissions")}</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
              {user.permissions.map((permission, index) => (
                <div key={index} className="bg-muted p-3 rounded-md">
                  <p className="font-medium">{permission.watercraftType}</p>
                  <p className="text-sm text-muted-foreground">
                    {t("settings_skill_level")}: {permission.skillLevel}/5
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
