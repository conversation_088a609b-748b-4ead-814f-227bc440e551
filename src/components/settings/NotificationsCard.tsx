
import { useLanguage } from "@/context/LanguageContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export function NotificationsCard() {
  const { t } = useLanguage();
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("settings_notification_preferences")}</CardTitle>
        <CardDescription>
          {t("settings_notification_preferences_description")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground mb-4">
          {t("settings_notification_mock_note")}
        </p>
      </CardContent>
    </Card>
  );
}
