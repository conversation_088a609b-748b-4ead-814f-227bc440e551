
import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { useAuth } from '@/context/AuthContext';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { StationSearchPopover } from './weather/StationSearchPopover';
import { UnitSelectionGroup } from './weather/UnitSelectionGroup';
import { useWeatherSettings } from './weather/useWeatherSettings';
import { Thermometer, Wind } from 'lucide-react';

export const WeatherSettingsCard: React.FC = () => {
  const { t } = useLanguage();
  const { user } = useAuth();
  const {
    currentStation,
    weatherUnit,
    isStationOpen,
    setIsStationOpen,
    handleSelectStation,
    handleUnitChange
  } = useWeatherSettings();

  const getUnitExamples = (unit: 'metric' | 'imperial') => {
    if (unit === 'imperial') {
      return {
        temp: '72°F',
        wind: '15 mph',
        distance: '5 miles'
      };
    }
    return {
      temp: '22°C',
      wind: '24 km/h',
      distance: '8 km'
    };
  };

  const examples = getUnitExamples(weatherUnit);

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('weather_title')}</CardTitle>
        <CardDescription>
          {t('weather_station_setting_description')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="weatherStation">{t('weather_station_setting')}</Label>
          <StationSearchPopover
            currentStation={currentStation}
            onSelectStation={handleSelectStation}
            isOpen={isStationOpen}
            setIsOpen={setIsStationOpen}
          />
          {currentStation && (
            <div className="text-xs text-muted-foreground">
              Current station: {currentStation.name}
              {currentStation.distance && ` (${currentStation.distance}km away)`}
            </div>
          )}
        </div>

        <div className="space-y-3">
          <UnitSelectionGroup
            currentUnit={weatherUnit}
            onUnitChange={handleUnitChange}
          />

          {/* Unit preference examples */}
          <div className="bg-muted/50 rounded-lg p-3 space-y-2">
            <div className="text-sm font-medium">Current unit preference:</div>
            <div className="flex flex-wrap gap-2">
              <Badge variant="secondary" className="flex items-center gap-1">
                <Thermometer className="h-3 w-3" />
                Temperature: {examples.temp}
              </Badge>
              <Badge variant="secondary" className="flex items-center gap-1">
                <Wind className="h-3 w-3" />
                Wind: {examples.wind}
              </Badge>
              <Badge variant="secondary">
                Distance: {examples.distance}
              </Badge>
            </div>
            <div className="text-xs text-muted-foreground">
              {user?.preferredWeatherUnit
                ? `Saved in your profile as ${weatherUnit}`
                : `Auto-detected as ${weatherUnit} based on your location`
              }
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
