
import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { useWeather } from '@/context/WeatherContext';
import { Button } from '@/components/ui/button';
import { MapPin, Navigation, Loader2, Thermometer, Wind, Waves, Eye, Gauge } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList, CommandSeparator } from '@/components/ui/command';
import { useIsMobile } from '@/hooks/use-mobile';
import { WeatherStation } from '@/types/weather';
import { searchWeatherStations, findNearbyStations, detectStationCapabilities } from '@/services/weather.service';
import { toast } from 'sonner';

interface StationSelectorProps {
  currentStation: WeatherStation | null;
  onSelectStation: (station: WeatherStation) => void;
}

export const StationSelector: React.FC<StationSelectorProps> = ({
  currentStation,
  onSelectStation,
}) => {
  const { t } = useLanguage();
  const { weatherUnit } = useWeather();
  const isMobile = useIsMobile();
  const [query, setQuery] = React.useState('');
  const [searchResults, setSearchResults] = React.useState<WeatherStation[]>([]);
  const [nearbyStations, setNearbyStations] = React.useState<WeatherStation[]>([]);
  const [isOpen, setIsOpen] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [locationLoading, setLocationLoading] = React.useState(false);

  // Format distance according to user's unit preference
  const formatDistance = (distanceKm: number): string => {
    if (weatherUnit === 'imperial') {
      const miles = distanceKm * 0.621371;
      return `${miles.toFixed(1)} mi`;
    }
    return `${distanceKm.toFixed(1)} km`;
  };

  // Handle station search
  React.useEffect(() => {
    if (query.length > 1) {
      setLoading(true);

      const performSearch = async () => {
        try {
          const results = await searchWeatherStations(query);
          setSearchResults(results);
        } catch (error) {
          console.error('Error searching stations:', error);
          setSearchResults([]);
        } finally {
          setLoading(false);
        }
      };

      const timer = setTimeout(performSearch, 300);
      return () => clearTimeout(timer);
    } else {
      setSearchResults([]);
    }
  }, [query]);

  // Handle finding nearby stations using geolocation
  const handleFindNearby = async () => {
    if (!navigator.geolocation) {
      toast.error('Geolocation is not supported by this browser');
      return;
    }

    setLocationLoading(true);

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          const { latitude, longitude } = position.coords;
          const nearby = await findNearbyStations(latitude, longitude, 100); // 100km radius
          setNearbyStations(nearby);

          if (nearby.length === 0) {
            toast.info('No weather stations found within 100km of your location');
          } else {
            toast.success(`Found ${nearby.length} nearby weather stations`);
          }
        } catch (error) {
          console.error('Error finding nearby stations:', error);
          toast.error('Failed to find nearby stations');
        } finally {
          setLocationLoading(false);
        }
      },
      (error) => {
        console.error('Geolocation error:', error);
        toast.error('Unable to get your location. Please check location permissions.');
        setLocationLoading(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    );
  };

  // Handle station selection with capability detection
  const handleStationSelect = async (station: WeatherStation) => {
    console.log('StationSelector: handleStationSelect called with station:', station);
    try {
      // If station doesn't have capabilities, detect them
      if (!station.capabilities) {
        console.log('StationSelector: Detecting capabilities for station:', station.id);
        const capabilities = await detectStationCapabilities(station.id);
        station.capabilities = capabilities;
      }

      console.log('StationSelector: Calling onSelectStation with station:', station);
      onSelectStation(station);
      setIsOpen(false);
      console.log('StationSelector: Station selection completed');
    } catch (error) {
      console.error('Error detecting station capabilities:', error);
      // Still select the station even if capability detection fails
      console.log('StationSelector: Selecting station despite capability detection error');
      onSelectStation(station);
      setIsOpen(false);
    }
  };

  // Render capability icons
  const renderCapabilityIcons = (station: WeatherStation) => {
    if (!station.capabilities) return null;

    const icons = [];
    const { capabilities } = station;

    if (capabilities.airTemperature) icons.push(<Thermometer key="temp" className="h-3 w-3 text-orange-500" title="Air Temperature" />);
    if (capabilities.waterTemperature) icons.push(<Thermometer key="water-temp" className="h-3 w-3 text-blue-500" title="Water Temperature" />);
    if (capabilities.wind) icons.push(<Wind key="wind" className="h-3 w-3 text-sky-500" title="Wind Data" />);
    if (capabilities.waterLevel) icons.push(<Waves key="tide" className="h-3 w-3 text-teal-500" title="Water Level/Tide" />);
    if (capabilities.barometricPressure) icons.push(<Gauge key="pressure" className="h-3 w-3 text-green-500" title="Barometric Pressure" />);
    if (capabilities.visibility) icons.push(<Eye key="visibility" className="h-3 w-3 text-purple-500" title="Visibility" />);

    return icons.length > 0 ? (
      <div className="flex gap-1 mt-1">
        {icons}
      </div>
    ) : null;
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          size={isMobile ? "default" : "sm"}
          className={`${isMobile ? 'h-10 w-[150px] text-sm' : 'h-7'} flex items-center`}
        >
          <MapPin className={`${isMobile ? 'h-4 w-4' : 'h-3 w-3'} mr-1 flex-shrink-0`} />
          <span className="truncate">
            {currentStation?.name || t('weather_station')}
          </span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[280px] lg:w-[350px]" align={isMobile ? "start" : "center"}>
        <Command filter={(value, search) => 1}>
          <div className="flex items-center border-b px-3">
            <CommandInput
              placeholder={t('weather_search_placeholder')}
              value={query}
              onValueChange={setQuery}
              className="h-9 border-0 focus:ring-0"
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={handleFindNearby}
              disabled={locationLoading}
              className="ml-2 h-8 w-8 p-0"
              title="Find nearby stations"
            >
              {locationLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Navigation className="h-4 w-4" />
              )}
            </Button>
          </div>
          <CommandEmpty>
            {loading ? t('weather_loading') : t('weather_no_results')}
          </CommandEmpty>
          <CommandList>
            {nearbyStations.length > 0 && (
              <>
                <CommandGroup heading="Nearby Stations">
                  {nearbyStations.slice(0, 5).map((station) => (
                    <CommandItem
                      key={`nearby-${station.id}`}
                      value={`nearby-${station.id}-${station.name}`}
                      onSelect={() => {
                        console.log('Nearby CommandItem onSelect triggered for:', station);
                        handleStationSelect(station);
                      }}
                      onClick={() => {
                        console.log('Nearby CommandItem onClick triggered for:', station);
                        handleStationSelect(station);
                      }}
                    >
                      <Navigation className="h-4 w-4 mr-2 text-blue-600" />
                      <div className="flex-1">
                        <div>
                          <span>{station.name}</span>
                          {station.state && <span className="ml-1 text-muted-foreground">({station.state})</span>}
                        </div>
                        {renderCapabilityIcons(station)}
                      </div>
                      {station.distance && (
                        <span className="text-xs text-muted-foreground ml-2">
                          {formatDistance(station.distance)}
                        </span>
                      )}
                    </CommandItem>
                  ))}
                </CommandGroup>
                {searchResults.length > 0 && <CommandSeparator />}
              </>
            )}
            {searchResults.length > 0 && (
              <CommandGroup heading={query ? "Search Results" : "Popular Stations"}>
                {searchResults.map((station) => (
                  <CommandItem
                    key={`search-${station.id}`}
                    value={`search-${station.id}-${station.name}`}
                    onSelect={() => {
                      console.log('CommandItem onSelect triggered for:', station);
                      handleStationSelect(station);
                    }}
                    onClick={() => {
                      console.log('CommandItem onClick triggered for:', station);
                      handleStationSelect(station);
                    }}
                  >
                    <MapPin className="h-4 w-4 mr-2 text-green-600" />
                    <div className="flex-1">
                      <div>
                        <span>{station.name}</span>
                        {station.state && <span className="ml-1 text-muted-foreground">({station.state})</span>}
                      </div>
                      {renderCapabilityIcons(station)}
                    </div>
                    {station.distance && (
                      <span className="text-xs text-muted-foreground ml-2">
                        {formatDistance(station.distance)}
                      </span>
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
