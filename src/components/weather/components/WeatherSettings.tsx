
import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { useWeather } from '@/context/WeatherContext';
import { StationSelector } from './StationSelector';
import { 
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { WeatherStation, WeatherUnit } from '@/types/weather';

export const WeatherSettings: React.FC = () => {
  const { t } = useLanguage();
  const { 
    currentStation, 
    setCurrentStation, 
    weatherUnit, 
    setWeatherUnit 
  } = useWeather();

  const handleUnitChange = (value: string) => {
    setWeatherUnit(value as WeatherUnit);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('settings_weather')}</CardTitle>
        <CardDescription>{t('settings_weather_description')}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="text-sm font-medium mb-2">{t('settings_preferred_station')}</h3>
          <StationSelector 
            currentStation={currentStation} 
            onSelectStation={setCurrentStation}
          />
        </div>
        
        <div>
          <h3 className="text-sm font-medium mb-2">{t('settings_unit_system')}</h3>
          <RadioGroup 
            value={weatherUnit} 
            onValueChange={handleUnitChange}
            className="flex flex-col space-y-1"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="metric" id="metric" />
              <Label htmlFor="metric">{t('settings_metric')}</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="imperial" id="imperial" />
              <Label htmlFor="imperial">{t('settings_imperial')}</Label>
            </div>
          </RadioGroup>
        </div>
      </CardContent>
    </Card>
  );
};
