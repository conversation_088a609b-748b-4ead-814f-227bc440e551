
import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { RefreshCcw, Thermometer, Droplet, Wind, Waves } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { WeatherUnit } from '@/types/weather';
import { formatTemperature, formatWindSpeed, formatTideLevel } from '@/utils/weatherUtils';
import { Skeleton } from '@/components/ui/skeleton';
import { useIsMobile } from '@/hooks/use-mobile';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface CurrentWeatherProps {
  weatherData: any;
  isLoading: boolean;
  weatherUnit: WeatherUnit;
  onRefresh: () => void;
}

export const CurrentWeather: React.FC<CurrentWeatherProps> = ({
  weatherData,
  isLoading,
  weatherUnit,
  onRefresh
}) => {
  const { t } = useLanguage();
  const isMobile = useIsMobile();
  
  if (isLoading) {
    return (
      <div className={`flex ${isMobile ? 'flex-col items-start' : 'items-center'} gap-2 px-2`}>
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-4 w-16" />
        {isMobile && <Skeleton className="h-4 w-16" />}
      </div>
    );
  }
  
  if (!weatherData) {
    return (
      <div className="flex items-center gap-2 px-2 text-sm text-muted-foreground">
        {t('weather_not_available')}
      </div>
    );
  }
  
  const { temperature, windSpeed, waterTemperature, tideLevel } = weatherData;
  
  // Create a weather item with tooltip
  const WeatherItem = ({ 
    icon, 
    value, 
    label, 
    color 
  }: { 
    icon: React.ReactNode; 
    value: string; 
    label: string; 
    color?: string 
  }) => {
    // Display N/A when value is not available or says "N/A"
    const displayValue = value === 'N/A' ? t('weather_not_available') : value;
    
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className={`flex items-center ${isMobile ? 'mb-1 text-sm' : ''}`}>
              {icon}
              <span className="whitespace-nowrap">{displayValue}</span>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{label}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };
  
  return (
    <div className={`
      flex ${isMobile ? 'flex-col items-start py-1' : 'items-center'} 
      gap-1 md:gap-3 px-1 text-xs md:text-sm overflow-hidden
    `}>
      <Button 
        variant="ghost" 
        size="sm" 
        onClick={onRefresh} 
        className={`p-0 h-6 w-6 ${isMobile ? 'absolute right-12 top-5' : ''}`}
        title={t('refresh')}
      >
        <RefreshCcw className="h-3 w-3" />
        <span className="sr-only">{t('refresh')}</span>
      </Button>
      
      <WeatherItem
        icon={<Thermometer className="h-3 w-3 mr-1 text-orange-500" />}
        value={formatTemperature(temperature, weatherUnit)}
        label={t('weather_temperature')}
      />
      
      {/* Always show water temp, even on mobile */}
      <WeatherItem
        icon={<Droplet className="h-3 w-3 mr-1 text-blue-500" />}
        value={waterTemperature !== null ? formatTemperature(waterTemperature, weatherUnit) : t('weather_not_available')}
        label={t('weather_water_temp')}
      />
      
      <WeatherItem
        icon={<Wind className="h-3 w-3 mr-1 text-teal-500" />}
        value={formatWindSpeed(windSpeed, weatherUnit)}
        label={t('weather_wind_speed')}
      />

      {/* Show tide level if available */}
      {tideLevel !== undefined && tideLevel !== null && (
        <WeatherItem
          icon={<Waves className="h-3 w-3 mr-1 text-cyan-500" />}
          value={formatTideLevel(tideLevel, weatherUnit)}
          label={t('weather_tide_level')}
        />
      )}
    </div>
  );
};
