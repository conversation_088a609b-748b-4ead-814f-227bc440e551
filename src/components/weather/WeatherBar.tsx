
import React, { useState, useEffect } from 'react';
import { useWeather } from '@/context/WeatherContext';
import { useLanguage } from '@/context/LanguageContext';
import { StationSelector, CurrentWeather, HourlyForecast, UnitToggle, WeatherAlerts } from './components';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronUp, Clock } from 'lucide-react';
import { formatDateInTimeZone } from '@/utils/timeUtils';
import { useIsMobile } from '@/hooks/use-mobile';

export const WeatherBar: React.FC = () => {
  const {
    currentStation,
    setCurrentStation,
    weatherData,
    hourlyForecast,
    weatherAlerts,
    isLoading,
    isExpanded,
    setIsExpanded,
    weatherUnit,
    setWeatherUnit,
    refreshWeather
  } = useWeather();
  const { t } = useLanguage();
  const isMobile = useIsMobile();
  const [currentTime, setCurrentTime] = useState<string>(formatDateInTimeZone(new Date(), 'h:mm a'));

  // Update time every minute
  useEffect(() => {
    const updateTime = () => {
      setCurrentTime(formatDateInTimeZone(new Date(), 'h:mm a'));
    };
    
    const interval = setInterval(updateTime, 60 * 1000); // Update every minute
    return () => clearInterval(interval);
  }, []);

  const renderNoStationSelected = () => (
    <div className="text-sm text-muted-foreground px-2">
      {t('weather_no_station_selected')}
    </div>
  );

  return (
    <Collapsible 
      open={isExpanded} 
      onOpenChange={setIsExpanded}
      className="bg-background border-b"
    >
      {/* Header Bar - Increased height for mobile */}
      <div className={`container mx-auto flex items-center ${isMobile ? 'h-16 py-2' : 'h-8'} px-2`}>
        {/* Station selector - made more prominent on mobile */}
        <div className={isMobile ? 'mr-2' : ''}>
          <StationSelector 
            currentStation={currentStation} 
            onSelectStation={setCurrentStation} 
          />
        </div>
        
        {/* Current weather data section - rearranged for mobile */}
        {!isExpanded && (
          <div className="flex-1 flex items-center min-w-0">
            {!currentStation
              ? renderNoStationSelected()
              : <CurrentWeather
                  weatherData={weatherData}
                  isLoading={isLoading}
                  weatherUnit={weatherUnit}
                  onRefresh={refreshWeather}
                />
            }
          </div>
        )}
        
        {/* Show current time in collapsed view with responsive spacing */}
        {!isExpanded && !isMobile && (
          <div className="flex items-center mr-2 text-xs whitespace-nowrap">
            <Clock className="h-3 w-3 mr-1" />
            <span>{currentTime}</span>
          </div>
        )}
        
        {/* Unit toggle and expand button (only show expand when collapsed) */}
        <div className="flex items-center">
          <UnitToggle
            weatherUnit={weatherUnit}
            setWeatherUnit={setWeatherUnit}
          />

          {!isExpanded && (
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="p-0 h-6 w-6 ml-1">
                <ChevronDown className="h-4 w-4" />
              </Button>
            </CollapsibleTrigger>
          )}
        </div>
      </div>
      
      <CollapsibleContent>
        <div className="space-y-4">
          {/* Weather Alerts */}
          {currentStation && (
            <WeatherAlerts
              alerts={weatherAlerts}
              isLoading={isLoading}
            />
          )}

          {/* Hourly Forecast */}
          <HourlyForecast
            currentStation={currentStation}
            hourlyForecast={hourlyForecast}
            weatherData={weatherData}
            weatherUnit={weatherUnit}
            isLoading={isLoading}
          />

          {/* Collapse button - positioned consistently with expand button */}
          <div className="container mx-auto px-2 pb-2">
            <div className="flex justify-end">
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm" className="p-0 h-6 w-6">
                  <ChevronUp className="h-4 w-4" />
                </Button>
              </CollapsibleTrigger>
            </div>
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
};
