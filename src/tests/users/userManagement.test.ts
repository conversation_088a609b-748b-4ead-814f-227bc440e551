
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { generateUserId } from '@/utils/idGenerator';
import { User, UserRole } from '@/types';
import { createValidUser } from '../utils/testUtils';
import { useState } from 'react';
import { renderHook, act } from '@testing-library/react-hooks';
import { useUserManagement } from '@/components/admin/users/useUserManagement';

// Mock the react hooks
vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...actual,
    useState: vi.fn(),
  };
});

// Mock the useLanguage hook
vi.mock('@/context/LanguageContext', () => ({
  useLanguage: () => ({ t: (key: string) => key }),
}));

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe('User Management', () => {
  let mockUsers: User[];
  let mockSetUsers: any;
  let mockSetSearchTerm: any;
  let mockSetUserFormOpen: any;
  let mockSetFormMode: any;
  let mockSetSelectedUser: any;
  let mockSetDeleteDialogOpen: any;
  let mockSetUserToDelete: any;

  beforeEach(() => {
    mockUsers = [
      {
        id: 'user-123',
        name: 'Test User',
        email: '<EMAIL>',
        phone: '************',
        role: 'member',
        skills: ['Rowing'],
        permissions: [{ watercraftType: 'boat', skillLevel: 1 }],
        language: 'en',
        favorites: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];
    
    mockSetUsers = vi.fn();
    mockSetSearchTerm = vi.fn();
    mockSetUserFormOpen = vi.fn();
    mockSetFormMode = vi.fn();
    mockSetSelectedUser = vi.fn();
    mockSetDeleteDialogOpen = vi.fn();
    mockSetUserToDelete = vi.fn();
    
    // Mock useState calls in order they appear in the hook
    (useState as any).mockImplementation((initialValue) => {
      if (Array.isArray(initialValue)) {
        return [mockUsers, mockSetUsers];
      } else if (initialValue === '') {
        return ['', mockSetSearchTerm];
      } else if (initialValue === false && mockSetUserFormOpen) {
        const result = [false, mockSetUserFormOpen];
        mockSetUserFormOpen = null; // Use once
        return result;
      } else if (initialValue === 'create') {
        return ['create', mockSetFormMode];
      } else if (initialValue === null && mockSetSelectedUser) {
        const result = [null, mockSetSelectedUser];
        mockSetSelectedUser = null; // Use once
        return result;
      } else if (initialValue === false) {
        return [false, mockSetDeleteDialogOpen];
      } else {
        return [null, mockSetUserToDelete];
      }
    });
  });

  describe('useUserManagement', () => {
    it('should filter users correctly', () => {
      (useState as any).mockImplementationOnce(() => [mockUsers, mockSetUsers]);
      (useState as any).mockImplementationOnce(() => ['test', mockSetSearchTerm]);
      
      const { result } = renderHook(() => useUserManagement());
      
      expect(result.current.filteredUsers.length).toBe(1);
      expect(result.current.filteredUsers[0].name).toBe('Test User');
    });

    it('should handle user role change correctly', () => {
      const { result } = renderHook(() => useUserManagement());
      
      act(() => {
        result.current.changeUserRole('user-123', 'admin');
      });
      
      expect(mockSetUsers).toHaveBeenCalled();
      // Check if the mapping function is called with correct logic
      const mappingFn = mockSetUsers.mock.calls[0][0];
      const updatedUsers = mappingFn(mockUsers);
      expect(updatedUsers[0].role).toBe('admin');
    });

    it('should prepare for user creation correctly', () => {
      const { result } = renderHook(() => useUserManagement());
      
      act(() => {
        result.current.handleCreateUser();
      });
      
      expect(mockSetSelectedUser).toHaveBeenCalledWith(null);
      expect(mockSetFormMode).toHaveBeenCalledWith('create');
      expect(mockSetUserFormOpen).toHaveBeenCalledWith(true);
    });

    it('should prepare for user edit correctly', () => {
      const { result } = renderHook(() => useUserManagement());
      const userToEdit = mockUsers[0];
      
      act(() => {
        result.current.handleEditUser(userToEdit);
      });
      
      expect(mockSetSelectedUser).toHaveBeenCalledWith(userToEdit);
      expect(mockSetFormMode).toHaveBeenCalledWith('edit');
      expect(mockSetUserFormOpen).toHaveBeenCalledWith(true);
    });

    it('should prepare for user deletion correctly', () => {
      const { result } = renderHook(() => useUserManagement());
      const userToDelete = mockUsers[0];
      
      act(() => {
        result.current.handleDeleteUserPrompt(userToDelete);
      });
      
      expect(mockSetUserToDelete).toHaveBeenCalledWith(userToDelete);
      expect(mockSetDeleteDialogOpen).toHaveBeenCalledWith(true);
    });

    it('should handle user deletion correctly', () => {
      (useState as any).mockImplementationOnce(() => [mockUsers, mockSetUsers]);
      (useState as any).mockImplementationOnce(() => ['', mockSetSearchTerm]);
      // Skip other useState mocks...
      (useState as any).mockImplementationOnce(() => [mockUsers[0], mockSetUserToDelete]);
      
      const { result } = renderHook(() => useUserManagement());
      
      act(() => {
        result.current.handleDeleteUser();
      });
      
      expect(mockSetUsers).toHaveBeenCalled();
      // Check if the mapping function is called with correct logic
      const mappingFn = mockSetUsers.mock.calls[0][0];
      const updatedUsers = mappingFn(mockUsers);
      expect(updatedUsers[0].deleted).toBe(true);
    });

    it('should handle user form submission - create mode', () => {
      (useState as any).mockImplementationOnce(() => [mockUsers, mockSetUsers]);
      // Skip other useState mocks...
      (useState as any).mockImplementationOnce(() => ['create', mockSetFormMode]);
      
      const { result } = renderHook(() => useUserManagement());
      
      const userData = createValidUser();
      
      act(() => {
        result.current.handleUserFormSubmit(userData);
      });
      
      expect(mockSetUsers).toHaveBeenCalled();
      // Verify a new user is added
      const updatingFn = mockSetUsers.mock.calls[0][0];
      const updatedUsers = updatingFn(mockUsers);
      expect(updatedUsers.length).toBe(mockUsers.length + 1);
    });

    it('should handle user form submission - edit mode', () => {
      (useState as any).mockImplementationOnce(() => [mockUsers, mockSetUsers]);
      // Skip other useState mocks...
      (useState as any).mockImplementationOnce(() => ['edit', mockSetFormMode]);
      (useState as any).mockImplementationOnce(() => [mockUsers[0], mockSetSelectedUser]);
      
      const { result } = renderHook(() => useUserManagement());
      
      const updatedData = { name: 'Updated Name', email: '<EMAIL>' };
      
      act(() => {
        result.current.handleUserFormSubmit(updatedData);
      });
      
      expect(mockSetUsers).toHaveBeenCalled();
      // Verify user is updated
      const updatingFn = mockSetUsers.mock.calls[0][0];
      const updatedUsers = updatingFn(mockUsers);
      expect(updatedUsers[0].name).toBe('Updated Name');
      expect(updatedUsers[0].email).toBe('<EMAIL>');
    });
  });

  describe('User Form Schema Validation', () => {
    it('should validate user form values correctly', async () => {
      const { userFormSchema } = await import('@/components/admin/users/userFormSchema');
      
      // Valid data
      const validData = {
        name: 'Test User',
        email: '<EMAIL>',
        phone: '************',
        role: 'member' as UserRole,
        skills: ['Rowing'],
      };
      
      // Invalid data cases
      const invalidName = { ...validData, name: 'T' };
      const invalidEmail = { ...validData, email: 'not-an-email' };
      const invalidPhone = { ...validData, phone: '' };
      
      // Test valid data
      const validResult = userFormSchema.safeParse(validData);
      expect(validResult.success).toBe(true);
      
      // Test invalid name
      const nameResult = userFormSchema.safeParse(invalidName);
      expect(nameResult.success).toBe(false);
      
      // Test invalid email
      const emailResult = userFormSchema.safeParse(invalidEmail);
      expect(emailResult.success).toBe(false);
      
      // Test invalid phone
      const phoneResult = userFormSchema.safeParse(invalidPhone);
      expect(phoneResult.success).toBe(false);
    });
  });
});
