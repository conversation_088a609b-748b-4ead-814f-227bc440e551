
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ConfigPage from '@/pages/Config';

// Mock local storage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key];
    }),
    clear: vi.fn(() => {
      store = {};
    }),
  };
})();

Object.defineProperty(window, 'localStorage', { value: localStorageMock });

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe('ConfigPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.clear();
  });

  it('shows the password screen initially', () => {
    render(<ConfigPage />);
    
    expect(screen.getByText('Configuration Access')).toBeInTheDocument();
    expect(screen.getByLabelText('Password')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Access Configuration' })).toBeInTheDocument();
  });

  it('shows an error with incorrect password', async () => {
    render(<ConfigPage />);
    
    const passwordInput = screen.getByLabelText('Password');
    const submitButton = screen.getByRole('button', { name: 'Access Configuration' });
    
    fireEvent.change(passwordInput, { target: { value: 'wrong-password' } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('Incorrect password')).toBeInTheDocument();
    });
  });

  it('allows access with correct password', async () => {
    render(<ConfigPage />);
    
    const passwordInput = screen.getByLabelText('Password');
    const submitButton = screen.getByRole('button', { name: 'Access Configuration' });
    
    fireEvent.change(passwordInput, { target: { value: 'rowing-admin-2023' } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('System Configuration')).toBeInTheDocument();
      expect(screen.getByText('Feature Flags')).toBeInTheDocument();
    });
  });

  it('saves configuration to localStorage', async () => {
    render(<ConfigPage />);
    
    // Log in first
    const passwordInput = screen.getByLabelText('Password');
    const submitButton = screen.getByRole('button', { name: 'Access Configuration' });
    fireEvent.change(passwordInput, { target: { value: 'rowing-admin-2023' } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('System Configuration')).toBeInTheDocument();
    });
    
    // Toggle a switch
    const teamCreationSwitch = screen.getByLabelText('Team Creation');
    fireEvent.click(teamCreationSwitch);
    
    // Save configuration
    const saveButton = screen.getByRole('button', { name: 'Save Configuration' });
    fireEvent.click(saveButton);
    
    expect(localStorageMock.setItem).toHaveBeenCalled();
    
    // Extract the saved configuration
    const setItemCalls = vi.mocked(localStorageMock.setItem).mock.calls;
    const savedConfigCall = setItemCalls.find(call => call[0] === 'app-config');
    const savedConfig = savedConfigCall ? JSON.parse(savedConfigCall[1]) : null;
    
    expect(savedConfig).not.toBeNull();
    expect(savedConfig.enableTeamCreation).toBe(true);
  });
});
