
import { render, screen, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import TeamsPage from '@/pages/Teams';
import { useTeams } from '@/hooks/useTeams';
import { Team } from '@/types';
import { MemoryRouter } from 'react-router-dom';

// Mock the useTeams hook
vi.mock('@/hooks/useTeams', () => ({
  useTeams: vi.fn(),
}));

// Mock the config context
vi.mock('@/context/ConfigContext', () => ({
  useConfig: () => ({
    isFeatureEnabled: () => true,
  }),
}));

// Mock the language context
vi.mock('@/context/LanguageContext', () => ({
  useLanguage: () => ({
    t: (key: string) => key, // Just return the key as the translation
  }),
}));

// Mock the auth context
vi.mock('@/context/AuthContext', () => ({
  useAuth: () => ({
    user: { id: 'user-001', name: '<PERSON>', role: 'member' },
  }),
}));

describe('Teams Page', () => {
  const mockTeams: Team[] = [
    {
      id: 'team-001',
      name: 'Morning Rowers',
      memberIds: ['user-001', 'user-002'],
      email: '<EMAIL>',
      practiceSchedules: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'team-002',
      name: 'Weekend Warriors',
      memberIds: ['user-003', 'user-004'],
      email: '<EMAIL>',
      practiceSchedules: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock implementation
    (useTeams as any).mockReturnValue({
      loading: false,
      getMyTeams: () => [mockTeams[0]],
      getOtherTeams: () => [mockTeams[1]],
      createTeam: vi.fn(),
    });
  });

  it('should render the teams page with my teams tab by default', async () => {
    render(
      <MemoryRouter>
        <TeamsPage />
      </MemoryRouter>
    );

    // Check that the page heading is rendered
    expect(screen.getByText('teams')).toBeInTheDocument();
    
    // Verify "My Teams" tab is selected by default
    await waitFor(() => {
      expect(screen.getByText('Morning Rowers')).toBeInTheDocument();
    });
    
    // "Weekend Warriors" shouldn't be visible in the initial tab
    expect(screen.queryByText('Weekend Warriors')).not.toBeInTheDocument();
  });

  it('should show loading state when teams are loading', async () => {
    // Override the mock to show loading state
    (useTeams as any).mockReturnValue({
      loading: true,
      getMyTeams: () => [],
      getOtherTeams: () => [],
      createTeam: vi.fn(),
    });

    render(
      <MemoryRouter>
        <TeamsPage />
      </MemoryRouter>
    );

    // Loading skeletons should be visible
    const skeletons = document.querySelectorAll('.animate-pulse');
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('should display empty state when user has no teams', async () => {
    // Override the mock to return empty teams
    (useTeams as any).mockReturnValue({
      loading: false,
      getMyTeams: () => [],
      getOtherTeams: () => [mockTeams[1]],
      createTeam: vi.fn(),
    });

    render(
      <MemoryRouter>
        <TeamsPage />
      </MemoryRouter>
    );

    // Empty state message should be visible
    await waitFor(() => {
      expect(screen.getByText('no_teams_yet')).toBeInTheDocument();
    });
  });

  it('should display other teams when switching to that tab', async () => {
    render(
      <MemoryRouter>
        <TeamsPage />
      </MemoryRouter>
    );

    // Click on the "Other Teams" tab
    const otherTeamsTab = screen.getByText('other_teams');
    otherTeamsTab.click();

    // "Weekend Warriors" should now be visible
    await waitFor(() => {
      expect(screen.getByText('Weekend Warriors')).toBeInTheDocument();
    });

    // "Morning Rowers" shouldn't be visible in the other teams tab
    expect(screen.queryByText('Morning Rowers')).not.toBeInTheDocument();
  });
});
