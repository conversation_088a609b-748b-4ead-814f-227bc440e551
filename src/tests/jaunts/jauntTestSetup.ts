
import { vi } from 'vitest';
import { Jaunt } from '@/types';

// Mock data
export let mockJaunts: Jaunt[] = [];
export let mockUsers: any[] = [];
export let mockWatercrafts: any[] = [];

// Mock the jaunt ID generator
vi.mock('@/utils/idGenerator', () => ({
  generateJauntId: vi.fn(() => 'jaunt-test-123'),
}));

// Mock the imports for the service
vi.mock('@/services/mockData/jaunts', () => ({
  mockJaunts: {
    get: () => mockJaunts,
    push: (item: any) => mockJaunts.push(item),
    findIndex: (fn: any) => mockJaunts.findIndex(fn),
    filter: (fn: any) => mockJaunts.filter(fn),
  }
}));

vi.mock('@/services/mockData/users', () => ({
  mockUsers: {
    get: () => mockUsers,
    findIndex: (fn: any) => mockUsers.findIndex(fn),
  }
}));

vi.mock('@/services/mockData/watercrafts', () => ({
  mockWatercrafts: {
    get: () => mockWatercrafts,
    findIndex: (fn: any) => mockWatercrafts.findIndex(fn),
    filter: (fn: any) => mockWatercrafts.filter(fn),
  }
}));

// Mock current date/time for testing
export const mockDate = new Date('2023-07-15T10:00:00Z');
vi.spyOn(global, 'Date').mockImplementation(() => mockDate as any);

// Setup test data
export function setupTestData() {
  mockJaunts = [
    {
      id: 'jaunt-001',
      userId: 'user-001',
      watercraftId: 'watercraft-001',
      startTime: new Date('2023-07-15T08:00:00Z'),
      plannedEndTime: new Date('2023-07-15T10:00:00Z'),
      createdAt: new Date('2023-07-14'),
      updatedAt: new Date('2023-07-14'),
    },
    {
      id: 'jaunt-002',
      userId: 'user-002',
      watercraftId: 'watercraft-002',
      startTime: new Date('2023-07-15T09:00:00Z'),
      plannedEndTime: new Date('2023-07-15T11:00:00Z'),
      createdAt: new Date('2023-07-14'),
      updatedAt: new Date('2023-07-14'),
    }
  ];

  mockUsers = [
    { id: 'user-001', name: 'Test User 1' },
    { id: 'user-002', name: 'Test User 2' },
  ];

  mockWatercrafts = [
    { id: 'watercraft-001', name: 'Test Watercraft 1', status: 'in-use' },
    { id: 'watercraft-002', name: 'Test Watercraft 2', status: 'in-use' },
    { id: 'watercraft-003', name: 'Test Watercraft 3', status: 'available' },
  ];
}

// Reset all mocks
export function resetMocks() {
  vi.resetAllMocks();
}
