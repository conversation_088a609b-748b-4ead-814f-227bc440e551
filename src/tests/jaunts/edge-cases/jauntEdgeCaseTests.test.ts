
import { describe, it, expect, beforeEach } from 'vitest';
import { createValidJaunt } from '../../utils/testUtils';
import { setupTestData, resetMocks, mockJaunts, mockDate } from '../jauntTestSetup';
import { hasOverlap, isValidDuration, areDatesInFuture } from '../utils/jauntTestHelpers';

describe('Jaunt Scheduling Edge Cases', () => {
  beforeEach(() => {
    setupTestData();
    resetMocks();
  });

  it('should handle concurrent jaunt creation attempts for the same watercraft', () => {
    // Simulate two users trying to create jaunts for the same watercraft at the same time
    // by checking if our validation would catch it
    
    const jaunt1 = createValidJaunt({
      watercraftId: 'watercraft-003',
      startTime: new Date('2023-07-16T10:00:00Z'),
      plannedEndTime: new Date('2023-07-16T12:00:00Z'),
    });
    
    const jaunt2 = createValidJaunt({
      watercraftId: 'watercraft-003',
      startTime: new Date('2023-07-16T11:00:00Z'),
      plannedEndTime: new Date('2023-07-16T13:00:00Z'),
    });
    
    // First jaunt doesn't overlap with anything
    expect(hasOverlap(jaunt1)).toBe(false);
    
    // Add the first jaunt to our mock data
    mockJaunts.push(jaunt1);
    
    // Now the second jaunt should overlap
    expect(hasOverlap(jaunt2)).toBe(true);
  });

  it('should handle zero-duration jaunts correctly', () => {
    // Create a jaunt with the same start and end time
    const zeroDurationJaunt = createValidJaunt({
      watercraftId: 'watercraft-003',
      startTime: new Date('2023-07-16T14:00:00Z'),
      plannedEndTime: new Date('2023-07-16T14:00:00Z'),
    });
    
    // This should be invalid
    expect(isValidDuration(zeroDurationJaunt)).toBe(false);
  });

  it('should handle past dates correctly when scheduling jaunts', () => {
    // Create a jaunt with start date in the past
    const pastStartJaunt = createValidJaunt({
      watercraftId: 'watercraft-003',
      startTime: new Date('2020-01-01T10:00:00Z'), // Past date
      plannedEndTime: new Date('2020-01-01T12:00:00Z'), // Past date
    });
    
    expect(areDatesInFuture(pastStartJaunt, mockDate)).toBe(false);
  });
});
