
import { describe, it, expect, beforeEach } from 'vitest';
import { Jaunt } from '@/types';
import { createValidJaunt } from '../utils/testUtils';
import { setupTestData, resetMocks } from './jauntTestSetup';

describe('Jaunt Validation', () => {
  beforeEach(() => {
    setupTestData();
    resetMocks();
  });

  // Helper function to validate jaunt data
  function validateJaunt(jaunt: Partial<Jaunt>): void {
    // Check required fields
    if (!jaunt.userId) throw new Error('User ID is required');
    if (!jaunt.watercraftId) throw new Error('Watercraft ID is required');
    if (!jaunt.startTime) throw new Error('Start time is required');
    if (!jaunt.plannedEndTime) throw new Error('Planned end time is required');
    
    // Validate time logic
    if (jaunt.startTime > jaunt.plannedEndTime) {
      throw new Error('Start time must be before planned end time');
    }
    
    // If actual end time exists, it should be after start time
    if (jaunt.actualEndTime && jaunt.actualEndTime < jaunt.startTime) {
      throw new Error('Actual end time must be after start time');
    }
  }

  it('should validate jaunt data correctly', () => {
    // Test valid jaunt
    const validJaunt = createValidJaunt();
    expect(() => validateJaunt(validJaunt)).not.toThrow();
    
    // Test invalid jaunt - missing required fields
    const invalidJaunt1 = { ...validJaunt, userId: undefined };
    expect(() => validateJaunt(invalidJaunt1 as any)).toThrow();
    
    // Test invalid jaunt - startTime after plannedEndTime
    const invalidJaunt2 = {
      ...validJaunt,
      startTime: new Date('2023-07-15T12:00:00Z'),
      plannedEndTime: new Date('2023-07-15T10:00:00Z'),
    };
    expect(() => validateJaunt(invalidJaunt2)).toThrow();
  });
});
