
import { describe, it, expect, beforeEach } from 'vitest';
import { setupTestData, resetMocks } from './jauntTestSetup';

describe('Jaunt Service', () => {
  beforeEach(() => {
    setupTestData();
    resetMocks();
  });

  it('should create a jaunt service', async () => {
    // Import the specific functions from jaunt.service instead of using default
    const { getAllJaunts, getJauntById, createJaunt, updateJaunt, deleteJaunt } = await import('@/services/jaunt.service');
    
    // Verify that each function exists
    expect(getAllJaunts).toBeDefined();
    expect(getJauntById).toBeDefined();
    expect(createJaunt).toBeDefined();
    expect(updateJaunt).toBeDefined();
    expect(deleteJaunt).toBeDefined();
    
    // Verify functions are the correct type
    expect(typeof getAllJaunts).toBe('function');
    expect(typeof getJauntById).toBe('function');
    expect(typeof createJaunt).toBe('function');
    expect(typeof updateJaunt).toBe('function');
    expect(typeof deleteJaunt).toBe('function');
  });
});
