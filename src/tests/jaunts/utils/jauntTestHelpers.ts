
import { Jaunt } from '@/types';
import { mockJaunts, mockWatercrafts } from '../jauntTestSetup';

// Helper function to check for jaunt time conflicts
export const hasOverlap = (jaunt: Partial<Jaunt>): boolean => {
  if (!jaunt.watercraftId || !jaunt.startTime || !jaunt.plannedEndTime) {
    return false;
  }
  
  return mockJaunts.some(existingJaunt => {
    // Skip deleted jaunts
    if (existingJaunt.deleted) return false;
    
    // Skip if different watercraft
    if (existingJaunt.watercraftId !== jaunt.watercraftId) return false;
    
    // Check for time overlap
    const newStart = new Date(jaunt.startTime);
    const newEnd = new Date(jaunt.plannedEndTime);
    const existingStart = new Date(existingJaunt.startTime);
    const existingEnd = existingJaunt.actualEndTime ? 
      new Date(existingJaunt.actualEndTime) : 
      new Date(existingJaunt.plannedEndTime);
    
    // Overlap exists if:
    // 1. New start time is between existing start and end
    // 2. New end time is between existing start and end
    // 3. New jaunt completely contains existing jaunt
    return (
      (newStart >= existingStart && newStart <= existingEnd) ||
      (newEnd >= existingStart && newEnd <= existingEnd) ||
      (newStart <= existingStart && newEnd >= existingEnd)
    );
  });
};

// Helper to check if watercraft is available
export const isWatercraftAvailable = (watercraftId: string): boolean => {
  const watercraft = mockWatercrafts.find(wc => wc.id === watercraftId);
  return watercraft && watercraft.status === 'available';
};

// Helper to update watercraft status
export const updateWatercraftStatus = (watercraftId: string, status: 'in-use' | 'available'): void => {
  const index = mockWatercrafts.findIndex(wc => wc.id === watercraftId);
  if (index !== -1) {
    mockWatercrafts[index].status = status;
  }
};

// Helper to validate jaunt duration
export const isValidDuration = (jaunt: Partial<Jaunt>): boolean => {
  if (!jaunt.startTime || !jaunt.plannedEndTime) return false;
  return new Date(jaunt.plannedEndTime) > new Date(jaunt.startTime);
};

// Helper to check if dates are in the future
export const areDatesInFuture = (jaunt: Partial<Jaunt>, mockCurrentDate: Date): boolean => {
  if (!jaunt.startTime || !jaunt.plannedEndTime) return false;
  return new Date(jaunt.startTime) >= mockCurrentDate;
};
