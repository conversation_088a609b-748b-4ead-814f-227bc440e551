
import { describe, it, expect, beforeEach } from 'vitest';
import { createValidJaunt } from '../../utils/testUtils';
import { setupTestData, resetMocks, mockJaunts } from '../jauntTestSetup';
import { hasOverlap } from '../utils/jauntTestHelpers';

describe('Basic Jaunt Scheduling', () => {
  beforeEach(() => {
    setupTestData();
    resetMocks();
  });

  it('should not allow creating overlapping jaunts for the same watercraft', () => {
    // Test non-overlapping jaunt
    const nonOverlappingJaunt = createValidJaunt({
      watercraftId: 'watercraft-001',
      startTime: new Date('2023-07-15T14:00:00Z'),
      plannedEndTime: new Date('2023-07-15T16:00:00Z'),
    });
    
    expect(hasOverlap(nonOverlappingJaunt)).toBe(false);
    
    // Test overlapping jaunt
    const overlappingJaunt = createValidJaunt({
      watercraftId: 'watercraft-001',
      startTime: new Date('2023-07-15T09:00:00Z'),
      plannedEndTime: new Date('2023-07-15T11:00:00Z'),
    });
    
    expect(hasOverlap(overlappingJaunt)).toBe(true);
  });

  it('should handle edge case of jaunt ending exactly when another starts', () => {
    // Create a jaunt that ends exactly when an existing jaunt starts
    const edgeCaseJaunt = createValidJaunt({
      watercraftId: 'watercraft-001',
      startTime: new Date('2023-07-15T06:00:00Z'),
      plannedEndTime: new Date('2023-07-15T08:00:00Z'), // Ends exactly when jaunt-001 starts
    });
    
    // This should not be considered an overlap
    expect(hasOverlap(edgeCaseJaunt)).toBe(false);
  });

  it('should handle deleted jaunts correctly when checking for overlaps', () => {
    // First mark a jaunt as deleted
    const deletedJauntIndex = mockJaunts.findIndex(j => j.id === 'jaunt-001');
    if (deletedJauntIndex !== -1) {
      mockJaunts[deletedJauntIndex] = { 
        ...mockJaunts[deletedJauntIndex], 
        deleted: true 
      };
    }
    
    // Now create a jaunt that would overlap with the deleted jaunt
    const newJaunt = createValidJaunt({
      watercraftId: 'watercraft-001',
      startTime: new Date('2023-07-15T09:00:00Z'), // During the time of the deleted jaunt
      plannedEndTime: new Date('2023-07-15T11:00:00Z'),
    });
    
    // Should not consider deleted jaunts when checking for overlaps
    expect(hasOverlap(newJaunt)).toBe(false);
  });
});
