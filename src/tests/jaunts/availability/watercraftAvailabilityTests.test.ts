
import { describe, it, expect, beforeEach } from 'vitest';
import { createValidJaunt } from '../../utils/testUtils';
import { setupTestData, resetMocks, mockWatercrafts } from '../jauntTestSetup';
import { isWatercraftAvailable, updateWatercraftStatus } from '../utils/jauntTestHelpers';

describe('Watercraft Availability for Jaunts', () => {
  beforeEach(() => {
    setupTestData();
    resetMocks();
  });

  it('should require a watercraft to be available to create a jaunt', () => {
    // Test available watercraft
    expect(isWatercraftAvailable('watercraft-003')).toBe(true);
    
    // Test unavailable watercraft
    expect(isWatercraftAvailable('watercraft-001')).toBe(false);
    
    // Test non-existent watercraft
    expect(isWatercraftAvailable('non-existent')).toBe(false);
  });

  it('should update watercraft status when a jaunt is created or ended', () => {
    // Test changing watercraft status to in-use
    updateWatercraftStatus('watercraft-003', 'in-use');
    expect(mockWatercrafts.find(wc => wc.id === 'watercraft-003')?.status).toBe('in-use');
    
    // Test changing watercraft status to available
    updateWatercraftStatus('watercraft-001', 'available');
    expect(mockWatercrafts.find(wc => wc.id === 'watercraft-001')?.status).toBe('available');
  });

  it('should prevent creating a jaunt for a watercraft in maintenance status', () => {
    // Set a watercraft to maintenance status
    const maintenanceWatercraftIndex = mockWatercrafts.findIndex(wc => wc.id === 'watercraft-003');
    if (maintenanceWatercraftIndex !== -1) {
      mockWatercrafts[maintenanceWatercraftIndex].status = 'maintenance';
    }
    
    // Should not allow creating a jaunt for a watercraft in maintenance
    expect(isWatercraftAvailable('watercraft-003')).toBe(false);
  });
});
