
import { describe, it, expect } from 'vitest';
import { Watercraft, Boat } from '@/types';

describe('WatercraftFormModel', () => {
  it('should provide default values for watercraft form', async () => {
    const { getDefaultWatercraftFormValues } = await import('@/components/admin/watercraft/WatercraftFormModel');
    
    const defaultValues = getDefaultWatercraftFormValues();
    
    expect(defaultValues.name).toBe('');
    expect(defaultValues.type).toBe('boat');
    expect(defaultValues.boatType).toBe('1x');
    expect(defaultValues.ownershipType).toBe('club');
    expect(defaultValues.memberId).toBe('');
    expect(defaultValues.location).toBe('');
    expect(defaultValues.skillLevel).toBe('1');
    expect(defaultValues.status).toBe('available');
    expect(defaultValues.weightMin).toBe('70');
    expect(defaultValues.weightMax).toBe('85');
  });
  
  it('should convert boat watercraft to form values correctly', async () => {
    const { watercraftToFormValues } = await import('@/components/admin/watercraft/WatercraftFormModel');
    
    const boat: Boat = {
      id: 'boat-123',
      name: 'Test Boat',
      type: 'boat',
      boatType: '8+',
      ownershipType: 'club',
      location: 'Test Location',
      skillLevel: 2,
      status: 'available',
      weightRange: {
        min: 75,
        max: 85
      }
    };
    
    const formValues = watercraftToFormValues(boat);
    
    expect(formValues.id).toBe('boat-123');
    expect(formValues.name).toBe('Test Boat');
    expect(formValues.type).toBe('boat');
    expect(formValues.boatType).toBe('8+');
    expect(formValues.ownershipType).toBe('club');
    expect(formValues.location).toBe('Test Location');
    expect(formValues.skillLevel).toBe('2');
    expect(formValues.status).toBe('available');
    expect(formValues.weightMin).toBe('75');
    expect(formValues.weightMax).toBe('85');
  });
  
  it('should handle non-boat watercraft correctly', async () => {
    const { watercraftToFormValues } = await import('@/components/admin/watercraft/WatercraftFormModel');
    
    const kayak: Watercraft = {
      id: 'kayak-123',
      name: 'Test Kayak',
      type: 'kayak',
      ownershipType: 'club',
      location: 'Test Location',
      skillLevel: 1,
      status: 'available'
    };
    
    const formValues = watercraftToFormValues(kayak);
    
    expect(formValues.id).toBe('kayak-123');
    expect(formValues.name).toBe('Test Kayak');
    expect(formValues.type).toBe('kayak');
    expect(formValues.boatType).toBe('1x'); // Should use default
    expect(formValues.weightMin).toBe('70'); // Should use default
    expect(formValues.weightMax).toBe('85'); // Should use default
  });
  
  it('should handle null input', async () => {
    const { watercraftToFormValues } = await import('@/components/admin/watercraft/WatercraftFormModel');
    
    const formValues = watercraftToFormValues(null);
    
    // Should return default values
    expect(formValues.name).toBe('');
    expect(formValues.type).toBe('boat');
    expect(formValues.boatType).toBe('1x');
  });
  
  it('should handle coastal boats with boatType', async () => {
    const { watercraftToFormValues } = await import('@/components/admin/watercraft/WatercraftFormModel');
    
    // Create a coastal boat with a boatType (edge case)
    const coastalBoat: any = {
      id: 'coastal-123',
      name: 'Coastal Boat',
      type: 'coastal',
      boatType: '1x', // Coastal with boat type
      ownershipType: 'club',
      location: 'Coastal Bay',
      skillLevel: 2,
      status: 'available'
    };
    
    const formValues = watercraftToFormValues(coastalBoat);
    
    expect(formValues.type).toBe('coastal');
    expect(formValues.boatType).toBe('1x'); // Should preserve the boat type
  });
});
