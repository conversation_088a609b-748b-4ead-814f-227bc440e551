import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, act } from '@testing-library/react';
import { ConfigProvider, useConfig, AppConfig } from '@/context/ConfigContext';

// Mock component that uses the config context
const TestComponent = () => {
  const { config, isFeatureEnabled } = useConfig();
  
  return (
    <div>
      <h1>Config Test</h1>
      <div data-testid="team-creation-status">
        {isFeatureEnabled('enableTeamCreation') ? 'Enabled' : 'Disabled'}
      </div>
      <div data-testid="member-signup-status">
        {isFeatureEnabled('enableMemberSignUp') ? 'Enabled' : 'Disabled'}
      </div>
      <div data-testid="all-config">{JSON.stringify(config)}</div>
    </div>
  );
};

// Mock local storage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key];
    }),
    clear: vi.fn(() => {
      store = {};
    }),
  };
})();

Object.defineProperty(window, 'localStorage', { value: localStorageMock });

describe('ConfigContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.clear();
  });

  it('provides default config values', () => {
    render(
      <ConfigProvider>
        <TestComponent />
      </ConfigProvider>
    );
    
    expect(screen.getByTestId('team-creation-status')).toHaveTextContent('Disabled');
    expect(screen.getByTestId('member-signup-status')).toHaveTextContent('Enabled');
  });

  it('loads config from localStorage', () => {
    const testConfig: AppConfig = {
      enableTeamCreation: true,
      enableAdvancedScheduling: false,
      enableWeatherAlerts: true,
      enableMaintenanceRequests: false,
      enableMemberSignUp: true,
      debugMode: true
    };
    
    localStorageMock.getItem.mockReturnValueOnce(JSON.stringify(testConfig));
    
    render(
      <ConfigProvider>
        <TestComponent />
      </ConfigProvider>
    );
    
    expect(screen.getByTestId('team-creation-status')).toHaveTextContent('Enabled');
    expect(screen.getByTestId('member-signup-status')).toHaveTextContent('Enabled');
    
    const configData = JSON.parse(screen.getByTestId('all-config').textContent || '{}');
    expect(configData.enableTeamCreation).toBe(true);
    expect(configData.enableMemberSignUp).toBe(true);
    expect(configData.enableWeatherAlerts).toBe(true);
    expect(configData.debugMode).toBe(true);
  });

  it('checks if a feature is enabled', () => {
    const testConfig = {
      enableTeamCreation: true,
      enableMemberSignUp: true
    };
    
    localStorageMock.getItem.mockReturnValueOnce(JSON.stringify(testConfig));
    
    render(
      <ConfigProvider>
        <TestComponent />
      </ConfigProvider>
    );
    
    expect(screen.getByTestId('team-creation-status')).toHaveTextContent('Enabled');
    expect(screen.getByTestId('member-signup-status')).toHaveTextContent('Enabled');
  });
});
