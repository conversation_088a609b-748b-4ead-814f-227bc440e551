
import { render, screen, waitFor, act } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { AuthProvider, useAuth, validateUserId } from '@/context/AuthContext';
import { mockUsers } from '@/services/mockData';

describe('AuthContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorage.clear();
  });

  describe('validateUserId function', () => {
    it('should validate correct user ID format', () => {
      expect(validateUserId('user-001')).toBe(true);
      expect(validateUserId('user-123')).toBe(true);
      expect(validateUserId('user-999')).toBe(true);
    });

    it('should reject invalid user ID formats', () => {
      expect(validateUserId('usr-123')).toBe(false);
      expect(validateUserId('user-1')).toBe(false);
      expect(validateUserId('user-12')).toBe(false);
      expect(validateUserId('user-1234')).toBe(false);
      expect(validateUserId('user-abc')).toBe(false);
      expect(validateUserId('user_001')).toBe(false);
    });
  });

  describe('AuthProvider', () => {
    const TestComponent = () => {
      const { user, isAuthenticated } = useAuth();
      return (
        <div>
          {isAuthenticated ? (
            <div>
              <div data-testid="user-id">{user?.id}</div>
              <div data-testid="user-name">{user?.name}</div>
            </div>
          ) : (
            <div>Not authenticated</div>
          )}
        </div>
      );
    };

    it('should initialize with default user that has a valid ID', async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        const userId = screen.getByTestId('user-id');
        expect(userId).toHaveTextContent('user-001');
        // Verify the ID is valid by our validation function
        expect(validateUserId(userId.textContent || '')).toBe(true);
      });
    });

    it('should restore a user with valid ID from localStorage', async () => {
      const validUser = {
        ...mockUsers[0],
        id: 'user-002',
        name: 'Test User',
      };
      
      localStorage.setItem('user', JSON.stringify(validUser));

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('user-id')).toHaveTextContent('user-002');
        expect(screen.getByTestId('user-name')).toHaveTextContent('Test User');
      });
    });

    it('should not restore a user with invalid ID from localStorage', async () => {
      const invalidUser = {
        ...mockUsers[0],
        id: 'invalid-id',
        name: 'Invalid User',
      };
      
      localStorage.setItem('user', JSON.stringify(invalidUser));

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      // Default user should be used
      await waitFor(() => {
        expect(screen.getByTestId('user-id')).toHaveTextContent('user-001');
        expect(screen.getByTestId('user-name')).toHaveTextContent('Alex Johnson');
      });
    });
  });
});
