
import { describe, it, expect } from 'vitest';
import { Boat, Watercraft } from '@/types';

describe('Watercraft Form Model Tests', () => {
  it('should correctly map a boat object to form values', async () => {
    const { watercraftToFormValues } = await import('@/components/admin/watercraft/WatercraftFormModel');
    
    const boat: Boat = {
      id: 'boat-123',
      name: 'Test Boat',
      type: 'boat',
      boatType: '4+',
      ownershipType: 'club',
      location: 'Test Location',
      skillLevel: 2,
      status: 'available',
      weightRange: {
        min: 75,
        max: 90
      }
    };
    
    const formValues = watercraftToFormValues(boat);
    
    expect(formValues.id).toBe('boat-123');
    expect(formValues.name).toBe('Test Boat');
    expect(formValues.type).toBe('boat');
    expect(formValues.boatType).toBe('4+');
    expect(formValues.ownershipType).toBe('club');
    expect(formValues.location).toBe('Test Location');
    expect(formValues.skillLevel).toBe('2');
    expect(formValues.status).toBe('available');
    expect(formValues.weightMin).toBe('75');
    expect(formValues.weightMax).toBe('90');
  });

  it('should handle missing boat data gracefully', async () => {
    const { watercraftToFormValues } = await import('@/components/admin/watercraft/WatercraftFormModel');
    
    const watercraft: Watercraft = {
      id: 'watercraft-123',
      name: 'Test Kayak',
      type: 'kayak',
      ownershipType: 'club',
      location: 'Test Location',
      skillLevel: 1,
      status: 'available',
    };
    
    const formValues = watercraftToFormValues(watercraft);
    
    expect(formValues.type).toBe('kayak');
    expect(formValues.boatType).toBe('1x'); // Default boat type
    expect(formValues.weightMin).toBe('70'); // Default weight
    expect(formValues.weightMax).toBe('85'); // Default weight
  });
});
