
import { describe, it, expect, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react-hooks';
import { BoatType, WatercraftStatus, WatercraftType } from '@/types';
import { useWatercraftManagement } from '@/components/admin/watercraft/useWatercraftManagement';
import { setupTestData, setupUseStateMock, resetMocks, mockSetWatercrafts, mockWatercrafts, WatercraftFormValues } from './watercraftTestSetup';

describe('Watercraft Form Submission', () => {
  beforeEach(() => {
    setupTestData();
    setupUseStateMock();
    resetMocks();
  });

  it('should handle form submission - create new watercraft', () => {
    const { result } = renderHook(() => useWatercraftManagement());
    
    const formData: WatercraftFormValues = {
      name: 'New Watercraft',
      type: 'boat' as WatercraftType,
      boatType: '1x' as BoatType,
      ownershipType: 'club',
      memberId: '',
      location: 'New Bay',
      skillLevel: '1',
      status: 'available' as WatercraftStatus,
      weightMin: '70',
      weightMax: '85',
    };
    
    act(() => {
      result.current.handleSubmit(formData as any);
    });
    
    expect(mockSetWatercrafts).toHaveBeenCalled();
    // Get the callback function passed to setWatercrafts
    const updateFn = mockSetWatercrafts.mock.calls[0][0];
    const updatedList = updateFn(mockWatercrafts);
    // Check that a new watercraft was added
    expect(updatedList.length).toBe(mockWatercrafts.length + 1);
    expect(updatedList[1].name).toBe('New Watercraft');
  });

  it('should handle form submission - update existing watercraft', () => {
    const { result } = renderHook(() => useWatercraftManagement());
    
    const formData: WatercraftFormValues = {
      id: 'watercraft-123',
      name: 'Updated Watercraft',
      type: 'boat' as WatercraftType,
      boatType: '2x' as BoatType,
      ownershipType: 'club',
      memberId: '',
      location: 'Updated Bay',
      skillLevel: '2',
      status: 'maintenance' as WatercraftStatus,
      weightMin: '75',
      weightMax: '90',
    };
    
    act(() => {
      result.current.handleSubmit(formData as any);
    });
    
    expect(mockSetWatercrafts).toHaveBeenCalled();
    // Check the updated watercraft
    const updatedWatercrafts = mockSetWatercrafts.mock.calls[0][0](mockWatercrafts);
    expect(updatedWatercrafts[0].name).toBe('Updated Watercraft');
    expect(updatedWatercrafts[0].location).toBe('Updated Bay');
    expect(updatedWatercrafts[0].skillLevel).toBe(2);
    expect(updatedWatercrafts[0].status).toBe('maintenance');
  });
});
