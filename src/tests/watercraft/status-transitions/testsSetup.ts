
import { vi } from 'vitest';
import { renderHook } from '@testing-library/react-hooks';
import { setupTestData, resetMocks, mockWatercrafts, mockSetWatercrafts } from '../watercraftTestSetup';
import { useWatercraftManagement } from '@/components/admin/watercraft/useWatercraftManagement';

// Helper to setup the watercraft management hook
export const setupWatercraftHook = () => {
  const useState = vi.fn();
  useState.mockImplementationOnce(() => [mockWatercrafts, mockSetWatercrafts]);
  useState.mockImplementationOnce(() => ['', vi.fn()]);
  return renderHook(() => useWatercraftManagement());
};

