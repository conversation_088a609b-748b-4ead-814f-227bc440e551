import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react-hooks';
import { setupTestData, resetMocks, mockWatercrafts, mockSetWatercrafts } from '../watercraftTestSetup';
import { useWatercraftManagement } from '@/components/admin/watercraft/useWatercraftManagement';

// Mock useState since we're testing hooks
vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...(actual as object),
    useState: vi.fn(),
  };
});

// Helper to setup the watercraft management hook
const setupWatercraftHook = () => {
  const useState = vi.fn();
  useState.mockImplementationOnce(() => [mockWatercrafts, mockSetWatercrafts]);
  useState.mockImplementationOnce(() => ['', vi.fn()]);
  return renderHook(() => useWatercraftManagement());
};

// Edge cases
describe('Edge Cases', () => {
  beforeEach(() => {
    setupTestData();
    resetMocks();
  });

  it('should update timestamps when changing status', () => {
    const { result } = setupWatercraftHook();
    
    // Store original timestamp
    const originalTimestamp = mockWatercrafts[0].updatedAt;
    
    // Wait a moment to ensure timestamp difference
    vi.advanceTimersByTime(1000);
    
    act(() => {
      result.current.changeStatus('watercraft-123', 'maintenance');
    });
    
    const mappingFn = mockSetWatercrafts.mock.calls[0][0];
    const updatedWatercrafts = mappingFn(mockWatercrafts);
    expect(updatedWatercrafts[0].updatedAt).not.toBe(originalTimestamp);
  });

  it('should not modify other watercraft when changing status', () => {
    // Add a second watercraft for this test
    mockWatercrafts.push({
      id: 'watercraft-456',
      name: 'Second Watercraft',
      type: 'kayak',
      ownershipType: 'club',
      location: 'Test Dock',
      skillLevel: 2,
      status: 'available',
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    const { result } = setupWatercraftHook();
    
    act(() => {
      result.current.changeStatus('watercraft-123', 'maintenance');
    });
    
    const mappingFn = mockSetWatercrafts.mock.calls[0][0];
    const updatedWatercrafts = mappingFn(mockWatercrafts);
    
    // First watercraft should have updated status
    expect(updatedWatercrafts[0].status).toBe('maintenance');
    
    // Second watercraft should remain unchanged
    expect(updatedWatercrafts[1].status).toBe('available');
  });

  it('should handle unchanged status transitions gracefully', () => {
    // Set initial status
    mockWatercrafts[0].status = 'available';
    
    const { result } = setupWatercraftHook();
    
    act(() => {
      // Change to same status
      result.current.changeStatus('watercraft-123', 'available');
    });
    
    const mappingFn = mockSetWatercrafts.mock.calls[0][0];
    const updatedWatercrafts = mappingFn(mockWatercrafts);
    
    // Status should remain the same
    expect(updatedWatercrafts[0].status).toBe('available');
    // But updatedAt should still be modified
    expect(updatedWatercrafts[0].updatedAt).toBeDefined();
  });

  it('should handle non-existent watercraft ids gracefully', () => {
    const { result } = setupWatercraftHook();
    
    act(() => {
      result.current.changeStatus('non-existent-id', 'maintenance');
    });
    
    const mappingFn = mockSetWatercrafts.mock.calls[0][0];
    const updatedWatercrafts = mappingFn(mockWatercrafts);
    
    // No watercraft should have changed
    expect(updatedWatercrafts[0].status).toBe(mockWatercrafts[0].status);
  });
});
