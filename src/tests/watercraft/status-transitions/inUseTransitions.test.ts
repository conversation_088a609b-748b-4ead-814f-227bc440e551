
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react-hooks';
import { setupTestData, resetMocks, mockWatercrafts, mockSetWatercrafts } from '../watercraftTestSetup';
import { useWatercraftManagement } from '@/components/admin/watercraft/useWatercraftManagement';

// Mock useState since we're testing hooks
vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...(actual as object),
    useState: vi.fn(),
  };
});

// Helper to setup the watercraft management hook
const setupWatercraftHook = () => {
  const useState = vi.fn();
  useState.mockImplementationOnce(() => [mockWatercrafts, mockSetWatercrafts]);
  useState.mockImplementationOnce(() => ['', vi.fn()]);
  return renderHook(() => useWatercraftManagement());
};

// In-use status transitions
describe('In-Use Status Transitions', () => {
  beforeEach(() => {
    setupTestData();
    resetMocks();
    // Set initial status to in-use
    mockWatercrafts[0].status = 'in-use';
  });

  it('should change from in-use to available', () => {
    const { result } = setupWatercraftHook();
    
    act(() => {
      result.current.changeStatus('watercraft-123', 'available');
    });
    
    const mappingFn = mockSetWatercrafts.mock.calls[0][0];
    const updatedWatercrafts = mappingFn(mockWatercrafts);
    expect(updatedWatercrafts[0].status).toBe('available');
  });

  it('should change from in-use to maintenance', () => {
    const { result } = setupWatercraftHook();
    
    act(() => {
      result.current.changeStatus('watercraft-123', 'maintenance');
    });
    
    const mappingFn = mockSetWatercrafts.mock.calls[0][0];
    const updatedWatercrafts = mappingFn(mockWatercrafts);
    expect(updatedWatercrafts[0].status).toBe('maintenance');
  });

  it('should change from in-use to regatta', () => {
    const { result } = setupWatercraftHook();
    
    act(() => {
      result.current.changeStatus('watercraft-123', 'regatta');
    });
    
    const mappingFn = mockSetWatercrafts.mock.calls[0][0];
    const updatedWatercrafts = mappingFn(mockWatercrafts);
    expect(updatedWatercrafts[0].status).toBe('regatta');
  });
});
