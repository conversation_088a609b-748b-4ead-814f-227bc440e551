
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react-hooks';
import { setupTestData, resetMocks, mockWatercrafts, mockSetWatercrafts } from '../watercraftTestSetup';
import { useWatercraftManagement } from '@/components/admin/watercraft/useWatercraftManagement';

// Mock useState since we're testing hooks
vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...(actual as object),
    useState: vi.fn(),
  };
});

// Helper to setup the watercraft management hook
const setupWatercraftHook = () => {
  const useState = vi.fn();
  useState.mockImplementationOnce(() => [mockWatercrafts, mockSetWatercrafts]);
  useState.mockImplementationOnce(() => ['', vi.fn()]);
  return renderHook(() => useWatercraftManagement());
};

// Standard status transitions
describe('Standard Status Transitions', () => {
  beforeEach(() => {
    setupTestData();
    resetMocks();
  });

  it('should change from available to in-use', () => {
    const { result } = setupWatercraftHook();
    
    act(() => {
      result.current.changeStatus('watercraft-123', 'in-use');
    });
    
    // Verify the mapping logic works
    const mappingFn = mockSetWatercrafts.mock.calls[0][0];
    const updatedWatercrafts = mappingFn(mockWatercrafts);
    expect(updatedWatercrafts[0].status).toBe('in-use');
  });

  it('should change from available to maintenance', () => {
    const { result } = setupWatercraftHook();
    
    act(() => {
      result.current.changeStatus('watercraft-123', 'maintenance');
    });
    
    const mappingFn = mockSetWatercrafts.mock.calls[0][0];
    const updatedWatercrafts = mappingFn(mockWatercrafts);
    expect(updatedWatercrafts[0].status).toBe('maintenance');
  });

  it('should change from available to regatta', () => {
    const { result } = setupWatercraftHook();
    
    act(() => {
      result.current.changeStatus('watercraft-123', 'regatta');
    });
    
    const mappingFn = mockSetWatercrafts.mock.calls[0][0];
    const updatedWatercrafts = mappingFn(mockWatercrafts);
    expect(updatedWatercrafts[0].status).toBe('regatta');
  });
});
