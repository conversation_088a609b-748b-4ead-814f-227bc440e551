
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react-hooks';
import { WatercraftStatus } from '@/types';
import { useWatercraftManagement } from '@/components/admin/watercraft/useWatercraftManagement';
import { setupTestData, setupUseStateMock, resetMocks, mockSetWatercrafts, mockWatercrafts } from './watercraftTestSetup';

// Mock useState since we're testing hooks
vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...(actual as object),
    useState: vi.fn(),
  };
});

describe('Watercraft Management Hooks', () => {
  beforeEach(() => {
    setupTestData();
    setupUseStateMock();
    resetMocks();
  });

  describe('useWatercraftManagement', () => {
    it('should filter watercrafts correctly', () => {
      const useState = vi.fn();
      useState.mockImplementationOnce(() => [mockWatercrafts, mockSetWatercrafts]);
      useState.mockImplementationOnce(() => ['test', vi.fn()]);
      
      const { result } = renderHook(() => useWatercraftManagement());
      
      expect(result.current.watercraft.length).toBe(1);
      expect(result.current.watercraft[0].name).toBe('Test Watercraft');
    });

    it('should handle watercraft status change correctly', () => {
      const { result } = renderHook(() => useWatercraftManagement());
      
      act(() => {
        result.current.changeStatus('watercraft-123', 'maintenance' as WatercraftStatus);
      });
      
      expect(mockSetWatercrafts).toHaveBeenCalled();
      // Verify the mapping logic works
      const mappingFn = mockSetWatercrafts.mock.calls[0][0];
      const updatedWatercrafts = mappingFn(mockWatercrafts);
      expect(updatedWatercrafts[0].status).toBe('maintenance');
    });

    it('should handle watercraft deletion correctly', () => {
      const { result } = renderHook(() => useWatercraftManagement());
      
      act(() => {
        result.current.deleteWatercraft('watercraft-123');
      });
      
      expect(mockSetWatercrafts).toHaveBeenCalled();
      // Verify the mapping logic works
      const mappingFn = mockSetWatercrafts.mock.calls[0][0];
      const updatedWatercrafts = mappingFn(mockWatercrafts);
      expect(updatedWatercrafts[0].deleted).toBe(true);
    });
  });
});
