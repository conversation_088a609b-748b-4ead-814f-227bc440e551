
import { vi } from 'vitest';
import { Watercraft, WatercraftType, BoatType, WatercraftStatus } from '@/types';

// Mock react hooks
vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...(actual as object),
    useState: vi.fn(),
  };
});

// Mock language hook
vi.mock('@/context/LanguageContext', () => ({
  useLanguage: () => ({ t: (key: string) => key }),
}));

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock the getCleanMockWatercrafts function
vi.mock('@/services/watercraft/mock-data', () => ({
  getCleanMockWatercrafts: vi.fn(() => []),
}));

// Mock ID generator
vi.mock('@/utils/idGenerator', () => ({
  generateWatercraftId: vi.fn(() => 'watercraft-test-123'),
}));

// Define the interface for the form values
export interface WatercraftFormValues {
  id?: string;
  name: string;
  type: WatercraftType;
  boatType: BoatType;
  ownershipType: 'club' | 'member';
  memberId: string;
  location: string;
  skillLevel: string;
  status: WatercraftStatus;
  weightMin: string;
  weightMax: string;
}

// Mock data setup
export let mockWatercrafts: Watercraft[] = [];
export let mockSetWatercrafts: any;
export let mockSetSearchTerm: any;

// Setup test data
export function setupTestData() {
  mockWatercrafts = [
    {
      id: 'watercraft-123',
      name: 'Test Watercraft',
      type: 'boat',
      ownershipType: 'club',
      location: 'Test Bay',
      skillLevel: 1,
      status: 'available',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];
  
  mockSetWatercrafts = vi.fn();
  mockSetSearchTerm = vi.fn();
}

// Setup useState mock for different scenarios
export function setupUseStateMock() {
  const useState = vi.fn();
  useState.mockImplementation((initialValue) => {
    if (Array.isArray(initialValue)) {
      return [mockWatercrafts, mockSetWatercrafts];
    } else {
      return ['', mockSetSearchTerm];
    }
  });
  
  return useState;
}

// Reset all mocks
export function resetMocks() {
  vi.clearAllMocks();
}
