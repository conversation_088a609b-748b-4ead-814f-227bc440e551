
import { vi, beforeAll, afterEach, afterAll, expect } from 'vitest';
import '@testing-library/jest-dom';

// Extend Vitest's expect with Jest DOM matchers
import * as matchers from '@testing-library/jest-dom/matchers';
expect.extend(matchers);

// Setup global test environment
beforeAll(() => {
  // Set up global test environment
  console.log('Setting up test environment');
  
  // Mock timers
  vi.useFakeTimers();
  vi.setSystemTime(new Date('2023-07-15T10:00:00Z'));
});

afterEach(() => {
  // Reset mocks after each test
  vi.clearAllMocks();
});

afterAll(() => {
  // Clean up after all tests
  vi.useRealTimers();
  console.log('Test environment cleanup complete');
});

// Mock browser globals if needed
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock window.matchMedia
global.matchMedia = vi.fn().mockImplementation(query => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: vi.fn(),
  removeListener: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
}));

// This extends the vi object globally to ensure mockImplementation and mockImplementationOnce work correctly
vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...(actual as object),
    useState: vi.fn(),
  };
});
