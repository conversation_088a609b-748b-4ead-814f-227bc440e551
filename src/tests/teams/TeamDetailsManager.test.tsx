
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { TeamDetailsManager } from '@/components/teams/TeamDetailsManager';
import { createValidTeam } from '@/tests/utils/testUtils';
import { LanguageProvider } from '@/context/LanguageContext';
import { toast } from 'sonner';

// Mock the toast function
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn()
  }
}));

// Mock the practice schedule and coach assignment components
vi.mock('@/components/teams/practice-schedule/PracticeScheduleManager', () => ({
  PracticeScheduleManager: vi.fn().mockImplementation(({ team, onUpdate, readOnly }) => (
    <div data-testid="practice-schedule-manager">
      Practice Schedule Manager
    </div>
  ))
}));

vi.mock('@/components/teams/coach-assignment/CoachAssignmentManager', () => ({
  CoachAssignmentManager: vi.fn().mockImplementation(({ team, onUpdate, readOnly }) => (
    <div data-testid="coach-assignment-manager">
      Coach Assignment Manager
    </div>
  ))
}));

describe('TeamDetailsManager', () => {
  const mockTeam = createValidTeam({
    id: 'team-1',
    name: 'Test Team'
  });
  
  const mockOnTeamUpdate = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders both the coach assignment and practice schedule managers', () => {
    render(
      <LanguageProvider>
        <TeamDetailsManager 
          team={mockTeam} 
          onTeamUpdate={mockOnTeamUpdate} 
        />
      </LanguageProvider>
    );

    // Check if both managers are rendered
    expect(screen.getByTestId('coach-assignment-manager')).toBeInTheDocument();
    expect(screen.getByTestId('practice-schedule-manager')).toBeInTheDocument();
  });

  it('passes the correct props to child components', () => {
    render(
      <LanguageProvider>
        <TeamDetailsManager 
          team={mockTeam} 
          onTeamUpdate={mockOnTeamUpdate}
          readOnly={true} 
        />
      </LanguageProvider>
    );

    // Both components should be rendered in readOnly mode
    expect(screen.getByTestId('coach-assignment-manager')).toBeInTheDocument();
    expect(screen.getByTestId('practice-schedule-manager')).toBeInTheDocument();
  });

  it('handles team updates correctly', async () => {
    const { rerender } = render(
      <LanguageProvider>
        <TeamDetailsManager 
          team={mockTeam} 
          onTeamUpdate={mockOnTeamUpdate} 
        />
      </LanguageProvider>
    );

    // Update the team
    const updatedTeam = { ...mockTeam, name: 'Updated Team' };
    
    // Re-render with the updated team
    rerender(
      <LanguageProvider>
        <TeamDetailsManager 
          team={updatedTeam} 
          onTeamUpdate={mockOnTeamUpdate} 
        />
      </LanguageProvider>
    );

    // Wait for any async operations (like the setTimeout in handleTeamUpdate)
    await waitFor(() => {
      // Both components should still be rendered
      expect(screen.getByTestId('coach-assignment-manager')).toBeInTheDocument();
      expect(screen.getByTestId('practice-schedule-manager')).toBeInTheDocument();
    });
  });
});
