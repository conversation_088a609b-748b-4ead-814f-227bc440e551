
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MembersList } from '@/components/teams/members/components/MembersList';
import { LanguageProvider } from '@/context/LanguageContext';

describe('MembersList', () => {
  const mockMembers = [
    { id: "user-001", name: "<PERSON>", email: "<EMAIL>" },
    { id: "user-002", name: "<PERSON>", email: "<EMAIL>" },
    { id: "user-003", name: "<PERSON>", email: "<EMAIL>" },
  ];

  const mockProps = {
    members: mockMembers,
    onRemoveMember: vi.fn(),
    readOnly: false,
    canRemove: true,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders a list of members', () => {
    render(
      <LanguageProvider>
        <MembersList {...mockProps} />
      </LanguageProvider>
    );

    // Check if all members are rendered
    expect(screen.getByText("<PERSON>")).toBeInTheDocument();
    expect(screen.getByText("<PERSON>")).toBeInTheDocument();
    expect(screen.getByText("Taylor Smith")).toBeInTheDocument();
    
    // Check their emails
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
  });

  it('calls onRemoveMember when remove button is clicked', async () => {
    render(
      <LanguageProvider>
        <MembersList {...mockProps} />
      </LanguageProvider>
    );

    // Find all remove buttons
    const removeButtons = screen.getAllByTitle("remove_member");
    
    // Click the first remove button
    await userEvent.click(removeButtons[0]);

    // Check if onRemoveMember was called with the correct user id
    expect(mockProps.onRemoveMember).toHaveBeenCalledWith("user-001");
  });

  it('disables remove buttons when canRemove is false', () => {
    render(
      <LanguageProvider>
        <MembersList {...mockProps} canRemove={false} />
      </LanguageProvider>
    );

    // Find all remove buttons and check if they are disabled
    const removeButtons = screen.getAllByTitle("cannot_remove_last_member");
    removeButtons.forEach(button => {
      expect(button).toBeDisabled();
    });
  });

  it('does not render remove buttons in readOnly mode', () => {
    render(
      <LanguageProvider>
        <MembersList {...mockProps} readOnly={true} />
      </LanguageProvider>
    );

    // Check if no remove buttons are rendered
    expect(screen.queryByTitle("remove_member")).not.toBeInTheDocument();
  });

  it('renders empty state when no members are provided', () => {
    render(
      <LanguageProvider>
        <MembersList {...mockProps} members={[]} />
      </LanguageProvider>
    );

    // Check if empty state message is displayed
    expect(screen.getByText("no_members_found")).toBeInTheDocument();
  });
});
