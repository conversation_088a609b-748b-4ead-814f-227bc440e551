
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TeamMembersManager } from '@/components/teams/members/TeamMembersManager';
import { LanguageProvider } from '@/context/LanguageContext';
import { createValidTeam } from '@/tests/utils/testUtils';
import { toast } from 'sonner';
import { mockAllUsers } from '@/components/teams/members/mockUsers';

// Mock the toast function
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn()
  }
}));

// Mock the mockAllUsers dependency
vi.mock('@/components/teams/members/mockUsers', () => ({
  mockAllUsers: [
    { id: "user-001", name: "<PERSON>", email: "<EMAIL>" },
    { id: "user-002", name: "<PERSON>", email: "<EMAIL>" },
    { id: "user-003", name: "<PERSON>", email: "<EMAIL>" },
    { id: "user-004", name: "<PERSON>", email: "jord<PERSON>@example.com" },
    { id: "user-005", name: "<PERSON> Lee", email: "<EMAIL>" },
  ]
}));

describe('TeamMembersManager', () => {
  // Create a mock team with one initial member
  const mockTeam = createValidTeam({
    id: 'team-1',
    name: 'Test Team',
    memberIds: ['user-001']
  });
  
  const mockOnTeamUpdate = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component with initial member', () => {
    render(
      <LanguageProvider>
        <TeamMembersManager 
          team={mockTeam} 
          onTeamUpdate={mockOnTeamUpdate} 
        />
      </LanguageProvider>
    );

    // Check if the member list contains the initial member
    expect(screen.getByText("Alex Johnson")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
  });

  it('allows searching for team members', async () => {
    const teamWithMultipleMembers = {
      ...mockTeam,
      memberIds: ['user-001', 'user-002', 'user-003']
    };

    render(
      <LanguageProvider>
        <TeamMembersManager 
          team={teamWithMultipleMembers} 
          onTeamUpdate={mockOnTeamUpdate} 
        />
      </LanguageProvider>
    );

    // Initially all three members should be visible
    expect(screen.getByText("Alex Johnson")).toBeInTheDocument();
    expect(screen.getByText("Sam Williams")).toBeInTheDocument();
    expect(screen.getByText("Taylor Smith")).toBeInTheDocument();

    // Type in the search box to filter members
    const searchInput = screen.getByPlaceholderText("search_team_members");
    await userEvent.type(searchInput, "Sam");

    // Now only Sam should be visible
    expect(screen.getByText("Sam Williams")).toBeInTheDocument();
    expect(screen.queryByText("Alex Johnson")).not.toBeInTheDocument();
    expect(screen.queryByText("Taylor Smith")).not.toBeInTheDocument();
  });

  it('opens add member dialog when clicking add button', async () => {
    render(
      <LanguageProvider>
        <TeamMembersManager 
          team={mockTeam} 
          onTeamUpdate={mockOnTeamUpdate} 
        />
      </LanguageProvider>
    );

    // Click the add member button
    const addButton = screen.getByText("add_member");
    await userEvent.click(addButton);

    // Dialog should be open
    expect(screen.getByRole("dialog")).toBeInTheDocument();
    expect(screen.getAllByText("add_member")[0]).toBeInTheDocument(); // Dialog title should be visible
  });

  it('adds a new member to the team', async () => {
    render(
      <LanguageProvider>
        <TeamMembersManager 
          team={mockTeam} 
          onTeamUpdate={mockOnTeamUpdate} 
        />
      </LanguageProvider>
    );

    // Click the add member button to open dialog
    const addButton = screen.getByText("add_member");
    await userEvent.click(addButton);

    // Click on a member in the dialog to add them
    const memberToAdd = screen.getByText("Sam Williams");
    await userEvent.click(memberToAdd);

    // Check if onTeamUpdate was called with updated memberIds
    expect(mockOnTeamUpdate).toHaveBeenCalledWith(
      expect.objectContaining({
        memberIds: ['user-001', 'user-002']
      })
    );

    // Check if success toast was shown
    expect(toast.success).toHaveBeenCalled();
  });

  it('removes a member from the team', async () => {
    const teamWithMultipleMembers = {
      ...mockTeam,
      memberIds: ['user-001', 'user-002']
    };

    render(
      <LanguageProvider>
        <TeamMembersManager 
          team={teamWithMultipleMembers} 
          onTeamUpdate={mockOnTeamUpdate} 
        />
      </LanguageProvider>
    );

    // Find and click the remove button for the second member
    const removeButtons = screen.getAllByTitle("remove_member");
    await userEvent.click(removeButtons[1]); // Remove the second member

    // Check if onTeamUpdate was called with updated memberIds
    expect(mockOnTeamUpdate).toHaveBeenCalledWith(
      expect.objectContaining({
        memberIds: ['user-001']
      })
    );

    // Check if success toast was shown
    expect(toast.success).toHaveBeenCalled();
  });

  it('prevents removing the last member', async () => {
    render(
      <LanguageProvider>
        <TeamMembersManager 
          team={mockTeam} 
          onTeamUpdate={mockOnTeamUpdate} 
        />
      </LanguageProvider>
    );

    // Try to remove the only member
    const removeButton = screen.getByTitle("remove_member");
    await userEvent.click(removeButton);

    // Check if error toast was shown
    expect(toast.error).toHaveBeenCalled();
    
    // Check that onTeamUpdate was not called
    expect(mockOnTeamUpdate).not.toHaveBeenCalled();
  });

  it('filters available users in add member dialog', async () => {
    render(
      <LanguageProvider>
        <TeamMembersManager 
          team={mockTeam} 
          onTeamUpdate={mockOnTeamUpdate} 
        />
      </LanguageProvider>
    );

    // Open the add member dialog
    const addButton = screen.getByText("add_member");
    await userEvent.click(addButton);

    // Initially all available users (except the one already in the team) should be visible
    expect(screen.getByText("Sam Williams")).toBeInTheDocument();
    expect(screen.getByText("Taylor Smith")).toBeInTheDocument();
    expect(screen.getByText("Jordan Brown")).toBeInTheDocument();

    // Search for a specific user
    const searchInput = screen.getByPlaceholderText("search_members");
    await userEvent.type(searchInput, "Taylor");

    // Now only Taylor should be visible in the available users list
    expect(screen.getByText("Taylor Smith")).toBeInTheDocument();
    expect(screen.queryByText("Sam Williams")).not.toBeInTheDocument();
    expect(screen.queryByText("Jordan Brown")).not.toBeInTheDocument();
  });

  it('renders empty state when no members match search', async () => {
    render(
      <LanguageProvider>
        <TeamMembersManager 
          team={mockTeam} 
          onTeamUpdate={mockOnTeamUpdate} 
        />
      </LanguageProvider>
    );

    // Search for a non-existent member
    const searchInput = screen.getByPlaceholderText("search_team_members");
    await userEvent.type(searchInput, "NonExistent");

    // Empty state should be shown
    expect(screen.getByText("no_members_found")).toBeInTheDocument();
  });

  it('is properly rendered in read-only mode', () => {
    render(
      <LanguageProvider>
        <TeamMembersManager 
          team={mockTeam} 
          onTeamUpdate={mockOnTeamUpdate}
          readOnly={true}
        />
      </LanguageProvider>
    );

    // The member should still be visible
    expect(screen.getByText("Alex Johnson")).toBeInTheDocument();
    
    // But add button should not be present
    expect(screen.queryByText("add_member")).not.toBeInTheDocument();
    
    // And remove buttons should not be present
    expect(screen.queryByTitle("remove_member")).not.toBeInTheDocument();
  });
});
