
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemberSearchBar } from '@/components/teams/members/components/MemberSearchBar';
import { LanguageProvider } from '@/context/LanguageContext';

describe('MemberSearchBar', () => {
  const mockProps = {
    value: "",
    onChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with default placeholder', () => {
    render(
      <LanguageProvider>
        <MemberSearchBar {...mockProps} />
      </LanguageProvider>
    );

    // Check if the search input is rendered with the correct placeholder
    expect(screen.getByPlaceholderText("search_team_members")).toBeInTheDocument();
  });

  it('renders with custom placeholder', () => {
    render(
      <LanguageProvider>
        <MemberSearchBar {...mockProps} placeholder="search_members" />
      </LanguageProvider>
    );

    // Check if the search input is rendered with the custom placeholder
    expect(screen.getByPlaceholderText("search_members")).toBeInTheDocument();
  });

  it('calls onChange when input value changes', async () => {
    render(
      <LanguageProvider>
        <MemberSearchBar {...mockProps} />
      </LanguageProvider>
    );

    // Find the search input
    const searchInput = screen.getByPlaceholderText("search_team_members");
    
    // Type in the search box
    await userEvent.type(searchInput, "Alex");

    // Check if onChange was called with the updated value
    expect(mockProps.onChange).toHaveBeenCalledWith("Alex");
  });

  it('displays the current value', () => {
    render(
      <LanguageProvider>
        <MemberSearchBar {...mockProps} value="Taylor" />
      </LanguageProvider>
    );

    // Find the search input
    const searchInput = screen.getByPlaceholderText("search_team_members") as HTMLInputElement;
    
    // Check if the input displays the current value
    expect(searchInput.value).toBe("Taylor");
  });
});
