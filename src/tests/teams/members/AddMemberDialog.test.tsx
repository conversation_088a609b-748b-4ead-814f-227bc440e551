
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AddMemberDialog } from '@/components/teams/members/components/AddMemberDialog';
import { LanguageProvider } from '@/context/LanguageContext';

describe('AddMemberDialog', () => {
  const mockAvailableUsers = [
    { id: "user-001", name: "<PERSON>", email: "<EMAIL>" },
    { id: "user-002", name: "<PERSON>", email: "<EMAIL>" },
    { id: "user-003", name: "<PERSON>", email: "<EMAIL>" },
  ];

  const mockProps = {
    open: true,
    onOpenChange: vi.fn(),
    availableUsers: mockAvailableUsers,
    onAddMember: vi.fn(),
    searchTerm: "",
    onSearchChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the dialog with available users', () => {
    render(
      <LanguageProvider>
        <AddMemberDialog {...mockProps} />
      </LanguageProvider>
    );

    // Check dialog title
    expect(screen.getByText("add_member")).toBeInTheDocument();
    
    // Check if all users are rendered
    expect(screen.getByText("Alex Johnson")).toBeInTheDocument();
    expect(screen.getByText("Sam Williams")).toBeInTheDocument();
    expect(screen.getByText("Taylor Smith")).toBeInTheDocument();
    
    // Check their emails
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
  });

  it('calls onAddMember when a user is clicked', async () => {
    render(
      <LanguageProvider>
        <AddMemberDialog {...mockProps} />
      </LanguageProvider>
    );

    // Click on the first user
    await userEvent.click(screen.getByText("Alex Johnson"));

    // Check if onAddMember was called with the correct user id
    expect(mockProps.onAddMember).toHaveBeenCalledWith("user-001");
  });

  it('calls onAddMember when add button is clicked', async () => {
    render(
      <LanguageProvider>
        <AddMemberDialog {...mockProps} />
      </LanguageProvider>
    );

    // Get all add buttons
    const addButtons = screen.getAllByRole("button");
    
    // Click the add button for the second user
    // Find the button next to Sam Williams
    const samRow = screen.getByText("Sam Williams").closest('div')?.parentElement;
    const samAddButton = samRow?.querySelector('button');
    if (samAddButton) {
      await userEvent.click(samAddButton);
    }

    // Check if onAddMember was called with the correct user id
    expect(mockProps.onAddMember).toHaveBeenCalledWith("user-002");
  });

  it('calls onSearchChange when search input changes', async () => {
    render(
      <LanguageProvider>
        <AddMemberDialog {...mockProps} />
      </LanguageProvider>
    );

    // Find the search input
    const searchInput = screen.getByPlaceholderText("search_members");
    
    // Type in the search box
    await userEvent.type(searchInput, "Taylor");

    // Check if onSearchChange was called
    expect(mockProps.onSearchChange).toHaveBeenCalledWith("Taylor");
  });

  it('displays empty state when no users are available', () => {
    render(
      <LanguageProvider>
        <AddMemberDialog {...mockProps} availableUsers={[]} />
      </LanguageProvider>
    );

    // Check if empty state message is displayed
    expect(screen.getByText("no_members_found")).toBeInTheDocument();
  });

  it('closes when the close button is clicked', async () => {
    render(
      <LanguageProvider>
        <AddMemberDialog {...mockProps} />
      </LanguageProvider>
    );

    // Find and click the close button (usually an X icon)
    const closeButton = screen.getByRole("button", { name: "Close" });
    await userEvent.click(closeButton);

    // Check if onOpenChange was called with false
    expect(mockProps.onOpenChange).toHaveBeenCalledWith(false);
  });
});
