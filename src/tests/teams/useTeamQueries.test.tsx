
import { renderHook, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { useTeamQueries } from '@/hooks/teams/useTeamQueries';
import { useAuth } from '@/context/AuthContext';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Team } from '@/types';

// Mock the auth context
vi.mock('@/context/AuthContext', () => ({
  useAuth: vi.fn(),
}));

// Mock the language context
vi.mock('@/context/LanguageContext', () => ({
  useLanguage: () => ({
    t: (key: string) => key,
  }),
}));

const mockTeams: Team[] = [
  {
    id: 'team-001',
    name: 'Morning Rowers',
    memberIds: ['user-001', 'user-002', 'user-003', 'user-004'],
    coachIds: ['coach-001'],
    email: '<EMAIL>',
    practiceSchedules: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'team-002',
    name: 'Weekend Warriors',
    memberIds: ['user-001', 'user-005', 'user-006'],
    coachIds: ['coach-002'],
    email: '<EMAIL>',
    practiceSchedules: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'team-003',
    name: 'Competitive Crew',
    memberIds: ['user-007', 'user-008', 'user-009', 'user-010', 'user-011'],
    coachIds: ['user-001'],
    email: '<EMAIL>',
    practiceSchedules: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

// Mock the fetchTeams function to return our test data
vi.mock('@tanstack/react-query', async () => {
  const actual = await vi.importActual('@tanstack/react-query');
  return {
    ...actual,
    useQuery: ({ queryKey }: { queryKey: string[] }) => {
      if (queryKey[0] === 'teams') {
        return {
          data: mockTeams,
          isLoading: false,
          error: null,
        };
      }
      return { data: undefined, isLoading: false, error: null };
    },
  };
});

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useTeamQueries hook', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return all teams', async () => {
    // Mock user is logged in
    (useAuth as any).mockReturnValue({
      user: { id: 'user-001', name: 'Alex' },
      isAuthenticated: true,
    });

    const { result } = renderHook(() => useTeamQueries(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.teams).toHaveLength(3);
    });
  });

  it('should return teams where user is a member', async () => {
    // Mock user is logged in
    (useAuth as any).mockReturnValue({
      user: { id: 'user-001', name: 'Alex' },
      isAuthenticated: true,
    });

    const { result } = renderHook(() => useTeamQueries(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      const myTeams = result.current.getMyTeams();
      expect(myTeams).toHaveLength(2);
      expect(myTeams[0].id).toBe('team-001');
      expect(myTeams[1].id).toBe('team-002');
    });
  });

  it('should return teams where user is not a member', async () => {
    // Mock user is logged in
    (useAuth as any).mockReturnValue({
      user: { id: 'user-001', name: 'Alex' },
      isAuthenticated: true,
    });

    const { result } = renderHook(() => useTeamQueries(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      const otherTeams = result.current.getOtherTeams();
      expect(otherTeams).toHaveLength(1);
      expect(otherTeams[0].id).toBe('team-003');
    });
  });

  it('should return teams where user is a coach', async () => {
    // Mock user is logged in
    (useAuth as any).mockReturnValue({
      user: { id: 'user-001', name: 'Alex' },
      isAuthenticated: true,
    });

    const { result } = renderHook(() => useTeamQueries(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      const coachingTeams = result.current.getCoachingTeams();
      expect(coachingTeams).toHaveLength(1);
      expect(coachingTeams[0].id).toBe('team-003');
    });
  });

  it('should return empty arrays when user is not logged in', async () => {
    // Mock user is not logged in
    (useAuth as any).mockReturnValue({
      user: null,
      isAuthenticated: false,
    });

    const { result } = renderHook(() => useTeamQueries(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.getMyTeams()).toHaveLength(0);
      expect(result.current.getOtherTeams()).toHaveLength(0);
      expect(result.current.getCoachingTeams()).toHaveLength(0);
    });
  });
});
