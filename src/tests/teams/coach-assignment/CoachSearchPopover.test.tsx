
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CoachSearchPopover } from '@/components/teams/coach-assignment';
import { createValidUser } from '@/tests/utils/testUtils';
import { LanguageProvider } from '@/context/LanguageContext';

describe('CoachSearchPopover', () => {
  const mockCoaches = [
    createValidUser({
      id: 'coach-1',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'coach'
    }),
    createValidUser({
      id: 'coach-2',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'coach'
    })
  ];
  
  const mockAssignedCoaches = [];
  const mockAssignCoach = vi.fn();

  it('renders the assign coach button', () => {
    render(
      <LanguageProvider>
        <CoachSearchPopover 
          coaches={mockCoaches} 
          assignedCoaches={mockAssignedCoaches}
          onAssignCoach={mockAssignCoach} 
        />
      </LanguageProvider>
    );

    // Check if the assign coach button is displayed
    expect(screen.getByText('Assign Coach')).toBeInTheDocument();
  });

  it('opens the popover when button is clicked', async () => {
    render(
      <LanguageProvider>
        <CoachSearchPopover 
          coaches={mockCoaches} 
          assignedCoaches={mockAssignedCoaches}
          onAssignCoach={mockAssignCoach} 
        />
      </LanguageProvider>
    );

    // Click the assign coach button
    fireEvent.click(screen.getByText('Assign Coach'));

    // Check if the search input is displayed
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Search coaches')).toBeInTheDocument();
    });

    // Check if coach names are displayed in the popover
    expect(screen.getByText('Sarah Johnson')).toBeInTheDocument();
    expect(screen.getByText('Michael Chen')).toBeInTheDocument();
  });

  it('calls onAssignCoach when a coach is selected', async () => {
    render(
      <LanguageProvider>
        <CoachSearchPopover 
          coaches={mockCoaches} 
          assignedCoaches={mockAssignedCoaches}
          onAssignCoach={mockAssignCoach} 
        />
      </LanguageProvider>
    );

    // Click the assign coach button
    fireEvent.click(screen.getByText('Assign Coach'));

    // Wait for the popover to open
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Search coaches')).toBeInTheDocument();
    });

    // Click on a coach to select
    fireEvent.click(screen.getByText('Sarah Johnson'));

    // Check if onAssignCoach was called with the correct coach
    expect(mockAssignCoach).toHaveBeenCalledWith(mockCoaches[0]);
  });

  it('shows a check mark next to coaches that are already assigned', async () => {
    // Create a component with one coach already assigned
    render(
      <LanguageProvider>
        <CoachSearchPopover 
          coaches={mockCoaches} 
          assignedCoaches={[mockCoaches[0]]}
          onAssignCoach={mockAssignCoach} 
        />
      </LanguageProvider>
    );

    // Click the assign coach button
    fireEvent.click(screen.getByText('Assign Coach'));

    // Wait for the popover to open
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Search coaches')).toBeInTheDocument();
    });
  });
});
