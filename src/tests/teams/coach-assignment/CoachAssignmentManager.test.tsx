
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CoachAssignmentManager } from '@/components/teams/coach-assignment';
import { createValidTeam, createValidUser } from '@/tests/utils/testUtils';
import { LanguageProvider } from '@/context/LanguageContext';
import { toast } from 'sonner';

// Mock the toast function
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn()
  }
}));

// Mock the fetch coaches function
const mockCoaches = [
  createValidUser({
    id: 'coach-1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'coach'
  }),
  createValidUser({
    id: 'coach-2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'coach'
  })
];

// Mock the fetch coaches function
vi.mock('@/components/teams/coach-assignment/CoachAssignmentManager', async () => {
  const actual = await vi.importActual('@/components/teams/coach-assignment/CoachAssignmentManager');
  return {
    ...actual,
    fetchCoaches: vi.fn().mockResolvedValue(mockCoaches)
  };
});

describe('CoachAssignmentManager', () => {
  const mockTeam = createValidTeam({
    id: 'team-1',
    name: 'Test Team',
    coachIds: []
  });
  
  const mockOnUpdate = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component correctly', async () => {
    render(
      <LanguageProvider>
        <CoachAssignmentManager 
          team={mockTeam} 
          onUpdate={mockOnUpdate} 
        />
      </LanguageProvider>
    );

    // Check if the title is rendered
    expect(screen.getByText('Team Coaches')).toBeInTheDocument();
    
    // Check if the empty state is shown when no coaches are assigned
    await waitFor(() => {
      expect(screen.getByText('No coaches assigned')).toBeInTheDocument();
    });
  });

  it('shows a list of coaches when coaches are assigned', async () => {
    const teamWithCoaches = {
      ...mockTeam,
      coachIds: ['coach-1']
    };

    render(
      <LanguageProvider>
        <CoachAssignmentManager 
          team={teamWithCoaches} 
          onUpdate={mockOnUpdate} 
        />
      </LanguageProvider>
    );

    // Wait for coaches to load
    await waitFor(() => {
      expect(screen.getByText('Sarah Johnson')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });
  });

  it('does not render the assign coach button when in readOnly mode', () => {
    render(
      <LanguageProvider>
        <CoachAssignmentManager 
          team={mockTeam} 
          onUpdate={mockOnUpdate}
          readOnly={true}
        />
      </LanguageProvider>
    );

    // The "Assign Coach" button should not be present in readOnly mode
    expect(screen.queryByText('Assign Coach')).not.toBeInTheDocument();
  });
});
