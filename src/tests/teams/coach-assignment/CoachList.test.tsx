
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { CoachList } from '@/components/teams/coach-assignment';
import { createValidUser } from '@/tests/utils/testUtils';
import { LanguageProvider } from '@/context/LanguageContext';

describe('CoachList', () => {
  const mockCoaches = [
    createValidUser({
      id: 'coach-1',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'coach'
    }),
    createValidUser({
      id: 'coach-2',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'coach'
    })
  ];
  
  const mockRemoveCoach = vi.fn();

  it('renders a list of coaches', () => {
    render(
      <LanguageProvider>
        <CoachList 
          coaches={mockCoaches} 
          onRemoveCoach={mockRemoveCoach} 
        />
      </LanguageProvider>
    );

    // Check if coach names and emails are displayed
    expect(screen.getByText('<PERSON>')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Michael Chen')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('calls onRemoveCoach when remove button is clicked', () => {
    render(
      <LanguageProvider>
        <CoachList 
          coaches={mockCoaches} 
          onRemoveCoach={mockRemoveCoach} 
        />
      </LanguageProvider>
    );

    // Find and click the remove button for the first coach
    const removeButtons = screen.getAllByRole('button', { name: /remove coach/i });
    fireEvent.click(removeButtons[0]);

    // Check if onRemoveCoach was called with the correct coach ID
    expect(mockRemoveCoach).toHaveBeenCalledWith('coach-1');
  });

  it('does not show remove buttons in readOnly mode', () => {
    render(
      <LanguageProvider>
        <CoachList 
          coaches={mockCoaches} 
          onRemoveCoach={mockRemoveCoach} 
          readOnly={true} 
        />
      </LanguageProvider>
    );

    // Check if coach names and emails are displayed
    expect(screen.getByText('Sarah Johnson')).toBeInTheDocument();
    expect(screen.getByText('Michael Chen')).toBeInTheDocument();

    // Remove buttons should not be present in readOnly mode
    expect(screen.queryAllByRole('button', { name: /remove coach/i })).toHaveLength(0);
  });
});
