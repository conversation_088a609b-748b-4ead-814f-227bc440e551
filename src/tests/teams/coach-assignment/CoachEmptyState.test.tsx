
import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { CoachEmptyState } from '@/components/teams/coach-assignment';
import { LanguageProvider } from '@/context/LanguageContext';

describe('CoachEmptyState', () => {
  it('renders the no coaches message', () => {
    render(
      <LanguageProvider>
        <CoachEmptyState />
      </LanguageProvider>
    );

    // Check if the no coaches message is displayed
    expect(screen.getByText('No coaches assigned')).toBeInTheDocument();
  });
});
