
import { describe, it, expect, vi } from 'vitest';
import { mockUsers } from '@/services/mockData';
import { validateUserId } from '@/context/AuthContext';
import { useTeamQueries } from '@/hooks/teams/useTeamQueries';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Mock the auth context
vi.mock('@/context/AuthContext', () => ({
  useAuth: () => ({
    user: { id: 'user-001', name: 'Test User' },
    isAuthenticated: true,
  }),
  validateUserId: vi.fn().mockImplementation((id: string) => /^user-\d{3}$/.test(id)),
}));

// Mock the language context
vi.mock('@/context/LanguageContext', () => ({
  useLanguage: () => ({
    t: (key: string) => key,
  }),
}));

describe('Team Data Consistency', () => {
  it('all mockUsers have valid user IDs', () => {
    mockUsers.forEach(user => {
      expect(validateUserId(user.id)).toBe(true);
    });
  });

  it('all team memberIds use valid user ID format', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => {
      const queryClient = new QueryClient({
        defaultOptions: { queries: { retry: false } },
      });
      return (
        <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
      );
    };

    const { result } = renderHook(() => useTeamQueries(), { wrapper });

    await waitFor(() => {
      expect(result.current.teams.length).toBeGreaterThan(0);
    });

    result.current.teams.forEach(team => {
      team.memberIds.forEach(memberId => {
        const isValid = validateUserId(memberId);
        expect(isValid).toBe(true);
        if (!isValid) {
          console.error(`Team ${team.name} has invalid member ID: ${memberId}`);
        }
      });

      if (team.coachIds) {
        team.coachIds.forEach(coachId => {
          // Since coachIds can have a different format (see team-003 using 'usr-1' format),
          // we don't use validateUserId here but check for a general valid format
          expect(typeof coachId).toBe('string');
          expect(coachId.length).toBeGreaterThan(3);
        });
      }
    });
  });

  it('getMyTeams should find teams with current user ID', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => {
      const queryClient = new QueryClient({
        defaultOptions: { queries: { retry: false } },
      });
      return (
        <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
      );
    };

    const { result } = renderHook(() => useTeamQueries(), { wrapper });

    await waitFor(() => {
      const myTeams = result.current.getMyTeams();
      expect(myTeams.length).toBeGreaterThan(0);
      
      myTeams.forEach(team => {
        const includesUser = team.memberIds.includes('user-001');
        expect(includesUser).toBe(true);
        if (!includesUser) {
          console.error(`Team ${team.name} doesn't include the current user ID`);
        }
      });
    });
  });
});
