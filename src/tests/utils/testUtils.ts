
import { 
  Watercraft, 
  Boat, 
  User, 
  UserRole, 
  Jaunt, 
  MaintenanceRequest, 
  WatercraftType, 
  MaintenanceIssueType, 
  MaintenanceRequestStatus,
  Team
} from '@/types';

// Helper function to create a valid watercraft for testing
export function createValidWatercraft(overrides?: Partial<Watercraft>): Watercraft {
  return {
    id: overrides?.id || 'watercraft-test-123',
    name: 'Test Watercraft',
    type: 'kayak',
    ownershipType: 'club',
    location: 'Test Location',
    skillLevel: 1,
    status: 'available',
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  };
}

// Helper function to create a valid boat (extends watercraft) for testing
export function createValidBoat(overrides?: Partial<Boat>): Boat {
  return {
    id: overrides?.id || 'boat-test-123',
    name: 'Test Boat',
    type: 'boat',
    boatType: '2x',
    ownershipType: 'club',
    location: 'Test Location',
    skillLevel: 1,
    status: 'available',
    weightRange: {
      min: 70,
      max: 85
    },
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  };
}

// Helper function to create a valid user for testing
export function createValidUser(overrides?: Partial<User>): User {
  return {
    id: overrides?.id || 'user-test-123',
    name: 'Test User',
    email: '<EMAIL>',
    phone: '************',
    role: 'member' as UserRole,
    skills: ['Rowing'],
    permissions: [{ watercraftType: 'boat' as WatercraftType, skillLevel: 1 }],
    language: 'en',
    favorites: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  };
}

// Helper function to create a valid jaunt for testing
export function createValidJaunt(overrides?: Partial<Jaunt>): Jaunt {
  return {
    id: overrides?.id || 'jaunt-test-123',
    userId: 'user-test-123',
    watercraftId: 'watercraft-test-123',
    startTime: new Date('2023-07-15T10:00:00Z'),
    plannedEndTime: new Date('2023-07-15T12:00:00Z'),
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  };
}

// Helper function to create a valid maintenance request for testing
export function createValidMaintenanceRequest(overrides?: Partial<MaintenanceRequest>): MaintenanceRequest {
  return {
    id: overrides?.id || 'maintenance-test-123',
    watercraftId: 'watercraft-test-123',
    requestDate: new Date(),
    watercraftState: 'unuseable',
    issueType: 'broken' as MaintenanceIssueType,
    note: 'Test maintenance issue',
    status: 'open' as MaintenanceRequestStatus,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  };
}

// Helper function to create a valid team for testing
export function createValidTeam(overrides?: Partial<Team>): Team {
  return {
    id: overrides?.id || 'team-test-123',
    name: 'Test Team',
    memberIds: ['user-test-123'],
    coachIds: [],
    email: '<EMAIL>',
    practiceSchedules: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  };
}
