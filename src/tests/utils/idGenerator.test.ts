
import { describe, it, expect, vi } from 'vitest';
import { v7 as uuidv7 } from 'uuid';

// Mock the uuid v7 generator 
vi.mock('uuid', () => ({
  v7: vi.fn(() => 'mock-uuid-hash'),
}));

describe('ID Generator', () => {
  it('should generate IDs with the correct prefix', async () => {
    const { generateId, generateWatercraftId, generateMaintenanceId, generateJauntId, generateUserId, generateTeamId } = await import('@/utils/idGenerator');
    
    // Test the base generateId function
    expect(generateId('test')).toBe('test-mock-uuid-hash');
    
    // Test type-specific generators
    expect(generateWatercraftId()).toBe('wct-mock-uuid-hash');
    expect(generateMaintenanceId()).toBe('mnt-mock-uuid-hash');
    expect(generateJauntId()).toBe('jnt-mock-uuid-hash');
    expect(generateUserId()).toBe('usr-mock-uuid-hash');
    expect(generateTeamId()).toBe('tem-mock-uuid-hash');
    
    // Verify the UUIDv7 function was called
    expect(uuidv7).toHaveBeenCalled();
  });
  
  it('should create different IDs for different types', async () => {
    const { generateId } = await import('@/utils/idGenerator');
    
    const id1 = generateId('type1');
    const id2 = generateId('type2');
    
    expect(id1).not.toBe(id2);
    expect(id1).toBe('type1-mock-uuid-hash');
    expect(id2).toBe('type2-mock-uuid-hash');
  });
});
