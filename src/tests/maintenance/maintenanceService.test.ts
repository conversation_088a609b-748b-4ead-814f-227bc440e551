
import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { MaintenanceRequest } from '@/types';
import { createValidMaintenanceRequest } from '../utils/testUtils';

// Mock data we'll use to simulate our service
let mockMaintenanceRequests: MaintenanceRequest[] = [];
let mockWatercrafts: any[] = [];

// Mock the maintenance ID generator
vi.mock('@/utils/idGenerator', () => ({
  generateMaintenanceId: vi.fn(() => 'maintenance-test-123'),
}));

// Mock the imports for the service
vi.mock('@/services/watercraft/mock-data', () => ({
  mockMaintenanceRequests: {
    get: () => mockMaintenanceRequests,
    push: (item: any) => mockMaintenanceRequests.push(item),
    findIndex: (fn: any) => mockMaintenanceRequests.findIndex(fn),
    some: (fn: any) => mockMaintenanceRequests.some(fn),
    filter: (fn: any) => mockMaintenanceRequests.filter(fn),
  },
  mockWatercrafts: {
    get: () => mockWatercrafts,
    findIndex: (fn: any) => mockWatercrafts.findIndex(fn),
  },
}));

describe('Maintenance Service', () => {
  beforeEach(() => {
    // Reset mocks for clean tests
    mockMaintenanceRequests = [
      {
        id: 'maintenance-001',
        watercraftId: 'watercraft-001',
        requestDate: new Date('2023-06-15'),
        watercraftState: 'unuseable',
        issueType: 'broken',
        note: 'The seat slide mechanism is broken',
        status: 'in-progress',
        createdAt: new Date('2023-06-15'),
        updatedAt: new Date('2023-06-16'),
      },
      {
        id: 'maintenance-002',
        watercraftId: 'watercraft-002',
        requestDate: new Date('2023-06-20'),
        watercraftState: 'unuseable',
        issueType: 'damaged',
        note: 'Large crack in hull',
        status: 'open',
        createdAt: new Date('2023-06-20'),
        updatedAt: new Date('2023-06-20'),
      }
    ];

    mockWatercrafts = [
      {
        id: 'watercraft-001',
        name: 'Test Watercraft 1',
        type: 'boat',
        status: 'maintenance',
      },
      {
        id: 'watercraft-002',
        name: 'Test Watercraft 2',
        type: 'boat',
        status: 'available',
      }
    ];

    // Clear all mock call records
    vi.resetAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should add a new maintenance request', async () => {
    const { addMaintenanceRequest } = await import('@/services/watercraft/maintenance.service');
    
    const newRequest = createValidMaintenanceRequest({
      watercraftId: 'watercraft-002',
      watercraftState: 'unuseable',
      status: 'in-progress'
    });
    
    const result = await addMaintenanceRequest(newRequest);
    
    expect(result.id).toBe('maintenance-test-123');
    expect(result.watercraftId).toBe('watercraft-002');
    expect(result.status).toBe('in-progress');
    expect(result.createdAt).toBeInstanceOf(Date);
    expect(result.updatedAt).toBeInstanceOf(Date);
    
    // Should have updated the watercraft status
    const watercraftIndex = mockWatercrafts.findIndex(wc => wc.id === 'watercraft-002');
    if (watercraftIndex !== -1) {
      expect(mockWatercrafts[watercraftIndex].status).toBe('maintenance');
    }
  });

  it('should update an existing maintenance request and handle status changes', async () => {
    const { updateMaintenanceRequest } = await import('@/services/watercraft/maintenance.service');
    
    // Update from in-progress to closed (should change watercraft status)
    const result = await updateMaintenanceRequest('maintenance-001', {
      status: 'closed',
      resolution: 'Fixed the seat slide mechanism',
      resolutionDate: new Date()
    });
    
    expect(result).not.toBeNull();
    if (result) {
      expect(result.status).toBe('closed');
      expect(result.resolution).toBe('Fixed the seat slide mechanism');
      expect(result.resolutionDate).toBeInstanceOf(Date);
      expect(result.updatedAt).toBeInstanceOf(Date);
    }
    
    // Should have changed the watercraft back to available
    const watercraftIndex = mockWatercrafts.findIndex(wc => wc.id === 'watercraft-001');
    if (watercraftIndex !== -1) {
      expect(mockWatercrafts[watercraftIndex].status).toBe('available');
    }
  });

  it('should handle non-existent maintenance request updates correctly', async () => {
    const { updateMaintenanceRequest } = await import('@/services/watercraft/maintenance.service');
    
    const result = await updateMaintenanceRequest('non-existent-id', {
      status: 'closed'
    });
    
    expect(result).toBeNull();
  });

  it('should get maintenance count correctly', async () => {
    const { getActiveMaintenanceCount } = await import('@/services/watercraft/maintenance.service');
    
    const count = await getActiveMaintenanceCount('watercraft-001');
    
    // There's one in-progress request for watercraft-001
    expect(count).toBe(1);
  });

  it('should get maintenance requests by watercraft', async () => {
    const { getMaintenanceRequestsByWatercraft } = await import('@/services/watercraft/maintenance.service');
    
    const requests = await getMaintenanceRequestsByWatercraft('watercraft-001');
    
    expect(requests.length).toBe(1);
    expect(requests[0].id).toBe('maintenance-001');
  });
});
