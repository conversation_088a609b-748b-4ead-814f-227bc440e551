
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { useLanguage } from "@/context/LanguageContext";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import LoginInstructions from "@/components/LoginInstructions";
import { TwitterIcon, FacebookIcon, GithubIcon, Mail, Apple } from "lucide-react";
import { Separator } from "@/components/ui/separator";

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const { login, socialLogin } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      await login(email, password);
      navigate("/");
    } catch (error) {
      console.error("Login failed:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSocialLogin = async (provider: 'github' | 'facebook' | 'twitter' | 'google' | 'apple') => {
    setLoading(true);
    try {
      await socialLogin(provider);
      navigate("/");
    } catch (error) {
      console.error(`${provider} login failed:`, error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen grid md:grid-cols-2 bg-gray-50">
      <div className="hidden md:flex flex-col justify-center items-center bg-marine-600 text-white p-8">
        <div className="max-w-md">
          <h1 className="text-3xl font-bold mb-6">{t("app_title")}</h1>
          <p className="text-xl mb-4">{t("app_description")}</p>
          <p className="opacity-80">{t("login_sidebar_text")}</p>
        </div>
      </div>
      
      <div className="flex flex-col justify-center items-center p-4 sm:p-8">
        <div className="w-full max-w-md">
          <Tabs defaultValue="login" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="login">{t("login")}</TabsTrigger>
              <TabsTrigger value="demo-users">{t("demo_users")}</TabsTrigger>
            </TabsList>
            
            <TabsContent value="login">
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h2 className="text-2xl font-bold mb-6 text-center">{t("welcome_back")}</h2>
                
                <form onSubmit={handleLogin} className="space-y-4">
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium mb-1">
                      {t("email")}
                    </label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder={t("email_placeholder")}
                      required
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="password" className="block text-sm font-medium mb-1">
                      {t("password")}
                    </label>
                    <Input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder={t("password_placeholder")}
                      required
                    />
                  </div>
                  
                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={loading}
                  >
                    {loading ? t("logging_in") : t("login")}
                  </Button>
                </form>
                
                <div className="mt-6">
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <Separator className="w-full" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-white px-2 text-muted-foreground">
                        {t("or_continue_with")}
                      </span>
                    </div>
                  </div>
                  
                  <div className="mt-6 grid grid-cols-5 gap-2">
                    <Button 
                      variant="outline" 
                      type="button" 
                      onClick={() => handleSocialLogin('github')}
                      disabled={loading}
                      className="flex items-center justify-center"
                    >
                      <GithubIcon className="h-5 w-5" />
                    </Button>
                    <Button 
                      variant="outline" 
                      type="button" 
                      onClick={() => handleSocialLogin('facebook')}
                      disabled={loading}
                      className="flex items-center justify-center"
                    >
                      <FacebookIcon className="h-5 w-5" />
                    </Button>
                    <Button 
                      variant="outline" 
                      type="button" 
                      onClick={() => handleSocialLogin('twitter')}
                      disabled={loading}
                      className="flex items-center justify-center"
                    >
                      <TwitterIcon className="h-5 w-5" />
                    </Button>
                    <Button 
                      variant="outline" 
                      type="button" 
                      onClick={() => handleSocialLogin('google')}
                      disabled={loading}
                      className="flex items-center justify-center"
                    >
                      <Mail className="h-5 w-5" />
                    </Button>
                    <Button 
                      variant="outline" 
                      type="button" 
                      onClick={() => handleSocialLogin('apple')}
                      disabled={loading}
                      className="flex items-center justify-center"
                    >
                      <Apple className="h-5 w-5" />
                    </Button>
                  </div>
                </div>
                
                <div className="mt-4 text-center text-sm text-gray-500">
                  <p>{t("demo_mode_note")}</p>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="demo-users">
              <LoginInstructions />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Login;
