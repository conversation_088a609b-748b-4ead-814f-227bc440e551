
import { useState, useEffect } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Watercraft, Boat, MaintenanceRequest, MaintenanceIssueType } from '@/types';
import { Wrench } from 'lucide-react';
import { toast } from 'sonner';
import { MaintenanceRequestForm } from '@/components/maintenance/MaintenanceRequestForm';
import { MaintenanceTabContent } from '@/components/maintenance/MaintenanceTabContent';
import { 
  getAllMaintenanceRequests, 
  addMaintenanceRequest,
  getAllWatercrafts
} from '@/services/watercraft';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

const MaintenancePage = () => {
  const { t } = useLanguage();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const queryClient = useQueryClient();
  
  // Fetch maintenance requests
  const { 
    data: maintenanceRequests, 
    isLoading: isLoadingRequests,
    error: requestsError
  } = useQuery({
    queryKey: ['maintenanceRequests'],
    queryFn: getAllMaintenanceRequests
  });
  
  // Fetch watercrafts
  const { 
    data: watercraftsArray, 
    isLoading: isLoadingWatercrafts,
    error: watercraftsError
  } = useQuery({
    queryKey: ['watercrafts'],
    queryFn: getAllWatercrafts
  });
  
  // Convert watercrafts array to a record for easier lookup
  const watercrafts: Record<string, Watercraft | Boat> = 
    watercraftsArray?.reduce((acc, craft) => {
      acc[craft.id] = craft;
      return acc;
    }, {} as Record<string, Watercraft | Boat>) || {};
  
  // Add maintenance request mutation
  const addMaintenanceRequestMutation = useMutation({
    mutationFn: (requestData: Omit<MaintenanceRequest, 'id' | 'createdAt' | 'updatedAt'>) => 
      addMaintenanceRequest(requestData),
    onSuccess: () => {
      // Invalidate and refetch maintenance requests
      queryClient.invalidateQueries({ queryKey: ['maintenanceRequests'] });
      setIsDialogOpen(false);
      toast.success(t('maintenance_request_created'));
    },
    onError: (error) => {
      console.error('Error adding maintenance request:', error);
      toast.error(t('error_loading_maintenance'));
    }
  });
  
  // Handle errors
  useEffect(() => {
    if (requestsError) {
      console.error('Error fetching maintenance requests:', requestsError);
      toast.error(t('error_loading_maintenance'));
    }
    
    if (watercraftsError) {
      console.error('Error fetching watercrafts:', watercraftsError);
      toast.error(t('error_loading_maintenance'));
    }
  }, [requestsError, watercraftsError, t]);
  
  const handleSubmitRequest = (formData: {
    watercraftId: string;
    watercraftState: 'useable' | 'unuseable';
    issueType: MaintenanceIssueType;
    note: string;
  }) => {
    // Create new maintenance request
    const newRequest: Omit<MaintenanceRequest, 'id' | 'createdAt' | 'updatedAt'> = {
      watercraftId: formData.watercraftId,
      requestDate: new Date(),
      watercraftState: formData.watercraftState,
      issueType: formData.issueType,
      note: formData.note,
      status: 'open'
    };
    
    // Submit the request via mutation
    addMaintenanceRequestMutation.mutate(newRequest);
  };
  
  // Determine if we're loading
  const isLoading = isLoadingRequests || isLoadingWatercrafts;
  
  return (
    <main className="container mx-auto px-4 py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">{t('maintenance')}</h1>
          <p className="text-muted-foreground">{t('manage_maintenance_requests')}</p>
        </div>
        
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <Button onClick={() => setIsDialogOpen(true)}>
            <Wrench className="mr-2 h-4 w-4" />
            {t('new_maintenance_request')}
          </Button>
          
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>{t('new_maintenance_request')}</DialogTitle>
            </DialogHeader>
            
            <MaintenanceRequestForm 
              watercrafts={watercrafts}
              onSubmit={handleSubmitRequest}
              onCancel={() => setIsDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>
      
      <Tabs defaultValue="all">
        <TabsList className="mb-6">
          <TabsTrigger value="all">{t('all_requests')}</TabsTrigger>
          <TabsTrigger value="open">{t('open')}</TabsTrigger>
          <TabsTrigger value="in-progress">{t('in_progress')}</TabsTrigger>
          <TabsTrigger value="closed">{t('closed')}</TabsTrigger>
        </TabsList>
        
        {['all', 'open', 'in-progress', 'closed'].map((tabValue) => (
          <TabsContent key={tabValue} value={tabValue} className="mt-0">
            <MaintenanceTabContent 
              status={tabValue as 'all' | 'open' | 'in-progress' | 'closed'}
              loading={isLoading}
              maintenanceRequests={maintenanceRequests || []}
              watercrafts={watercrafts}
            />
          </TabsContent>
        ))}
      </Tabs>
    </main>
  );
};

export default MaintenancePage;
