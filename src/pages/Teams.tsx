
import { useState } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { useAuth } from '@/context/AuthContext';
import { useTeams } from '@/hooks/useTeams';
import { Team } from '@/types';
import { TeamDetailsManager } from '@/components/teams/TeamDetailsManager';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { useConfig } from '@/context/ConfigContext';
import { Button } from '@/components/ui/button';
import { CreateTeamDialog } from '@/components/teams/CreateTeamDialog';
import { Link } from 'react-router-dom';
import { UserPlus, PlusCircle, Users, CalendarCheck } from 'lucide-react';
import { TeamTabContent } from '@/components/teams/TeamTabContent';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const TeamsPage = () => {
  const { t } = useLanguage();
  const { user } = useAuth();
  const { getMyTeams, getOtherTeams, loading, createTeam } = useTeams();
  const { isFeatureEnabled } = useConfig();
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  
  // Check if team creation is enabled from config
  const teamCreationEnabled = isFeatureEnabled('enableTeamCreation');
  const memberSignUpEnabled = isFeatureEnabled('enableMemberSignUp');
  
  // Get the teams for the current user
  const myTeams = getMyTeams();
  const otherTeams = getOtherTeams();
  
  const handleCreateTeam = ({ name, email, practiceSheetUrl }: { 
    name: string; 
    email: string; 
    practiceSheetUrl: string;
  }) => {
    createTeam({
      name,
      email,
      practiceSheetUrl
    });
  };
  
  const handleTeamClick = (team: Team) => {
    setSelectedTeam(team);
    setDetailsDialogOpen(true);
  };
  
  const handleTeamUpdate = (updatedTeam: Team) => {
    toast.success(t('team_updated'));
    setSelectedTeam(updatedTeam);
  };
  
  return (
    <main className="container mx-auto px-4 py-6">
      <div className={`mb-6 ${teamCreationEnabled ? 'flex justify-between items-center' : ''}`}>
        <div>
          <h1 className="text-3xl font-bold">{t('teams')}</h1>
          <p className="text-muted-foreground">
            {teamCreationEnabled ? t('manage_your_rowing_teams') : t('view_teams')}
          </p>
        </div>
        
        <div className="flex gap-2">
          {memberSignUpEnabled && (
            <Button asChild variant="outline" className="gap-1">
              <Link to="/lineups">
                <CalendarCheck className="h-4 w-4 mr-1" />
                {t('lineups')}
              </Link>
            </Button>
          )}
          
          {teamCreationEnabled && (
            <Button onClick={() => setCreateDialogOpen(true)} className="gap-1">
              <UserPlus className="h-4 w-4" />
              {t('create_team')}
            </Button>
          )}
        </div>
      </div>
      
      <Tabs defaultValue="my-teams" className="mb-8">
        <TabsList className="mb-4">
          <TabsTrigger value="my-teams">
            <Users className="h-4 w-4 mr-2" />
            {t('my_teams')}
          </TabsTrigger>
          <TabsTrigger value="other-teams">
            {t('other_teams')}
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="my-teams">
          <TeamTabContent 
            teams={myTeams}
            loading={loading}
            onCreateTeamClick={() => setCreateDialogOpen(true)}
            readOnly={!teamCreationEnabled}
            onTeamClick={handleTeamClick}
          />
        </TabsContent>
        
        <TabsContent value="other-teams">
          <TeamTabContent 
            teams={otherTeams}
            loading={loading}
            onCreateTeamClick={() => setCreateDialogOpen(true)}
            readOnly={true}
            isOtherTeams={true}
            onTeamClick={handleTeamClick}
          />
        </TabsContent>
      </Tabs>
      
      {teamCreationEnabled && (
        <CreateTeamDialog 
          open={createDialogOpen}
          onOpenChange={setCreateDialogOpen}
          onCreateTeam={handleCreateTeam}
        />
      )}
      
      {selectedTeam && (
        <Dialog open={detailsDialogOpen} onOpenChange={setDetailsDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{selectedTeam.name}</DialogTitle>
            </DialogHeader>
            <div className="mt-4">
              <TeamDetailsManager 
                team={selectedTeam}
                onTeamUpdate={handleTeamUpdate}
                readOnly={!teamCreationEnabled || (user?.role !== 'coach' && user?.role !== 'admin')}
              />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </main>
  );
};

export default TeamsPage;
