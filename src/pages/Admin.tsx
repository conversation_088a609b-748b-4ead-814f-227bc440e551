
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { useLanguage } from "@/context/LanguageContext";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AdminUsersPanel } from "@/components/admin/AdminUsersPanel";
import { AdminWatercraftPanel } from "@/components/admin/AdminWatercraftPanel";
import { AdminTeamsPanel } from "@/components/admin/AdminTeamsPanel";
import { AdminStatsPanel } from "@/components/admin/AdminStatsPanel";
import { Shield } from "lucide-react";

const AdminPage = () => {
  const { user } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();

  // Redirect non-admin users
  useEffect(() => {
    if (user && user.role !== "admin") {
      navigate("/");
    }
  }, [user, navigate]);

  if (!user || user.role !== "admin") {
    return null; // Don't render anything if not admin
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Shield className="h-8 w-8 text-marine-600" />
        <h1 className="text-3xl font-bold">{t("admin_dashboard")}</h1>
      </div>
      
      <Card className="mb-8">
        <CardHeader className="pb-2">
          <CardTitle>{t("admin_overview")}</CardTitle>
          <CardDescription>{t("admin_overview_description")}</CardDescription>
        </CardHeader>
        <CardContent>
          <AdminStatsPanel />
        </CardContent>
      </Card>

      <Tabs defaultValue="users" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="users">{t("admin_users")}</TabsTrigger>
          <TabsTrigger value="watercraft">{t("admin_watercraft")}</TabsTrigger>
          <TabsTrigger value="teams">{t("admin_teams")}</TabsTrigger>
        </TabsList>
        <TabsContent value="users">
          <AdminUsersPanel />
        </TabsContent>
        <TabsContent value="watercraft">
          <AdminWatercraftPanel />
        </TabsContent>
        <TabsContent value="teams">
          <AdminTeamsPanel />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminPage;
