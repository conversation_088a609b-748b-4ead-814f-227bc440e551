
import { useAuth } from "@/context/AuthContext";
import { useLanguage } from "@/context/LanguageContext";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  User as UserIcon, 
  Shield,
  Bell,
  Cloud
} from "lucide-react";

// Import the component cards
import { ProfileSettingsCard } from "@/components/settings/ProfileSettingsCard";
import { SkillsPermissionsCard } from "@/components/settings/SkillsPermissionsCard";
import { AccountSecurityCard } from "@/components/settings/AccountSecurityCard";
import { NotificationsCard } from "@/components/settings/NotificationsCard";
import { WeatherSettingsCard } from "@/components/settings/WeatherSettingsCard";

export default function Settings() {
  const { user, loading } = useAuth();
  const { t } = useLanguage();

  if (loading || !user) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex justify-center items-center h-52">
          <p>{t("loading")}...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 max-w-5xl">
      <header className="mb-8">
        <h1 className="text-3xl font-bold">{t("settings_title")}</h1>
        <p className="text-muted-foreground">{t("settings_description")}</p>
      </header>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid grid-cols-4 max-w-md">
          <TabsTrigger value="profile">
            <UserIcon className="mr-2 h-4 w-4" />
            {t("settings_profile")}
          </TabsTrigger>
          <TabsTrigger value="account">
            <Shield className="mr-2 h-4 w-4" />
            {t("settings_account")}
          </TabsTrigger>
          <TabsTrigger value="notifications">
            <Bell className="mr-2 h-4 w-4" />
            {t("settings_notifications")}
          </TabsTrigger>
          <TabsTrigger value="weather">
            <Cloud className="mr-2 h-4 w-4" />
            {t("weather_title")}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <ProfileSettingsCard user={user} />
          <SkillsPermissionsCard user={user} />
        </TabsContent>

        <TabsContent value="account" className="space-y-6">
          <AccountSecurityCard />
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <NotificationsCard />
        </TabsContent>

        <TabsContent value="weather" className="space-y-6">
          <WeatherSettingsCard />
        </TabsContent>
      </Tabs>
    </div>
  );
}
