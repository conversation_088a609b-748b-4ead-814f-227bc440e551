
import { useAuth } from '@/context/AuthContext';
import { useConfig } from '@/context/ConfigContext';
import { useTeams } from '@/hooks/useTeams';
import { LineupsPageContent } from './lineups/components/LineupsPageContent';

export default function LineupsPage() {
  const { user } = useAuth();
  const { isFeatureEnabled } = useConfig();
  const { teams, getMyTeams, getCoachingTeams } = useTeams();
  
  const isCoach = user?.role === 'coach' || user?.role === 'admin';
  const isMemberSignUpEnabled = isFeatureEnabled('enableMemberSignUp');
  
  // Get teams that the user belongs to
  const userTeams = isCoach ? getCoachingTeams() : getMyTeams();
  
  return (
    <LineupsPageContent
      teams={teams}
      userTeams={userTeams}
      isCoach={isCoach}
      isMemberSignUpEnabled={isMemberSignUpEnabled}
    />
  );
}
