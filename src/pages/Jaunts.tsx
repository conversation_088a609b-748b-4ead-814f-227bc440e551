
import { useJaunts } from '@/hooks/useJaunts';
import { JauntList } from '@/components/jaunts/JauntList';
import { JauntPageHeader } from '@/components/jaunts/JauntPageHeader';
import { JauntErrorMessage } from '@/components/jaunts/JauntErrorMessage';
import { useLanguage } from '@/context/LanguageContext';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { HomeIcon } from "lucide-react";

const JauntsPage = () => {
  const { jaunts, watercrafts, isLoading, error } = useJaunts();
  const { t } = useLanguage();

  return (
    <main className="container mx-auto px-4 py-6">
      <Breadcrumb className="mb-4">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">
              <HomeIcon className="h-4 w-4 mr-1" />
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{t('jaunts_title')}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <JauntErrorMessage error={error} />
      <JauntPageHeader />
      <JauntList 
        jaunts={jaunts} 
        watercrafts={watercrafts} 
        loading={isLoading} 
      />
    </main>
  );
};

export default JauntsPage;
