
import React from "react";
import { useAuth } from "@/context/AuthContext";
import { useLanguage } from "@/context/LanguageContext";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { UserRole, SkillType, WatercraftPermission } from "@/types/user";
import { getAvatarFallback } from "@/utils/avatar";
import { Ship, Award, Phone, Mail, Calendar } from "lucide-react";

const ProfilePage: React.FC = () => {
  const { user } = useAuth();
  const { t } = useLanguage();

  if (!user) {
    return <div>{t("loading")}</div>;
  }

  const roleColors: Record<UserRole, string> = {
    member: "bg-blue-100 text-blue-800 border-blue-200",
    coach: "bg-green-100 text-green-800 border-green-200",
    admin: "bg-purple-100 text-purple-800 border-purple-200",
  };

  const skillColors: Record<SkillType, string> = {
    Rowing: "bg-indigo-100 text-indigo-800 border-indigo-200",
    Coaching: "bg-green-100 text-green-800 border-green-200",
    Maintenance: "bg-amber-100 text-amber-800 border-amber-200",
    Coxswain: "bg-teal-100 text-teal-800 border-teal-200",
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">{t("profile")}</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-1">
          <CardHeader className="flex flex-col items-center">
            <Avatar className="h-24 w-24">
              <AvatarImage src="" alt={user.name} />
              <AvatarFallback className="text-xl">{getAvatarFallback(user.name)}</AvatarFallback>
            </Avatar>
            <CardTitle className="mt-4 text-center">{user.name}</CardTitle>
            <div className="mt-2">
              <Badge className={roleColors[user.role]}>
                {t(user.role as any)}
              </Badge>
            </div>
            <CardDescription className="mt-2 text-center flex flex-col gap-2">
              <span className="flex items-center gap-1">
                <Mail className="h-4 w-4" />
                {user.email}
              </span>
              <span className="flex items-center gap-1">
                <Phone className="h-4 w-4" />
                {user.phone}
              </span>
              <span className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                {t("member_since")}: {formatDate(user.createdAt)}
              </span>
            </CardDescription>
          </CardHeader>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>{t("skills_and_certifications")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="font-medium mb-2 flex items-center gap-2">
                <Award className="h-5 w-5" />
                {t("skills")}
              </h3>
              <div className="flex flex-wrap gap-2">
                {user.skills.map((skill) => (
                  <Badge key={skill} className={skillColors[skill]}>
                    {skill}
                  </Badge>
                ))}
              </div>
            </div>

            <div>
              <h3 className="font-medium mb-2 flex items-center gap-2">
                <Ship className="h-5 w-5" />
                {t("watercraft_permissions")}
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {user.permissions.map((permission, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-3 border">
                    <div className="font-medium">{permission.watercraftType}</div>
                    <div className="text-sm text-gray-500">
                      {t("skill_level")}: {permission.skillLevel}/5
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ProfilePage;
