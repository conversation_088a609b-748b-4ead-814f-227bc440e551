
import { Team, PracticeInstance, PracticeSchedule } from '@/types';
import { generateId } from '@/utils/idGenerator';
import { addDays, format, isBefore, startOfDay } from 'date-fns';

// Map day names to date.getDay() values (0 = Sunday, 1 = Monday, etc.)
const dayMap: Record<string, number> = {
  sunday: 0,
  monday: 1,
  tuesday: 2,
  wednesday: 3,
  thursday: 4,
  friday: 5,
  saturday: 6
};

/**
 * Helper function to convert practice schedules to practice instances
 */
export const generatePracticeInstances = (team: Team): PracticeInstance[] => {
  if (!team.practiceSchedules || team.practiceSchedules.length === 0) {
    return [];
  }
  
  const instances: PracticeInstance[] = [];
  const today = startOfDay(new Date());
  // Generate practices for a full year (365 days) instead of just 120 days
  const endDate = addDays(today, 365);
  
  // Loop through each day in the range
  let currentDate = today;
  while (isBefore(currentDate, endDate)) {
    const dayOfWeek = currentDate.getDay();
    
    // Check if any practice schedules match this day
    team.practiceSchedules.forEach(schedule => {
      const scheduleDay = dayMap[schedule.day];
      
      if (dayOfWeek === scheduleDay) {
        // Create a practice instance for this day
        const practiceDate = new Date(currentDate);
        
        // Use existing instance if already created for this date and schedule
        const existingInstance = team.practiceInstances?.find(instance => 
          instance.scheduleId === schedule.id && 
          format(new Date(instance.date), 'yyyy-MM-dd') === format(practiceDate, 'yyyy-MM-dd')
        );
        
        if (existingInstance) {
          instances.push(existingInstance);
        } else {
          instances.push({
            id: generateId('practice'),
            teamId: team.id,
            scheduleId: schedule.id,
            date: practiceDate,
            startTime: schedule.startTime,
            endTime: schedule.endTime,
            location: schedule.location,
            notes: schedule.notes,
            attendees: [],
            boatAssignments: []
          });
        }
      }
    });
    
    currentDate = addDays(currentDate, 1);
  }
  
  return instances;
};
