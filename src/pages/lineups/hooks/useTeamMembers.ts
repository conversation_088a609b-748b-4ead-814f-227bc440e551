
import { Team } from '@/types';
import { mockAllUsers } from '@/components/teams/members/mockUsers';

/**
 * Helper to get team members from mock data
 */
export const useTeamMembers = () => {
  const getTeamMembers = (team: Team) => {
    return team.memberIds.map(id => {
      const user = mockAllUsers.find(user => user.id === id);
      return {
        id,
        name: user?.name || `Member ${id.slice(-3)}`,
        role: 'member'
      };
    });
  };

  return {
    getTeamMembers
  };
};
