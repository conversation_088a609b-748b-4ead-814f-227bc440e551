
import { useState, useEffect } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { Team, PracticeInstance } from '@/types';
import { PracticeSignUpManager } from '@/components/teams/practice-signup';
import { Heading, PageContainer } from '@/components/ui/card';
import { availableBoats } from '../data/availableBoats';
import { generatePracticeInstances } from '../utils/practiceInstanceGenerator';
import { useTeamMembers } from '../hooks/useTeamMembers';
import { TeamSelector } from './TeamSelector';

interface LineupsPageContentProps {
  teams: Team[];
  userTeams: Team[];
  isCoach: boolean;
  isMemberSignUpEnabled: boolean;
}

export const LineupsPageContent = ({
  teams,
  userTeams,
  isCoach,
  isMemberSignUpEnabled
}: LineupsPageContentProps) => {
  const { t } = useLanguage();
  const { getTeamMembers } = useTeamMembers();
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
  const [teamMembers, setTeamMembers] = useState<{ id: string; name: string; role: string }[]>([]);
  const [practiceInstances, setPracticeInstances] = useState<PracticeInstance[]>([]);
  
  // Initialize with the first team when teams data is available
  useEffect(() => {
    if (userTeams.length > 0 && !selectedTeam) {
      setSelectedTeam(userTeams[0]);
    }
  }, [userTeams, selectedTeam]);
  
  // Generate practice instances when selected team changes
  useEffect(() => {
    if (selectedTeam) {
      // Use existing practice instances or generate new ones
      const instances = selectedTeam.practiceInstances?.length 
        ? selectedTeam.practiceInstances 
        : generatePracticeInstances(selectedTeam);
      
      setPracticeInstances(instances);
      
      // Get team members
      const members = getTeamMembers(selectedTeam);
      setTeamMembers(members);
    }
  }, [selectedTeam, getTeamMembers]);

  const handleTeamSelect = (team: Team) => {
    setSelectedTeam(team);
  };
  
  if (!isMemberSignUpEnabled) {
    return (
      <PageContainer>
        <Heading className="mb-4">{t('lineups')}</Heading>
        <p className="text-muted-foreground">
          {t('feature_disabled')}
        </p>
      </PageContainer>
    );
  }
  
  return (
    <PageContainer>
      <Heading className="mb-4">{t('lineups')}</Heading>
      <p className="text-muted-foreground mb-6">
        {userTeams.length > 0 
          ? t('manage_your_rowing_teams') 
          : t('no_teams_yet')}
      </p>
      
      {userTeams.length > 0 && (
        <TeamSelector 
          teams={userTeams}
          selectedTeam={selectedTeam}
          onSelectTeam={handleTeamSelect}
        />
      )}
      
      {selectedTeam && (
        <PracticeSignUpManager 
          team={selectedTeam}
          practices={practiceInstances}
          availableBoats={availableBoats}
          teamMembers={teamMembers}
          onUpdatePractice={(updatedPractice) => {
            // Find the practice in the instances array
            const practiceIndex = practiceInstances.findIndex(
              (p) => p.id === updatedPractice.id
            );
            
            // Create a new array with the updated practice
            const updatedPractices = [...practiceInstances];
            if (practiceIndex >= 0) {
              updatedPractices[practiceIndex] = updatedPractice;
            } else {
              updatedPractices.push(updatedPractice);
            }
            
            // Update the state
            setPracticeInstances(updatedPractices);
            
            // Also update the team's practice instances
            if (selectedTeam) {
              setSelectedTeam({
                ...selectedTeam,
                practiceInstances: updatedPractices,
              });
            }
          }}
          initialTab={isCoach ? "lineup" : "sign-up"}
        />
      )}
    </PageContainer>
  );
};
