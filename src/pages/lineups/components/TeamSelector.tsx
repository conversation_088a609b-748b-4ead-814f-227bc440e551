
import { useState } from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { Team } from '@/types';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';

interface TeamSelectorProps {
  teams: Team[];
  selectedTeam: Team | null;
  onSelectTeam: (team: Team) => void;
}

export const TeamSelector = ({
  teams,
  selectedTeam,
  onSelectTeam,
}: TeamSelectorProps) => {
  const { t } = useLanguage();
  const [open, setOpen] = useState(false);

  if (teams.length === 0) {
    return null;
  }

  return (
    <div className="mb-6">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full md:w-[300px] justify-between"
          >
            {selectedTeam ? selectedTeam.name : t('select_team')}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full md:w-[300px] p-0">
          <Command>
            <CommandInput placeholder={t('search_teams')} />
            <CommandEmpty>{t('no_teams_found')}</CommandEmpty>
            <CommandGroup>
              {teams.map((team) => (
                <CommandItem
                  key={team.id}
                  value={team.name}
                  onSelect={() => {
                    onSelectTeam(team);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedTeam?.id === team.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {team.name}
                  <span className="ml-2 text-xs text-muted-foreground">
                    ({team.memberIds.length} {t('members')})
                  </span>
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
};
