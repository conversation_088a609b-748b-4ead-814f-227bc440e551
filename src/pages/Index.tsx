
import { useAuth } from '@/context/AuthContext';
import { JauntModal } from '@/components/watercraft/JauntModal';
import { BoatDetailsModal } from '@/components/watercraft/BoatDetailsModal';
import { CurrentJaunt } from '@/components/watercraft/CurrentJaunt';
import { WatercraftList } from '@/components/watercraft/WatercraftList';
import { WelcomeMessage } from '@/components/watercraft/WelcomeMessage';
import { useWatercraft } from '@/hooks/useWatercraft';

const Index = () => {
  const { user } = useAuth();
  const {
    filteredWatercrafts,
    currentJaunt,
    currentWatercraft,
    isJauntModalOpen,
    isBoatDetailsModalOpen,
    selectedWatercraft,
    filters,
    setFilters,
    handleToggleFavorite,
    handleCheckout,
    handleViewDetails,
    handleJauntSubmit,
    handleCheckin,
    handleReportIssue,
    setIsJauntModalOpen,
    setIsBoatDetailsModalOpen,
    isWatercraftFavorite
  } = useWatercraft();

  return (
    <div className="min-h-screen bg-background">
      {/* Welcome Message */}
      <WelcomeMessage user={user} />
      
      {/* Current Jaunt Section */}
      {currentJaunt && currentWatercraft && (
        <div className="mb-6">
          <CurrentJaunt
            jaunt={currentJaunt}
            watercraft={currentWatercraft}
            onCheckin={handleCheckin}
            onReportIssue={handleReportIssue}
          />
        </div>
      )}
      
      {/* Watercraft Listing Section */}
      <WatercraftList
        watercrafts={filteredWatercrafts}
        onFilterChange={setFilters}
        onToggleFavorite={handleToggleFavorite}
        onCheckout={handleCheckout}
        onViewDetails={handleViewDetails}
      />
      
      {/* Jaunt Modal */}
      <JauntModal
        isOpen={isJauntModalOpen}
        onClose={() => setIsJauntModalOpen(false)}
        watercraft={selectedWatercraft}
        onSubmit={handleJauntSubmit}
      />
      
      {/* Boat Details Modal */}
      <BoatDetailsModal
        isOpen={isBoatDetailsModalOpen}
        onClose={() => setIsBoatDetailsModalOpen(false)}
        watercraft={selectedWatercraft}
        onCheckout={handleCheckout}
        onToggleFavorite={handleToggleFavorite}
        isFavorite={selectedWatercraft ? isWatercraftFavorite(selectedWatercraft.id) : false}
      />
    </div>
  );
};

export default Index;
