
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Save, Shield } from 'lucide-react';
import { ConfigLogin, FeatureFlagsCard, SystemSettingsCard } from '@/components/config';
import { AppConfig } from '@/context/ConfigContext';

// The actual password - in a real app, this would be securely stored
const CONFIG_PASSWORD = "rowing-admin-2023";

// Default configuration options
const defaultConfig: AppConfig = {
  enableTeamCreation: false,
  enableAdvancedScheduling: false,
  enableWeatherAlerts: false,
  enableMaintenanceRequests: true,
  enableMemberSignUp: true, // Changed from false to true
  debugMode: false
};

const ConfigPage = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState('');
  const [config, setConfig] = useState(defaultConfig);

  // Load config from localStorage if available
  useEffect(() => {
    const savedConfig = localStorage.getItem('app-config');
    if (savedConfig) {
      try {
        setConfig({ ...defaultConfig, ...JSON.parse(savedConfig) });
      } catch (error) {
        console.error('Failed to parse saved config', error);
        localStorage.removeItem('app-config');
      }
    }
  }, []);

  const handleConfigChange = (key: keyof AppConfig) => {
    setConfig(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const saveConfig = () => {
    try {
      localStorage.setItem('app-config', JSON.stringify(config));
      toast.success('Configuration saved successfully');
    } catch (error) {
      console.error('Failed to save config', error);
      toast.error('Failed to save configuration');
    }
  };

  if (!isAuthenticated) {
    return (
      <ConfigLogin 
        onLogin={() => setIsAuthenticated(true)}
        password={password}
        setPassword={setPassword}
        configPassword={CONFIG_PASSWORD}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="container mx-auto max-w-4xl">
        <div className="flex items-center gap-2 mb-6">
          <Shield className="h-6 w-6 text-marine-600" />
          <h1 className="text-3xl font-bold">System Configuration</h1>
        </div>
        
        <Alert className="mb-8">
          <AlertDescription>
            Changes to these settings will affect the behavior of the application. Use with caution.
          </AlertDescription>
        </Alert>
        
        <FeatureFlagsCard config={config} onConfigChange={handleConfigChange} />
        
        <SystemSettingsCard config={config} onConfigChange={handleConfigChange} />
        
        <div className="flex justify-end">
          <Button onClick={saveConfig} className="gap-2">
            <Save className="h-4 w-4" />
            Save Configuration
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ConfigPage;
