
import { useQuery } from '@tanstack/react-query';
import { Team } from '@/types';
import { useAuth } from '@/context/AuthContext';
import { useLanguage } from '@/context/LanguageContext';
import { toast } from 'sonner';

// Mock API function - would be replaced with real API call in production
const fetchTeams = async (): Promise<Team[]> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Mock data
  return [
    {
      id: 'team-001',
      name: 'Morning Rowers',
      memberIds: ['user-001', 'user-002', 'user-003', 'user-004'],
      coachIds: ['usr-1', 'usr-3'],
      email: '<EMAIL>',
      whatsappGroupId: 'whatsapp-group-001',
      practiceSignupSheetUrl: 'https://example.com/sheets/morning-practice',
      practiceSchedules: [
        {
          id: 'psc-001',
          day: 'monday',
          startTime: '06:30',
          endTime: '08:00',
          location: 'Main Dock'
        },
        {
          id: 'psc-002',
          day: 'wednesday',
          startTime: '06:30',
          endTime: '08:00',
          location: 'Main Dock'
        },
        {
          id: 'psc-003',
          day: 'friday',
          startTime: '06:30',
          endTime: '08:00',
          location: 'Main Dock'
        }
      ],
      createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
    },
    {
      id: 'team-002',
      name: 'Weekend Warriors',
      memberIds: ['user-001', 'user-005', 'user-006'],
      coachIds: ['usr-2'],
      email: '<EMAIL>',
      practiceSignupSheetUrl: 'https://example.com/sheets/weekend-practice',
      practiceSchedules: [
        {
          id: 'psc-004',
          day: 'saturday',
          startTime: '08:00',
          endTime: '10:00',
          location: 'South Dock'
        },
        {
          id: 'psc-005',
          day: 'sunday',
          startTime: '08:00',
          endTime: '10:00',
          location: 'South Dock'
        }
      ],
      createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
    },
    {
      id: 'team-003',
      name: 'Competitive Crew',
      memberIds: ['user-007', 'user-008', 'user-009', 'user-010', 'user-011'],
      coachIds: ['usr-1', 'usr-2'],
      email: '<EMAIL>',
      whatsappGroupId: 'whatsapp-group-002',
      practiceSchedules: [
        {
          id: 'psc-006',
          day: 'monday',
          startTime: '16:00',
          endTime: '18:00',
          location: 'Competition Dock'
        },
        {
          id: 'psc-007',
          day: 'tuesday',
          startTime: '16:00',
          endTime: '18:00',
          location: 'Competition Dock'
        },
        {
          id: 'psc-008',
          day: 'thursday',
          startTime: '16:00',
          endTime: '18:00',
          location: 'Competition Dock'
        }
      ],
      createdAt: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
    }
  ];
};

// Validate the consistency of user IDs in teams
const validateTeamUserIds = (teams: Team[]): void => {
  teams.forEach(team => {
    // Check for any invalid member IDs
    const invalidMemberIds = team.memberIds.filter(id => !/^user-\d{3}$/.test(id));
    
    if (invalidMemberIds.length > 0) {
      console.error(`Team ${team.name} contains invalid member IDs:`, invalidMemberIds);
    }
  });
};

/**
 * Hook for fetching team data and providing filtered team lists
 */
export const useTeamQueries = () => {
  const { t } = useLanguage();
  const { user } = useAuth();
  
  // Fetch all teams using React Query
  const { data: teams = [], isLoading, error } = useQuery({
    queryKey: ['teams'],
    queryFn: fetchTeams,
    meta: {
      onError: () => {
        toast.error(t('error_loading_teams'));
      }
    },
    select: (data) => {
      // Validate team user IDs when data is received
      validateTeamUserIds(data);
      return data;
    }
  });
  
  // Filter teams for current user
  const getMyTeams = () => {
    if (!user) return [];
    
    // For debugging purposes
    console.log('Current user ID:', user.id);
    console.log('Available teams:', teams.map(team => ({
      name: team.name,
      memberIds: team.memberIds
    })));
    
    // Filter teams where the user is a member
    return teams.filter(team => {
      const isMember = team.memberIds.includes(user.id);
      console.log(`Team ${team.name}: User is member? ${isMember}`);
      return isMember;
    });
  };
  
  // Get teams the user is not a member of
  const getOtherTeams = () => {
    if (!user) return [];
    return teams.filter(team => !team.memberIds.includes(user.id));
  };
  
  // Get teams where the user is a coach
  const getCoachingTeams = () => {
    if (!user) return [];
    return teams.filter(team => team.coachIds?.includes(user.id));
  };

  return {
    teams,
    loading: isLoading,
    error,
    getMyTeams,
    getOtherTeams,
    getCoachingTeams
  };
};
