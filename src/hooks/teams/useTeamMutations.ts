
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Team } from '@/types';
import { useAuth } from '@/context/AuthContext';
import { useLanguage } from '@/context/LanguageContext';
import { toast } from 'sonner';
import { generateTeamId } from '@/utils/idGenerator';

// Mock API functions for mutations
const createTeamApi = async (teamData: { 
  name: string; 
  email: string; 
  practiceSheetUrl?: string;
  userId: string;
}): Promise<Team> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  return {
    id: generateTeamId(),
    name: teamData.name,
    memberIds: [teamData.userId], // Add current user
    email: teamData.email,
    practiceSignupSheetUrl: teamData.practiceSheetUrl || undefined,
    practiceSchedules: [],
    createdAt: new Date(),
    updatedAt: new Date()
  };
};

const updateTeamApi = async (team: Team): Promise<Team> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  return {
    ...team,
    updatedAt: new Date()
  };
};

/**
 * Hook for team creation and update mutations
 */
export const useTeamMutations = () => {
  const { t } = useLanguage();
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  // Create new team mutation
  const createTeamMutation = useMutation({
    mutationFn: (teamData: { name: string; email: string; practiceSheetUrl?: string }) => 
      createTeamApi({ ...teamData, userId: user?.id || 'user-001' }),
    onSuccess: (newTeam) => {
      // Invalidate and refetch teams query
      queryClient.setQueryData(['teams'], (oldTeams: Team[] = []) => [...oldTeams, newTeam]);
      toast.success(t('team_created_successfully'));
    },
    onError: () => {
      toast.error(t('error_loading_teams')); 
    }
  });
  
  // Update team mutation
  const updateTeamMutation = useMutation({
    mutationFn: updateTeamApi,
    onSuccess: (updatedTeam) => {
      // Update the teams query data
      queryClient.setQueryData(['teams'], (oldTeams: Team[] = []) => 
        oldTeams.map(team => team.id === updatedTeam.id ? updatedTeam : team)
      );
      toast.success(t('team_updated_successfully'));
    },
    onError: () => {
      toast.error(t('error_updating_team'));
    }
  });

  const createTeam = (teamData: { 
    name: string; 
    email: string; 
    practiceSheetUrl?: string 
  }) => {
    return createTeamMutation.mutate(teamData);
  };
  
  const updateTeam = (team: Team) => {
    return updateTeamMutation.mutate(team);
  };

  return {
    createTeam,
    updateTeam,
    isCreating: createTeamMutation.isPending,
    isUpdating: updateTeamMutation.isPending
  };
};
