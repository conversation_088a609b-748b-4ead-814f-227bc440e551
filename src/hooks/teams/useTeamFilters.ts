
import { Team } from '@/types';

/**
 * Hook for filtering team data in different ways
 */
export const useTeamFilters = (teams: Team[]) => {
  // Filter teams by name
  const filterTeamsByName = (name: string) => {
    if (!name) return teams;
    const searchTerm = name.toLowerCase();
    return teams.filter(team => team.name.toLowerCase().includes(searchTerm));
  };

  // Filter teams by member count (more than specified count)
  const filterTeamsByMemberCount = (minCount: number) => {
    return teams.filter(team => team.memberIds.length >= minCount);
  };

  // Filter teams with coaches
  const filterTeamsWithCoaches = () => {
    return teams.filter(team => team.coachIds && team.coachIds.length > 0);
  };

  // Filter teams with practice schedules
  const filterTeamsWithPracticeSchedules = () => {
    return teams.filter(team => 
      team.practiceSchedules && team.practiceSchedules.length > 0
    );
  };

  return {
    filterTeamsByName,
    filterTeamsByMemberCount,
    filterTeamsWithCoaches,
    filterTeamsWithPracticeSchedules
  };
};
