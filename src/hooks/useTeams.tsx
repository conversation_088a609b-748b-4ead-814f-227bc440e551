
import { useTeamQueries, useTeamMutations } from './teams';

/**
 * Main hook that combines all team-related functionality
 */
export const useTeams = () => {
  const { 
    teams, 
    loading, 
    error, 
    getMyTeams, 
    getOtherTeams,
    getCoachingTeams 
  } = useTeamQueries();
  
  const { 
    createTeam, 
    updateTeam, 
    isCreating, 
    isUpdating 
  } = useTeamMutations();

  return {
    // Data and loading states
    teams,
    loading,
    error,
    
    // Team filtering methods
    getMyTeams,
    getOtherTeams,
    getCoachingTeams,
    
    // Team mutation methods
    createTeam,
    updateTeam,
    isCreating,
    isUpdating
  };
};
