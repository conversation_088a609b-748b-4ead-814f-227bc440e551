
import { useState, useCallback, useMemo } from 'react';
import { Watercraft, Boat, WatercraftFilters } from '@/types';
import { WatercraftFiltersReturn } from './types';

export const useWatercraftFilters = (watercrafts: (Watercraft | Boat)[]): WatercraftFiltersReturn => {
  const [filters, setFilters] = useState<WatercraftFilters>({
    search: '',
    type: [],
    status: [],
    showFavoritesOnly: false,
    skillLevel: [1, 3],
    weightRange: [50, 100]
  });

  const filteredWatercrafts = useMemo(() => {
    return watercrafts.filter((watercraft) => {
      // Search filter
      if (
        filters.search &&
        !watercraft.name.toLowerCase().includes(filters.search.toLowerCase()) &&
        !watercraft.type.toLowerCase().includes(filters.search.toLowerCase())
      ) {
        return false;
      }

      // Type filter
      if (filters.type.length > 0 && !filters.type.includes(watercraft.type)) {
        return false;
      }

      // Boat type filter (for boats only)
      if (
        watercraft.type === 'boat' && 
        filters.boatTypes && 
        filters.boatTypes.length > 0 && 
        !filters.boatTypes.includes((watercraft as Boat).boatType)
      ) {
        return false;
      }

      // Status filter
      if (filters.status.length > 0 && !filters.status.includes(watercraft.status)) {
        return false;
      }

      // Skill level filter
      if (
        filters.skillLevel &&
        (watercraft.skillLevel < filters.skillLevel[0] || 
         watercraft.skillLevel > filters.skillLevel[1])
      ) {
        return false;
      }

      // Weight range filter (for boats only)
      if (
        watercraft.type === 'boat' &&
        filters.weightRange &&
        (watercraft as Boat).weightRange &&
        ((watercraft as Boat).weightRange!.min < filters.weightRange[0] ||
         (watercraft as Boat).weightRange!.max > filters.weightRange[1])
      ) {
        return false;
      }

      // Favorites filter
      if (filters.showFavoritesOnly && !watercraft.isFavorite) {
        return false;
      }

      return true;
    });
  }, [watercrafts, filters]);

  return {
    filters,
    setFilters,
    filteredWatercrafts,
  };
};
