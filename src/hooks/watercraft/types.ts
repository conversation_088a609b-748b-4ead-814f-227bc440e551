
import { Watercraft, Boat, Jaunt } from '@/types';

export interface WatercraftFilters {
  search: string;
  type: string[];
  status: string[];
  showFavoritesOnly: boolean;
}

export interface UseWatercraftReturn {
  allWatercrafts: (Watercraft | Boat)[];
  filteredWatercrafts: (Watercraft | Boat)[];
  currentJaunt: Jaunt | null;
  currentWatercraft: Watercraft | Boat | null;
  isJauntModalOpen: boolean;
  isBoatDetailsModalOpen: boolean;
  selectedWatercraft: Watercraft | Boat | null;
  filters: WatercraftFilters;
  setFilters: (filters: WatercraftFilters) => void;
  handleToggleFavorite: (watercraftId: string) => void;
  handleCheckout: (watercraft: Watercraft | Boat) => void;
  handleViewDetails: (watercraft: Watercraft | Boat) => void;
  handleJauntSubmit: (data: { startTime: Date; plannedEndTime: Date; comment?: string }) => void;
  handleCheckin: (jaunt: Jaunt) => void;
  handleReportIssue: (jaunt: Jaunt) => void;
  setIsJauntModalOpen: (isOpen: boolean) => void;
  setIsBoatDetailsModalOpen: (isOpen: boolean) => void;
  isWatercraftFavorite: (watercraftId: string) => boolean;
}

export interface JauntManagementReturn {
  currentJaunt: Jaunt | null;
  currentWatercraft: Watercraft | Boat | null;
  handleJauntSubmit: (data: { startTime: Date; plannedEndTime: Date; comment?: string }) => void;
  handleCheckin: (jaunt: Jaunt) => void;
  handleReportIssue: (jaunt: Jaunt) => void;
}

export interface WatercraftFiltersReturn {
  filters: WatercraftFilters;
  setFilters: (filters: WatercraftFilters) => void;
  filteredWatercrafts: (Watercraft | Boat)[];
}

export interface FavoritesManagementReturn {
  handleToggleFavorite: (watercraftId: string) => void;
  isWatercraftFavorite: (watercraftId: string) => boolean;
}
