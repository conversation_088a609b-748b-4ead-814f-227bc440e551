
import { User } from '@/types';
import { useAuth } from '@/context/AuthContext';
import { useLanguage } from '@/context/LanguageContext';
import { toast } from 'sonner';

export function useFavoritesManagement(user: User | null) {
  const { t } = useLanguage();
  
  // Handle toggling watercraft as favorite
  const handleToggleFavorite = (watercraftId: string) => {
    if (!user) {
      toast.error(t('must_login_to_favorite'));
      return;
    }
    
    // In a real app, this would call an API
    const isFavorite = user.favorites.includes(watercraftId);
    
    let updatedFavorites = [];
    if (isFavorite) {
      updatedFavorites = user.favorites.filter(id => id !== watercraftId);
      toast.success(t('removed_from_favorites'));
    } else {
      updatedFavorites = [...user.favorites, watercraftId];
      toast.success(t('added_to_favorites'));
    }
    
    // Update user in localStorage
    const updatedUser = { ...user, favorites: updatedFavorites };
    localStorage.setItem('user', JSON.stringify(updatedUser));
  };

  // Check if a watercraft is in user's favorites
  const isWatercraftFavorite = (watercraftId: string): boolean => {
    if (!user) {
      return false;
    }
    return user.favorites.includes(watercraftId);
  };

  return { 
    handleToggleFavorite,
    isWatercraftFavorite
  };
}
