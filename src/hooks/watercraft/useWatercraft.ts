
import { useState } from 'react';
import { Watercraft, Boat, Jaunt, WatercraftFilters } from '@/types';
import { useAuth } from '@/context/AuthContext';
import { mockWatercrafts } from '@/services/mockData/watercrafts';
import { mockJaunts } from '@/services/mockData/jaunts';
import { useWatercraftFilters } from './useWatercraftFilters';
import { useFavoritesManagement } from './useFavoritesManagement';
import { useJauntManagement } from './useJauntManagement';
import { UseWatercraftReturn } from './types';

export function useWatercraft(): UseWatercraftReturn {
  const { user } = useAuth();
  const [isJauntModalOpen, setIsJauntModalOpen] = useState(false);
  const [isBoatDetailsModalOpen, setIsBoatDetailsModalOpen] = useState(false);
  const [selectedWatercraft, setSelectedWatercraft] = useState<Watercraft | Boat | null>(null);
  
  // Get all watercrafts
  const allWatercrafts = mockWatercrafts;
  
  // Add favorite flag to watercrafts based on user's favorites
  const watercraftsWithFavorites = allWatercrafts.map(craft => ({
    ...craft,
    isFavorite: user?.favorites.includes(craft.id) || false
  }));
  
  // Use the filtering hook
  const { filters, setFilters, filteredWatercrafts } = useWatercraftFilters(watercraftsWithFavorites);
  
  // Use the favorites management hook
  const { handleToggleFavorite, isWatercraftFavorite } = useFavoritesManagement(user);
  
  // Use the jaunt management hook
  const { 
    currentJaunt, 
    currentWatercraft, 
    handleJauntSubmit, 
    handleCheckin, 
    handleReportIssue 
  } = useJauntManagement(user);
  
  // Handler for checkout action
  const handleCheckout = (watercraft: Watercraft | Boat) => {
    if (watercraft.status !== 'available') {
      return;
    }
    
    setSelectedWatercraft(watercraft);
    setIsJauntModalOpen(true);
  };
  
  // Handler for view details action
  const handleViewDetails = (watercraft: Watercraft | Boat) => {
    setSelectedWatercraft(watercraft);
    setIsBoatDetailsModalOpen(true);
  };
  
  return {
    allWatercrafts,
    filteredWatercrafts,
    currentJaunt,
    currentWatercraft,
    isJauntModalOpen,
    isBoatDetailsModalOpen,
    selectedWatercraft,
    filters,
    setFilters,
    handleToggleFavorite,
    handleCheckout,
    handleViewDetails,
    handleJauntSubmit,
    handleCheckin,
    handleReportIssue,
    setIsJauntModalOpen,
    setIsBoatDetailsModalOpen,
    isWatercraftFavorite
  };
}
