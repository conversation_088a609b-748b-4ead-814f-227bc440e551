
import { useState, useEffect } from 'react';
import { Watercraft, Boat, Jaunt, User } from '@/types';
import { useLanguage } from '@/context/LanguageContext';
import { toast } from 'sonner';
import { generateId } from '@/utils/idGenerator';
import { mockJaunts } from '@/services/mockData/jaunts';

export function useJauntManagement(user: User | null) {
  const { t } = useLanguage();
  const [currentJaunt, setCurrentJaunt] = useState<Jaunt | null>(null);
  const [currentWatercraft, setCurrentWatercraft] = useState<Watercraft | Boat | null>(null);

  // Check for current jaunt on component mount
  useEffect(() => {
    if (user && mockJaunts.length > 0) {
      const userJaunt = mockJaunts.find(
        jaunt => jaunt.userId === user.id && !jaunt.actualEndTime
      );
      
      if (userJaunt) {
        setCurrentJaunt(userJaunt);
      }
    }
  }, [user]);
  
  // Handle jaunt submission
  const handleJauntSubmit = (data: {
    startTime: Date;
    plannedEndTime: Date;
    comment?: string;
  }) => {
    // Implementation will be handled by parent component that has access to selectedWatercraft
    toast.success(t('checkout_success'));
  };
  
  // Handle checking in (ending a jaunt)
  const handleCheckin = (jaunt: Jaunt) => {
    toast.success(t('checkin_success'));
  };
  
  // Handle reporting an issue with a watercraft
  const handleReportIssue = (jaunt: Jaunt) => {
    toast.info(t('issue_report_feature_coming_soon'));
    // This would open a maintenance request modal in a complete implementation
  };

  return {
    currentJaunt,
    currentWatercraft,
    handleJauntSubmit,
    handleCheckin,
    handleReportIssue
  };
}
