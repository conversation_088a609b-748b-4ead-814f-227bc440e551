
import { useQuery } from '@tanstack/react-query';
import { Jaunt, Watercraft, Boat } from '@/types';
import { useAuth } from '@/context/AuthContext';

// This would be replaced with actual API calls in a real application
const fetchJaunts = async (userId: string): Promise<Jaunt[]> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Mock data
  return [
    {
      id: 'jaunt-001',
      userId: userId,
      watercraftId: 'craft-001',
      startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      plannedEndTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000),
      actualEndTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000 + 1.5 * 60 * 60 * 1000),
      distanceTraveled: 5.2,
      comments: 'Great weather, smooth rowing',
    },
    {
      id: 'jaunt-002',
      userId: userId,
      watercraftId: 'craft-003',
      startTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      plannedEndTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 1 * 60 * 60 * 1000),
      actualEndTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 1.2 * 60 * 60 * 1000),
      distanceTraveled: 3.7,
      comments: 'A bit windy',
    },
    {
      id: 'jaunt-003',
      userId: userId,
      watercraftId: 'craft-002',
      startTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // Yesterday
      plannedEndTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 3 * 60 * 60 * 1000),
      actualEndTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 2.5 * 60 * 60 * 1000),
      distanceTraveled: 8.1,
      comments: 'Beautiful sunset row',
    }
  ];
};

const fetchWatercrafts = async (): Promise<Record<string, Watercraft | Boat>> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Mock data
  return {
    'craft-001': {
      id: 'craft-001',
      name: 'Speedy',
      type: 'boat',
      boatType: '1x',
      ownershipType: 'club',
      location: 'Dock A',
      skillLevel: 3,
      status: 'available'
    } as Boat,
    'craft-002': {
      id: 'craft-002',
      name: 'Wave Rider',
      type: 'kayak',
      ownershipType: 'club',
      location: 'Dock B',
      skillLevel: 2,
      status: 'available'
    } as Watercraft,
    'craft-003': {
      id: 'craft-003',
      name: 'Blue Streak',
      type: 'surfski',
      ownershipType: 'club',
      location: 'Rack 3',
      skillLevel: 4,
      status: 'available'
    } as Watercraft
  };
};

export const useJaunts = () => {
  const { user } = useAuth();
  const userId = user?.id || 'user-001';
  
  const { data: jaunts = [], isLoading: isJauntsLoading, error: jauntsError } = useQuery({
    queryKey: ['jaunts', userId],
    queryFn: () => fetchJaunts(userId),
  });
  
  const { data: watercrafts = {}, isLoading: isWatercraftsLoading } = useQuery({
    queryKey: ['watercrafts-for-jaunts'],
    queryFn: fetchWatercrafts,
  });
  
  return {
    jaunts,
    watercrafts,
    isLoading: isJauntsLoading || isWatercraftsLoading,
    error: jauntsError
  };
};
