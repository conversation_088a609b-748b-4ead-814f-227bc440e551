
/**
 * Generates a fallback string for an avatar based on the user's name
 * Takes the first letter of the first and last name if available
 */
export const getAvatarFallback = (name: string): string => {
  if (!name) return "?";
  
  const nameParts = name.split(" ");
  
  if (nameParts.length === 1) {
    return nameParts[0].charAt(0).toUpperCase();
  }
  
  return `${nameParts[0].charAt(0)}${nameParts[nameParts.length - 1].charAt(0)}`.toUpperCase();
};
