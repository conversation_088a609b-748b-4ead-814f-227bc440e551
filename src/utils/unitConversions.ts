
// Helper functions to convert between unit systems
export const convertToFahrenheit = (celsius: number) => Math.round(celsius * 9/5 + 32);
export const convertToCelsius = (fahrenheit: number) => Math.round((fahrenheit - 32) * 5/9);
export const convertToMph = (kmh: number) => Math.round(kmh * 0.621371);
export const convertToKmh = (mph: number) => Math.round(mph / 0.621371);
export const convertToInches = (cm: number) => Math.round(cm * 0.393701);
export const convertToFeet = (meters: number) => Number((meters * 3.28084).toFixed(1));
export const convertToCm = (inches: number) => Math.round(inches / 0.393701);

// Formatting functions for display
export const formatTemperature = (temp: number, unit: 'metric' | 'imperial') => {
  if (unit === 'imperial') {
    return `${convertToFahrenheit(temp)}°F`;
  }
  return `${temp}°C`;
};

export const formatWindSpeed = (speed: number, unit: 'metric' | 'imperial') => {
  if (unit === 'imperial') {
    return `${convertToMph(speed)} mph`;
  }
  return `${speed} km/h`;
};
