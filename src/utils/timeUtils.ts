
import { formatInTimeZone, toZonedTime, fromZonedTime } from 'date-fns-tz';
import { format, parse } from 'date-fns';

/**
 * Converts a Date to UTC
 * @param date The date to convert to UTC
 */
export const toUTC = (date: Date): Date => {
  return new Date(
    Date.UTC(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      date.getHours(),
      date.getMinutes(),
      date.getSeconds(),
      date.getMilliseconds()
    )
  );
};

/**
 * Formats a date in a specific timezone
 * @param date The date to format
 * @param formatStr The format string
 * @param timeZone The timezone to format in (defaults to user's timezone or system timezone)
 */
export const formatDateInTimeZone = (
  date: Date,
  formatStr: string = 'yyyy-MM-dd HH:mm',
  timeZone: string = Intl.DateTimeFormat().resolvedOptions().timeZone
): string => {
  return formatInTimeZone(date, timeZone, formatStr);
};

/**
 * Converts a date from UTC to the user's local timezone
 * @param utcDate The UTC date to convert
 * @param timeZone The timezone to convert to (defaults to user's timezone)
 */
export const utcToLocalTime = (
  utcDate: Date,
  timeZone: string = Intl.DateTimeFormat().resolvedOptions().timeZone
): Date => {
  return toZonedTime(utcDate, timeZone);
};

/**
 * Converts a date from the user's local timezone to UTC
 * @param localDate The local date to convert
 * @param timeZone The timezone to convert from (defaults to user's timezone)
 */
export const localToUTCTime = (
  localDate: Date,
  timeZone: string = Intl.DateTimeFormat().resolvedOptions().timeZone
): Date => {
  return fromZonedTime(localDate, timeZone);
};

/**
 * Formats a time input for display (HH:MM format)
 * @param timeString Time string in HH:MM format
 */
export const formatTimeForDisplay = (timeString: string): string => {
  if (!timeString) return '';
  
  try {
    // Parse the time string (e.g., "14:30") into a Date object
    const timeDate = parse(timeString, 'HH:mm', new Date());
    // Format it according to the user's locale
    return format(timeDate, 'h:mm a');
  } catch (error) {
    console.error('Error formatting time:', error);
    return timeString;
  }
};

/**
 * Formats a datetime-local input value from a Date
 * @param date The date to format
 */
export const formatDateForInput = (date: Date): string => {
  return date.toISOString().slice(0, 16);
};

/**
 * Converts a time string to a Date object in UTC
 * @param timeStr Time string in HH:MM format
 * @param baseDate Optional base date (defaults to today)
 */
export const timeStringToUTCDate = (
  timeStr: string, 
  baseDate: Date = new Date()
): Date => {
  const [hours, minutes] = timeStr.split(':').map(Number);
  
  const date = new Date(baseDate);
  date.setHours(hours, minutes, 0, 0);
  
  return toUTC(date);
};
