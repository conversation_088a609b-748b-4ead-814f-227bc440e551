
import { v7 as uuidv7 } from 'uuid';

// Helper function to generate an ID with a type prefix using UUIDv7
export const generateId = (type: string): string => {
  return `${type}-${uuidv7()}`;
};

// Type-specific ID generators with 3-character prefixes
export const generateWatercraftId = (): string => generateId('wct');
export const generateMaintenanceId = (): string => generateId('mnt');
export const generateJauntId = (): string => generateId('jnt');
export const generateUserId = (): string => generateId('usr');
export const generateTeamId = (): string => generateId('tem');
