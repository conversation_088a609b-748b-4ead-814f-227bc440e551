
import { WeatherData, WeatherUnit } from '@/types/weather';
import { convertToFahrenheit, convertToMph, convertToFeet } from '@/utils/unitConversions';

/**
 * Formats wind direction in degrees to a cardinal direction
 */
export const formatWindDirection = (degrees: number): string => {
  const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW', 'N'];
  const index = Math.round(degrees / 22.5);
  return directions[index];
};

/**
 * Formats a timestamp to a time string (e.g. "11:00 AM")
 */
export const formatTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

/**
 * Formats a temperature value based on the unit system
 */
export const formatTemperature = (temp: number | undefined, unit: WeatherUnit): string => {
  if (temp === undefined) return 'N/A';
  
  if (unit === 'imperial') {
    return `${Math.round(convertToFahrenheit(temp))}°F`;
  }
  return `${Math.round(temp)}°C`;
};

/**
 * Formats a wind speed value based on the unit system
 */
export const formatWindSpeed = (speed: number, unit: WeatherUnit): string => {
  if (unit === 'imperial') {
    return `${Math.round(convertToMph(speed))} mph`;
  }
  return `${Math.round(speed)} km/h`;
};

/**
 * Formats tide level based on the selected unit system
 */
export const formatTideLevel = (level: number | undefined | null, unit: WeatherUnit): string => {
  if (level === undefined || level === null) return 'N/A';
  
  if (unit === 'imperial') {
    return `${convertToFeet(level)} ft`;
  }
  return `${level.toFixed(1)} m`;
};

/**
 * Converts weather data to the selected unit system
 */
export const convertWeatherData = (data: WeatherData, unit: WeatherUnit): WeatherData => {
  if (unit === 'metric') {
    return data;
  }
  
  // Convert to imperial
  return {
    ...data,
    // We actually don't convert here, we just keep the original data
    // The formatting functions will handle the conversions when displaying
  };
};
