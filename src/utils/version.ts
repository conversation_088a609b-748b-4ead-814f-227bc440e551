import versionConfig from '../../version.json';

export interface VersionInfo {
  major: number;
  minor: number;
  version: string;
  gitHash: string;
  fullVersion: string;
  name: string;
  description: string;
  buildDate: string;
}

/**
 * Get the current version information
 * This will be populated at build time with actual git hash and build date
 */
export function getVersionInfo(): VersionInfo {
  // These values will be replaced at build time
  const gitHash = import.meta.env.VITE_GIT_HASH || 'dev';
  const buildDate = import.meta.env.VITE_BUILD_DATE || new Date().toISOString();
  
  const version = `${versionConfig.major}.${versionConfig.minor}`;
  const fullVersion = `${version}-${gitHash}`;
  
  return {
    major: versionConfig.major,
    minor: versionConfig.minor,
    version,
    gitHash,
    fullVersion,
    name: versionConfig.name,
    description: versionConfig.description,
    buildDate,
  };
}

/**
 * Get a short version string for display
 */
export function getShortVersion(): string {
  const info = getVersionInfo();
  return info.fullVersion;
}

/**
 * Get just the semantic version (major.minor)
 */
export function getSemanticVersion(): string {
  return `${versionConfig.major}.${versionConfig.minor}`;
}
