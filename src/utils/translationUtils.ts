
import { translations } from '@/i18n/translations';
import { TranslationKey } from '@/i18n/types';

/**
 * Validates that all languages have the same translation keys.
 * Returns object with information about missing keys.
 */
export function validateTranslationCompleteness(): {
  complete: boolean;
  missingByLanguage: Record<string, TranslationKey[]>;
} {
  const result = {
    complete: true,
    missingByLanguage: {} as Record<string, TranslationKey[]>,
  };

  // Use English as the reference language
  const referenceKeys = Object.keys(translations.en) as TranslationKey[];

  // Check each language against the reference
  Object.entries(translations).forEach(([lang, langTranslations]) => {
    if (lang === 'en') return; // Skip English as it's our reference

    const missingKeys = referenceKeys.filter(
      (key) => !Object.prototype.hasOwnProperty.call(langTranslations, key)
    ) as TranslationKey[];

    if (missingKeys.length > 0) {
      result.complete = false;
      result.missingByLanguage[lang] = missingKeys;
    }
  });

  return result;
}

/**
 * Check if a key exists in all language translations
 */
export function isKeyInAllTranslations(key: TranslationKey): boolean {
  return Object.values(translations).every(
    (langTranslations) => Object.prototype.hasOwnProperty.call(langTranslations, key)
  );
}

/**
 * Get all translation keys that are in at least one language
 */
export function getAllTranslationKeys(): TranslationKey[] {
  const keysSet = new Set<string>();
  
  Object.values(translations).forEach((langTranslations) => {
    Object.keys(langTranslations).forEach((key) => {
      keysSet.add(key);
    });
  });
  
  return Array.from(keysSet) as TranslationKey[];
}
