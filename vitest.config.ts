
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./src/tests/setup.ts'],
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: ['node_modules/', 'src/tests/'],
    },
    deps: {
      inline: ['vitest-mock-extended', '@testing-library/jest-dom'],
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src')
    },
  },
});
