# BoatBook

A comprehensive boat and watercraft management application for rowing clubs and marine organizations.

## Features

- **Watercraft Management**: Track boats, kayaks, and other watercraft
- **Jaunt Scheduling**: Plan and manage water activities and outings
- **Team Management**: Organize teams and member assignments
- **Maintenance Tracking**: Monitor equipment maintenance and repairs
- **Weather Integration**: Real-time weather information for planning
- **Multi-language Support**: Available in multiple languages
- **Responsive Design**: Works on desktop and mobile devices

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Docker (optional, for containerized deployment)

### Local Development

```sh
# Clone the repository
git clone <YOUR_GIT_URL>

# Navigate to the project directory
cd boatbook

# Install dependencies
npm install

# Start the development server
npm run dev
```

The application will be available at `http://localhost:8080`

### Docker Deployment

```sh
# Build and run with Docker Compose
npm run docker:compose

# Or build and run manually
npm run docker:build
npm run docker:run
```

The application will be available at `http://localhost:8080`

## Technology Stack

- **Frontend**: React 18 with TypeScript
- **Build Tool**: Vite
- **UI Components**: shadcn/ui with Radix UI primitives
- **Styling**: Tailwind CSS
- **State Management**: React Query (TanStack Query)
- **Routing**: React Router
- **Testing**: Vitest with Testing Library
- **Deployment**: Docker with Nginx

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run test` - Run tests
- `npm run test:coverage` - Run tests with coverage
- `npm run lint` - Run ESLint
- `npm run docker:build` - Build Docker image
- `npm run docker:run` - Run Docker container
- `npm run docker:compose` - Run with Docker Compose

## Project Structure

```
src/
├── components/     # Reusable UI components
├── pages/         # Page components
├── context/       # React context providers
├── hooks/         # Custom React hooks
├── services/      # API services and data layer
├── types/         # TypeScript type definitions
├── utils/         # Utility functions
└── tests/         # Test files
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and ensure they pass
6. Submit a pull request

## License

This project is private and proprietary.
