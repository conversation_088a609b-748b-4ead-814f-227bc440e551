const request = require('supertest');
const express = require('express');
const weatherRoutes = require('../routes/weather');

// Create test app
const app = express();

// Add simple in-memory cache for testing
app.use((req, res, next) => {
  req.cache = {
    get: () => null,
    set: () => {},
  };
  next();
});

app.use('/api/weather', weatherRoutes);

describe('Weather Station Search API', () => {
  describe('GET /api/weather/stations/search', () => {
    it('should return empty array for queries less than 2 characters', async () => {
      const response = await request(app)
        .get('/api/weather/stations/search?q=a')
        .expect(200);

      expect(response.body).toEqual([]);
    });

    it('should return empty array for missing query parameter', async () => {
      const response = await request(app)
        .get('/api/weather/stations/search')
        .expect(200);

      expect(response.body).toEqual([]);
    });

    it('should find Redwood City station by city name', async () => {
      const response = await request(app)
        .get('/api/weather/stations/search?q=redwood%20city')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      
      const redwoodCity = response.body.find(station => station.id === '9414523');
      expect(redwoodCity).toBeDefined();
      expect(redwoodCity.name).toBe('Redwood City, CA');
      expect(redwoodCity.lat).toBe(37.5067);
      expect(redwoodCity.lon).toBe(-122.21);
      expect(redwoodCity.state).toBe('CA');
    });

    it('should find Redwood City station by city name with state', async () => {
      const response = await request(app)
        .get('/api/weather/stations/search?q=redwood%20city,%20ca')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      
      const redwoodCity = response.body.find(station => station.id === '9414523');
      expect(redwoodCity).toBeDefined();
      expect(redwoodCity.name).toBe('Redwood City, CA');
    });

    it('should find stations by partial city name', async () => {
      const response = await request(app)
        .get('/api/weather/stations/search?q=san+francisco')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);

      // Should find San Francisco station
      const sfStation = response.body.find(station =>
        station.name.toLowerCase().includes('san francisco')
      );
      expect(sfStation).toBeDefined();
      expect(sfStation.id).toBe('9414290');
    });

    it('should find station by ID', async () => {
      const response = await request(app)
        .get('/api/weather/stations/search?q=9414523')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      
      const station = response.body.find(s => s.id === '9414523');
      expect(station).toBeDefined();
      expect(station.name).toBe('Redwood City, CA');
    });

    it('should handle case-insensitive searches', async () => {
      const response = await request(app)
        .get('/api/weather/stations/search?q=REDWOOD%20CITY')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      
      const redwoodCity = response.body.find(station => station.id === '9414523');
      expect(redwoodCity).toBeDefined();
    });

    it('should find San Francisco stations', async () => {
      const response = await request(app)
        .get('/api/weather/stations/search?q=san%20francisco')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      
      const sanFrancisco = response.body.find(station => station.id === '9414290');
      expect(sanFrancisco).toBeDefined();
      expect(sanFrancisco.name).toBe('San Francisco, CA');
    });

    it('should find multiple stations for broad searches', async () => {
      const response = await request(app)
        .get('/api/weather/stations/search?q=san')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(1);
      
      // Should include San Francisco and San Diego
      const sanFrancisco = response.body.find(s => s.name.includes('San Francisco'));
      const sanDiego = response.body.find(s => s.name.includes('San Diego'));
      
      expect(sanFrancisco).toBeDefined();
      expect(sanDiego).toBeDefined();
    });

    it('should return valid station objects', async () => {
      const response = await request(app)
        .get('/api/weather/stations/search?q=redwood%20city')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      
      response.body.forEach(station => {
        expect(station).toHaveProperty('id');
        expect(station).toHaveProperty('name');
        expect(station).toHaveProperty('lat');
        expect(station).toHaveProperty('lon');
        expect(typeof station.lat).toBe('number');
        expect(typeof station.lon).toBe('number');
        expect(station.lat).toBeGreaterThan(-90);
        expect(station.lat).toBeLessThan(90);
        expect(station.lon).toBeGreaterThan(-180);
        expect(station.lon).toBeLessThan(180);
      });
    });

    it('should handle URL encoded queries', async () => {
      const response = await request(app)
        .get('/api/weather/stations/search?q=redwood%20city%2C%20ca')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      
      const redwoodCity = response.body.find(station => station.id === '9414523');
      expect(redwoodCity).toBeDefined();
    });

    it('should limit results to reasonable number', async () => {
      const response = await request(app)
        .get('/api/weather/stations/search?q=ca')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeLessThanOrEqual(15);
    });

    it('should return empty array for non-existent locations', async () => {
      const response = await request(app)
        .get('/api/weather/stations/search?q=nonexistentcity')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      // May return empty array or very few results
    });
  });

  describe('GET /api/weather/stations/nearby', () => {
    it('should find stations near San Francisco', async () => {
      const response = await request(app)
        .get('/api/weather/stations/nearby?lat=37.8063&lon=-122.4659&radius=50')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      
      // Should include San Francisco station
      const sanFrancisco = response.body.find(station => station.id === '9414290');
      expect(sanFrancisco).toBeDefined();
      expect(sanFrancisco.distance).toBeDefined();
      expect(typeof sanFrancisco.distance).toBe('number');
    });

    it('should require lat and lon parameters', async () => {
      const response = await request(app)
        .get('/api/weather/stations/nearby')
        .expect(400);

      expect(response.body).toHaveProperty('error');
    });

    it('should handle invalid coordinates', async () => {
      const response = await request(app)
        .get('/api/weather/stations/nearby?lat=invalid&lon=invalid')
        .expect(400);

      expect(response.body).toHaveProperty('error');
    });

    it('should sort results by distance', async () => {
      const response = await request(app)
        .get('/api/weather/stations/nearby?lat=37.8063&lon=-122.4659&radius=100')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      
      if (response.body.length > 1) {
        for (let i = 1; i < response.body.length; i++) {
          const prev = response.body[i - 1];
          const curr = response.body[i];
          
          if (prev.distance !== undefined && curr.distance !== undefined) {
            expect(prev.distance).toBeLessThanOrEqual(curr.distance);
          }
        }
      }
    });
  });
});
