# BoatBook Deployment Guide

## Quick Start with Docker

### Prerequisites
- <PERSON><PERSON> and Docker Compose installed
- Git (for cloning the repository)

### Option 1: Docker Compose (Recommended)
```bash
# Clone the repository
git clone <your-repo-url>
cd boatbook

# Build and run with Docker Compose
npm run docker:compose
```

The application will be available at `http://localhost:8080`

### Option 2: Manual Docker Build
```bash
# Build the Docker image
npm run docker:build

# Run the container
npm run docker:run
```

### Option 3: Local Development
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

## Production Deployment

### Environment Variables
The application supports the following environment variables:

- `NODE_ENV`: Set to `production` for production builds
- `VITE_APP_TITLE`: Custom application title

### Docker Production Deployment
```bash
# Build production image
docker build -t boatbook:latest .

# Run with custom environment
docker run -d \
  -p 80:80 \
  -e NODE_ENV=production \
  --name boatbook-prod \
  boatbook:latest
```

### Docker Compose Production
```yaml
version: '3.8'
services:
  boatbook:
    build: .
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
```

## Testing

```bash
# Run tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run linting
npm run lint
```

## Troubleshooting

### Build Issues
- If you encounter dependency conflicts, try deleting `node_modules` and `package-lock.json`, then run `npm install`
- For Docker builds, ensure you have enough disk space and memory allocated to Docker

### Runtime Issues
- Check container logs: `docker logs <container-name>`
- Verify port availability: `netstat -an | grep 8080`

### Performance Optimization
- The build includes automatic code splitting and chunk optimization
- Static assets are cached with appropriate headers
- Consider using a CDN for production deployments
